'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Phone, 
  Mail, 
  MapPin, 
  Clock,
  Send,
  CheckCircle
} from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input, Textarea } from '@/components/ui/Input';

const contactInfo = [
  {
    icon: Phone,
    title: 'Phone',
    details: '(*************',
    description: 'Call us for immediate assistance'
  },
  {
    icon: Mail,
    title: 'Email',
    details: '<EMAIL>',
    description: 'Send us a message anytime'
  },
  {
    icon: MapPin,
    title: 'Office',
    details: '123 Business Ave, Suite 100',
    description: 'Visit us for a consultation'
  },
  {
    icon: Clock,
    title: 'Hours',
    details: 'Mon-Fri: 9AM-6PM',
    description: 'We respond within 24 hours'
  }
];

const services = [
  'Web Development',
  'App Development', 
  'Lead Generation',
  'SEO Services',
  'Social Media Marketing',
  'PPC Advertising',
  'Reputation Management'
];

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    service: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="lg">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="heading-1 text-gray-900 mb-6">
              Get Your Free{' '}
              <span className="gradient-text">Strategy Session</span>
            </h1>
            <p className="text-large text-gray-600">
              Ready to transform your digital presence? Let's discuss how we can help your 
              local service business generate more leads and grow online.
            </p>
          </div>
        </Container>
      </Section>

      {/* Contact Form & Info */}
      <Section background="white" padding="xl">
        <Container>
          <div className="grid lg:grid-cols-3 gap-12">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <Card>
                  <CardContent className="space-y-8">
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900 mb-2">
                        Send Us a Message
                      </h2>
                      <p className="text-gray-600">
                        Fill out the form below and we'll get back to you within 24 hours.
                      </p>
                    </div>

                    {isSubmitted ? (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="text-center py-8"
                      >
                        <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                        <h3 className="text-xl font-bold text-gray-900 mb-2">
                          Message Sent Successfully!
                        </h3>
                        <p className="text-gray-600">
                          Thank you for reaching out. We'll be in touch within 24 hours.
                        </p>
                      </motion.div>
                    ) : (
                      <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="grid md:grid-cols-2 gap-6">
                          <Input
                            label="Full Name *"
                            name="name"
                            value={formData.name}
                            onChange={handleChange}
                            required
                            placeholder="John Smith"
                          />
                          <Input
                            label="Email Address *"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleChange}
                            required
                            placeholder="<EMAIL>"
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 xs:gap-6">
                          <Input
                            label="Phone Number"
                            name="phone"
                            type="tel"
                            value={formData.phone}
                            onChange={handleChange}
                            placeholder="(*************"
                          />
                          <Input
                            label="Company Name"
                            name="company"
                            value={formData.company}
                            onChange={handleChange}
                            placeholder="Your Business Name"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Service Interest
                          </label>
                          <select
                            name="service"
                            value={formData.service}
                            onChange={handleChange}
                            className="w-full px-3 xs:px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all duration-200 text-sm xs:text-base touch-target"
                          >
                            <option value="">Select a service</option>
                            {services.map((service) => (
                              <option key={service} value={service}>
                                {service}
                              </option>
                            ))}
                          </select>
                        </div>

                        <Textarea
                          label="Message *"
                          name="message"
                          value={formData.message}
                          onChange={handleChange}
                          required
                          rows={5}
                          placeholder="Tell us about your business and how we can help..."
                        />

                        <Button
                          type="submit"
                          variant="primary"
                          size="lg"
                          className="w-full"
                          isLoading={isSubmitting}
                        >
                          {isSubmitting ? 'Sending...' : 'Send Message'}
                          {!isSubmitting && <Send className="ml-2 h-5 w-5" />}
                        </Button>
                      </form>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                <h3 className="text-xl font-bold text-gray-900 mb-6">
                  Get in Touch
                </h3>
                <div className="space-y-6">
                  {contactInfo.map((info, index) => {
                    const Icon = info.icon;
                    return (
                      <motion.div
                        key={info.title}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                        className="flex items-start space-x-4"
                      >
                        <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Icon className="h-6 w-6" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">
                            {info.title}
                          </h4>
                          <p className="text-gray-900 font-medium mb-1">
                            {info.details}
                          </p>
                          <p className="text-gray-600 text-sm">
                            {info.description}
                          </p>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </motion.div>

              {/* Quick Benefits */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <Card className="bg-gradient-to-br from-blue-50 to-green-50">
                  <CardContent className="space-y-4">
                    <h4 className="font-bold text-gray-900">
                      What You'll Get:
                    </h4>
                    <ul className="space-y-3">
                      {[
                        'Free 30-minute consultation',
                        'Custom digital marketing strategy',
                        'Competitive analysis report',
                        'No obligation or pressure'
                      ].map((benefit) => (
                        <li key={benefit} className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-green-600 mr-3 flex-shrink-0" />
                          <span className="text-gray-700 text-sm">{benefit}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </div>
        </Container>
      </Section>

      {/* FAQ Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="heading-2 text-gray-900 mb-6">
              Frequently Asked Questions
            </h2>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {[
              {
                question: 'How quickly can you start working on my project?',
                answer: 'We can typically start within 1-2 weeks of our initial consultation, depending on project scope and current workload.'
              },
              {
                question: 'Do you work with businesses outside your target industries?',
                answer: 'While we specialize in landscaping, roofing, and pest control, we do work with other local service businesses on a case-by-case basis.'
              },
              {
                question: 'What makes you different from other marketing agencies?',
                answer: 'Our deep specialization in local service industries means we understand your unique challenges and have proven strategies that work.'
              },
              {
                question: 'Do you offer month-to-month contracts?',
                answer: 'Yes, we offer flexible contract terms. While we recommend longer commitments for best results, we have month-to-month options available.'
              }
            ].map((faq, index) => (
              <motion.div
                key={faq.question}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card>
                  <CardContent className="space-y-3">
                    <h4 className="font-semibold text-gray-900">
                      {faq.question}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {faq.answer}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </Container>
      </Section>
    </>
  );
}
