import React from 'react';
import { clsx } from 'clsx';

interface GlassCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'subtle' | 'strong';
  hover?: boolean;
  padding?: 'sm' | 'md' | 'lg' | 'xl';
}

export const GlassCard: React.FC<GlassCardProps> = ({
  children,
  className,
  variant = 'default',
  hover = true,
  padding = 'lg'
}) => {
  const baseClasses = 'relative backdrop-blur-md border border-white/20 rounded-xl shadow-lg';
  
  const variantClasses = {
    default: 'bg-white/10',
    subtle: 'bg-white/5',
    strong: 'bg-white/20'
  };

  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-12'
  };

  const hoverClasses = hover 
    ? 'transition-all duration-300 hover:bg-white/15 hover:shadow-xl hover:scale-[1.02]' 
    : '';

  return (
    <div className={clsx(
      baseClasses,
      variantClasses[variant],
      paddingClasses[padding],
      hoverClasses,
      className
    )}>
      {children}
    </div>
  );
};

interface GradientCardProps {
  children: React.ReactNode;
  className?: string;
  gradient?: 'blue' | 'green' | 'purple' | 'orange';
  direction?: 'to-r' | 'to-br' | 'to-b' | 'to-bl';
}

export const GradientCard: React.FC<GradientCardProps> = ({
  children,
  className,
  gradient = 'blue',
  direction = 'to-br'
}) => {
  const gradientClasses = {
    blue: `bg-gradient-${direction} from-blue-500/10 via-blue-400/5 to-cyan-500/10`,
    green: `bg-gradient-${direction} from-green-500/10 via-emerald-400/5 to-teal-500/10`,
    purple: `bg-gradient-${direction} from-purple-500/10 via-violet-400/5 to-indigo-500/10`,
    orange: `bg-gradient-${direction} from-orange-500/10 via-amber-400/5 to-yellow-500/10`
  };

  return (
    <div className={clsx(
      'relative backdrop-blur-sm border border-white/10 rounded-xl shadow-lg p-8',
      'transition-all duration-300 hover:shadow-xl hover:scale-[1.02]',
      gradientClasses[gradient],
      className
    )}>
      {children}
    </div>
  );
};

interface NeumorphismCardProps {
  children: React.ReactNode;
  className?: string;
  inset?: boolean;
}

export const NeumorphismCard: React.FC<NeumorphismCardProps> = ({
  children,
  className,
  inset = false
}) => {
  const shadowClasses = inset
    ? 'shadow-inner shadow-gray-300/50'
    : 'shadow-lg shadow-gray-300/30';

  return (
    <div className={clsx(
      'bg-gray-50 rounded-xl p-8 border border-gray-200/50',
      'transition-all duration-300 hover:shadow-xl',
      shadowClasses,
      className
    )}>
      {children}
    </div>
  );
};

interface FloatingCardProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
}

export const FloatingCard: React.FC<FloatingCardProps> = ({
  children,
  className,
  delay = 0
}) => {
  return (
    <div 
      className={clsx(
        'animate-float',
        className
      )}
      style={{
        animationDelay: `${delay}s`
      }}
    >
      {children}
    </div>
  );
};

// Add floating animation to globals.css
export const floatingKeyframes = `
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}
`;
