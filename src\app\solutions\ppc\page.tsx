import { Metadata } from 'next';
import Link from 'next/link';
import { <PERSON>R<PERSON>, MousePointer, Target, TrendingUp, DollarSign, Star, CheckCircle, Phone, Zap } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';


export const metadata: Metadata = {
  title: 'PPC Advertising for Landscaping Businesses | GroundUP Digital',
  description: 'Get immediate results with targeted Google Ads and Local Service Ads for your landscaping business. Attract high-intent customers ready to hire.',
  keywords: 'landscaping PPC, Google Ads for landscapers, Local Service Ads landscaping, lawn care advertising, landscape contractor ads',
};

const ppcFeatures = [
  {
    icon: Target,
    title: 'High-Intent Customer Targeting',
    description: 'Reach customers actively searching for landscaping services in your area.',
    benefits: ['Location-based targeting', 'Service-specific keywords', 'Seasonal campaign optimization', 'Competitor analysis']
  },
  {
    icon: Zap,
    title: 'Immediate Results',
    description: 'Start generating leads for your landscaping business within 24-48 hours.',
    benefits: ['Fast campaign setup', 'Quick lead generation', 'Real-time optimization', 'Instant visibility boost']
  },
  {
    icon: DollarSign,
    title: 'Proven ROI',
    description: 'Our landscaping clients see average returns of $4-6 for every $1 spent on ads.',
    benefits: ['Cost-effective campaigns', 'ROI tracking', 'Budget optimization', 'Performance reporting']
  }
];

const adTypes = [
  {
    name: 'Google Local Service Ads',
    description: 'Appear at the top of Google when customers search for landscaping services',
    features: ['Google Guaranteed badge', 'Pay per lead, not click', 'Background check verification', 'Customer reviews display']
  },
  {
    name: 'Google Search Ads',
    description: 'Target customers searching for specific landscaping services',
    features: ['Keyword targeting', 'Ad extensions', 'Location targeting', 'Call tracking']
  },
  {
    name: 'Display Advertising',
    description: 'Build brand awareness across Google\'s network of websites',
    features: ['Visual ad formats', 'Remarketing campaigns', 'Audience targeting', 'Brand awareness']
  },
  {
    name: 'YouTube Advertising',
    description: 'Showcase your landscaping work with video ads',
    features: ['Video showcases', 'Before/after content', 'Local targeting', 'Engagement tracking']
  }
];

const packages = [
  {
    name: "Lead Starter",
    adSpend: "$1,000",
    managementFee: "$497",
    period: "/month",
    description: "Perfect for small landscaping businesses starting with paid advertising",
    features: [
      "Google Local Service Ads setup",
      "Basic Google Search campaigns",
      "Location targeting optimization",
      "Monthly performance reports",
      "Phone call tracking"
    ],
    popular: false
  },
  {
    name: "Growth Accelerator",
    adSpend: "$2,500",
    managementFee: "$797",
    period: "/month", 
    description: "Ideal for growing landscaping companies ready to scale their lead generation",
    features: [
      "Multi-platform campaigns",
      "Advanced keyword targeting",
      "Conversion tracking setup",
      "A/B testing optimization",
      "Dedicated account manager",
      "Bi-weekly optimization calls"
    ],
    popular: true
  },
  {
    name: "Market Domination",
    adSpend: "$5,000+",
    managementFee: "$1,297",
    period: "/month",
    description: "For established landscaping businesses ready to dominate their market",
    features: [
      "Full-funnel advertising strategy",
      "Video advertising campaigns",
      "Advanced audience targeting",
      "Competitive intelligence",
      "Custom landing pages",
      "Weekly strategy sessions"
    ],
    popular: false
  }
];

const caseStudy = {
  company: "Evergreen Landscaping Co.",
  location: "Phoenix, AZ",
  results: [
    { metric: "Cost Per Lead", improvement: "-45%" },
    { metric: "Lead Volume", improvement: "+280%" },
    { metric: "Conversion Rate", improvement: "+65%" },
    { metric: "Return on Ad Spend", improvement: "520%" }
  ]
};

export default function PPCPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <MousePointer className="w-4 h-4" />
              <span className="text-sm font-semibold">PPC Advertising for Landscaping Businesses</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Get Immediate Leads for Your{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscaping Business
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Targeted Google Ads and Local Service Ads that connect your landscaping business with customers 
              ready to hire. Start generating qualified leads within 24-48 hours.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <a href="/partnership" className="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-white text-blue-600 hover:bg-gray-50 shadow-lg hover:shadow-xl px-6 xs:px-8 py-3 xs:py-4 text-base xs:text-lg touch-target-lg">
                <Phone className="w-5 h-5 mr-2" />
                Get Free Ad Account Audit
              </a>
              <a href="/client-success" className="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 border-2 border-white/30 text-white hover:bg-white/10 px-6 xs:px-8 py-3 xs:py-4 text-base xs:text-lg touch-target-lg">
                View Campaign Examples
                <ArrowRight className="w-5 h-5 ml-2" />
              </a>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '24-48hrs', label: 'Time to First Lead' },
                { number: '4.8x', label: 'Average ROI' },
                { number: '180+', label: 'Campaigns Managed' },
                { number: '95%', label: 'Client Retention' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Why Landscaping Businesses Choose Our PPC Services
            </h2>
            <p className="text-lg text-gray-600">
              We specialize in creating high-converting ad campaigns specifically for landscaping business owners. 
              Our strategies are proven to generate quality leads and maximize your advertising investment.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {ppcFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Ad Types Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Advertising Solutions for Every Landscaping Business Goal
            </h2>
            <p className="text-lg text-gray-600">
              We create comprehensive advertising strategies using multiple platforms to maximize your reach and results.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {adTypes.map((adType, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{adType.name}</h3>
                  <p className="text-gray-600 mb-4">{adType.description}</p>
                  <ul className="space-y-2">
                    {adType.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Real Results for Landscaping Business Owners
              </h2>
              <p className="text-lg text-gray-600">
                See how our PPC campaigns helped {caseStudy.company} dramatically increase their leads and revenue
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A mid-sized landscaping business in {caseStudy.location} struggling with inconsistent lead flow. 
                      Our targeted PPC campaigns helped them achieve predictable growth and scale their operations.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Read Full Case Study
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              PPC Management Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Professional PPC management designed specifically for landscaping business owners. 
              All packages include dedicated support and proven strategies for your industry.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-sm text-gray-600 mb-2">Ad Spend: {pkg.adSpend}{pkg.period}</div>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.managementFee}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <div className="text-xs text-gray-500 mb-2">Management Fee</div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* FAQ Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Frequently Asked Questions About PPC for Landscaping Businesses
              </h2>
              <p className="text-lg text-gray-600">
                Common questions landscaping business owners have about Google Ads and pay-per-click advertising.
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "How quickly can PPC generate leads for my landscaping business?",
                  answer: "PPC campaigns can start generating leads within 24-48 hours of launch. Unlike SEO which takes months, Google Ads puts your landscaping business in front of customers actively searching for your services immediately."
                },
                {
                  question: "What's a good budget for landscaping PPC campaigns?",
                  answer: "Most successful landscaping businesses start with $1,500-$3,000/month for PPC. This allows for comprehensive coverage of high-intent keywords like 'landscape design near me' and seasonal services across your service area."
                },
                {
                  question: "How do you target the right customers for landscaping services?",
                  answer: "We use advanced targeting including location-based ads, demographic targeting, seasonal keyword optimization, and negative keywords to filter out DIY searchers, ensuring your ads reach property owners ready to hire professional landscapers."
                },
                {
                  question: "Can PPC work for seasonal landscaping services?",
                  answer: "Absolutely! PPC is perfect for seasonal landscaping services. We create seasonal campaigns for spring cleanup, summer maintenance, fall leaf removal, and winter services, adjusting budgets and keywords based on seasonal demand patterns."
                },
                {
                  question: "How do you measure PPC success for landscaping businesses?",
                  answer: "We track lead quality, cost per lead, conversion rates, and most importantly - actual jobs booked and revenue generated. Our landscaping PPC campaigns typically achieve 15-25% conversion rates with strong ROI tracking."
                }
              ].map((faq, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{faq.question}</h3>
                  <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Start Generating Leads for Your Landscaping Business?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a free PPC audit and discover exactly how to attract high-intent customers ready to hire your landscaping services.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="/partnership" className="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-white text-blue-600 hover:bg-gray-50 shadow-lg hover:shadow-xl px-6 xs:px-8 py-3 xs:py-4 text-base xs:text-lg touch-target-lg">
                <Phone className="w-5 h-5 mr-2" />
                Get Free PPC Audit
              </a>
              <a href="/partnership" className="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 border-2 border-white/30 text-white hover:bg-white/10 px-6 xs:px-8 py-3 xs:py-4 text-base xs:text-lg touch-target-lg">
                Schedule Consultation
                <ArrowRight className="w-5 h-5 ml-2" />
              </a>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
