@tailwind base;
@tailwind components;
@tailwind utilities;

/* Fixed UI Elements Z-Index Management */
.fixed-social-icons {
  z-index: 9998;
}

.fixed-scroll-to-top {
  z-index: 9998;
}

.floating-cta {
  z-index: 9999;
}

.mega-menu {
  z-index: 9997;
}

/* Custom scrollbar for mega menu */
.mega-menu .overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.mega-menu .overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.mega-menu .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.mega-menu .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Premium Landscaping Brand Styling */
.landscaping-gradient {
  background: linear-gradient(135deg, #059669 0%, #2563eb 100%);
}

.landscaping-text-gradient {
  background: linear-gradient(135deg, #059669 0%, #2563eb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.premium-shadow {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.landscaping-pattern {
  background-image: radial-gradient(circle at 1px 1px, rgba(5, 150, 105, 0.15) 1px, transparent 0);
  background-size: 20px 20px;
}

/* Custom animations for social icons */
@keyframes socialPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.social-pulse {
  animation: socialPulse 2s infinite;
}

/* WhatsApp notification animation */
@keyframes whatsappBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.whatsapp-bounce {
  animation: whatsappBounce 2s infinite;
}

/* Scroll to top button glow effect */
@keyframes scrollGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.8);
  }
}

.scroll-glow {
  animation: scrollGlow 3s infinite;
}

@layer base {
  :root {
    --font-inter: 'Inter', sans-serif;
  }

  * {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    height: 100%;
  }

  body {
    font-family: var(--font-inter), system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    height: 100%;
  }

  #__next {
    height: 100%;
  }
}

/* Modern Animations and Effects */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-slideInLeft {
  animation: slideInLeft 0.6s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInRight 0.6s ease-out forwards;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* Glassmorphism Effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-blue {
  background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-green {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
}

@layer components {
  /* Focus States for Accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  /* Loading States */
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }

  .skeleton-text {
    @apply skeleton h-4 w-full mb-2;
  }

  .skeleton-title {
    @apply skeleton h-6 w-3/4 mb-4;
  }

  /* Hover Effects */
  .hover-lift {
    @apply transition-transform duration-300 hover:scale-105 hover:shadow-lg;
  }

  .hover-glow {
    @apply transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25;
  }

  /* Enhanced Responsive Typography */
  .text-responsive-xs {
    @apply text-xs xs:text-sm sm:text-base;
  }

  .text-responsive-sm {
    @apply text-sm xs:text-base sm:text-lg;
  }

  .text-responsive-base {
    @apply text-base xs:text-lg sm:text-xl;
  }

  .text-responsive-lg {
    @apply text-lg xs:text-xl sm:text-2xl md:text-3xl;
  }

  .text-responsive-xl {
    @apply text-xl xs:text-2xl sm:text-3xl md:text-4xl lg:text-5xl;
  }

  .text-responsive-2xl {
    @apply text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl;
  }

  .text-responsive-3xl {
    @apply text-3xl xs:text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl;
  }

  /* Responsive Spacing */
  .spacing-responsive-sm {
    @apply p-2 xs:p-3 sm:p-4 md:p-6;
  }

  .spacing-responsive-md {
    @apply p-4 xs:p-6 sm:p-8 md:p-12;
  }

  .spacing-responsive-lg {
    @apply p-6 xs:p-8 sm:p-12 md:p-16 lg:p-20;
  }

  .spacing-responsive-xl {
    @apply p-8 xs:p-12 sm:p-16 md:p-20 lg:p-24 xl:p-32;
  }

  /* Responsive Margins */
  .margin-responsive-sm {
    @apply m-2 xs:m-3 sm:m-4 md:m-6;
  }

  .margin-responsive-md {
    @apply m-4 xs:m-6 sm:m-8 md:m-12;
  }

  .margin-responsive-lg {
    @apply m-6 xs:m-8 sm:m-12 md:m-16 lg:m-20;
  }

  /* Responsive Gaps */
  .gap-responsive-sm {
    @apply gap-2 xs:gap-3 sm:gap-4 md:gap-6;
  }

  .gap-responsive-md {
    @apply gap-4 xs:gap-6 sm:gap-8 md:gap-12;
  }

  .gap-responsive-lg {
    @apply gap-6 xs:gap-8 sm:gap-12 md:gap-16 lg:gap-20;
  }
}

@layer components {
  /* Enhanced Container with better responsive padding */
  .container {
    @apply max-w-7xl mx-auto px-3 xs:px-4 sm:px-6 lg:px-8 xl:px-12;
  }

  .container-sm {
    @apply max-w-3xl mx-auto px-3 xs:px-4 sm:px-6 lg:px-8;
  }

  .container-md {
    @apply max-w-5xl mx-auto px-3 xs:px-4 sm:px-6 lg:px-8;
  }

  .container-lg {
    @apply max-w-7xl mx-auto px-3 xs:px-4 sm:px-6 lg:px-8 xl:px-12;
  }

  .container-xl {
    @apply max-w-8xl mx-auto px-3 xs:px-4 sm:px-6 lg:px-8 xl:px-12;
  }

  .container-full {
    @apply max-w-full mx-auto px-3 xs:px-4 sm:px-6 lg:px-8 xl:px-12;
  }

  /* Enhanced Section Padding */
  .section-padding {
    @apply py-8 xs:py-12 sm:py-16 md:py-20 lg:py-24 xl:py-32;
  }

  .section-padding-sm {
    @apply py-4 xs:py-6 sm:py-8 md:py-12;
  }

  .section-padding-md {
    @apply py-6 xs:py-8 sm:py-12 md:py-16 lg:py-20;
  }

  .section-padding-lg {
    @apply py-8 xs:py-12 sm:py-16 md:py-20 lg:py-24 xl:py-32;
  }

  .section-padding-xl {
    @apply py-12 xs:py-16 sm:py-20 md:py-24 lg:py-32 xl:py-40;
  }

  /* Enhanced Responsive Headings */
  .heading-1 {
    @apply text-2xl xs:text-3xl sm:text-4xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight;
  }

  .heading-2 {
    @apply text-xl xs:text-2xl sm:text-3xl md:text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight;
  }

  .heading-3 {
    @apply text-lg xs:text-xl sm:text-2xl md:text-2xl lg:text-3xl xl:text-4xl font-bold leading-tight;
  }

  .heading-4 {
    @apply text-base xs:text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold leading-tight;
  }

  .heading-5 {
    @apply text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl font-bold leading-tight;
  }

  .heading-6 {
    @apply text-xs xs:text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl font-bold leading-tight;
  }

  /* Enhanced Text Sizes */
  .text-large {
    @apply text-base xs:text-lg sm:text-xl md:text-2xl leading-relaxed;
  }

  .text-body {
    @apply text-sm xs:text-base sm:text-lg md:text-xl leading-relaxed;
  }

  .text-small {
    @apply text-xs xs:text-sm sm:text-base leading-relaxed;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-blue-50 via-white to-green-50;
  }

  .card-shadow {
    @apply shadow-lg hover:shadow-xl transition-shadow duration-300;
  }

  .btn-primary {
    @apply bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors duration-200 shadow-lg hover:shadow-xl;
  }

  .btn-outline {
    @apply border-2 border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-blue-600 hover:text-white transition-all duration-200;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-400 {
    animation-delay: 400ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }

  /* Touch-friendly utilities */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  .touch-target-lg {
    @apply min-h-[48px] min-w-[48px];
  }

  /* Mobile-first responsive utilities */
  .mobile-hidden {
    @apply hidden sm:block;
  }

  .mobile-only {
    @apply block sm:hidden;
  }

  .tablet-hidden {
    @apply hidden md:block;
  }

  .tablet-only {
    @apply hidden sm:block md:hidden;
  }

  .desktop-hidden {
    @apply hidden lg:block;
  }

  .desktop-only {
    @apply hidden lg:block;
  }

  /* Responsive flex utilities */
  .flex-responsive {
    @apply flex flex-col sm:flex-row;
  }

  .flex-responsive-reverse {
    @apply flex flex-col-reverse sm:flex-row;
  }

  /* Responsive grid utilities */
  .grid-responsive-1 {
    @apply grid grid-cols-1;
  }

  .grid-responsive-2 {
    @apply grid grid-cols-1 sm:grid-cols-2;
  }

  .grid-responsive-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .grid-responsive-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .grid-responsive-auto {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-auto;
  }

  /* Responsive aspect ratios */
  .aspect-responsive-square {
    @apply aspect-square;
  }

  .aspect-responsive-video {
    @apply aspect-video;
  }

  .aspect-responsive-photo {
    @apply aspect-[4/3] sm:aspect-[3/2] lg:aspect-[16/9];
  }

  /* Safe area utilities for mobile devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Responsive overflow handling */
  .overflow-responsive {
    @apply overflow-hidden sm:overflow-visible;
  }

  .overflow-x-responsive {
    @apply overflow-x-auto sm:overflow-x-visible;
  }

  .overflow-y-responsive {
    @apply overflow-y-auto sm:overflow-y-visible;
  }
}
