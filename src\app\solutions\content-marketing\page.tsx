import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, PenTool, Users, TrendingUp, Calendar, Star, CheckCircle, Phone, FileText } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Content Marketing for Landscaping Businesses | GroundUP Digital',
  description: 'Strategic content marketing that positions your landscaping business as the local authority. Build trust, educate customers, and drive more sales.',
  keywords: 'landscaping content marketing, landscaping blog writing, lawn care content strategy, landscape business authority building',
};

const contentFeatures = [
  {
    icon: PenTool,
    title: 'Industry Authority Building',
    description: 'Position your landscaping business as the trusted local expert through valuable content.',
    benefits: ['Educational blog posts', 'Seasonal landscaping guides', 'Project showcases', 'Expert tips & advice']
  },
  {
    icon: Users,
    title: 'Customer Education',
    description: 'Help potential customers understand your services and make informed decisions.',
    benefits: ['Service explanations', 'Before/after case studies', 'Maintenance guides', 'Design inspiration']
  },
  {
    icon: TrendingUp,
    title: 'Long-term Growth',
    description: 'Build sustainable organic traffic and customer relationships that compound over time.',
    benefits: ['SEO-optimized content', 'Social media amplification', 'Email nurturing', 'Brand awareness']
  }
];

const contentTypes = [
  {
    name: 'Educational Blog Posts',
    description: 'In-depth articles that educate customers about landscaping topics',
    examples: ['Seasonal landscaping tips', 'Plant care guides', 'Design trends', 'Maintenance schedules']
  },
  {
    name: 'Project Showcases',
    description: 'Detailed case studies of your landscaping projects',
    examples: ['Before/after transformations', 'Design process walkthroughs', 'Client testimonials', 'Problem-solving stories']
  },
  {
    name: 'Video Content',
    description: 'Engaging visual content that showcases your expertise',
    examples: ['Time-lapse installations', 'How-to tutorials', 'Property walkthroughs', 'Team introductions']
  },
  {
    name: 'Social Media Content',
    description: 'Consistent posting that builds your online community',
    examples: ['Daily project photos', 'Quick tips', 'Behind-the-scenes', 'Customer features']
  }
];

const packages = [
  {
    name: "Content Foundation",
    price: "$997",
    period: "/month",
    description: "Essential content marketing for landscaping businesses starting their authority building journey",
    features: [
      "4 blog posts per month",
      "Social media content calendar",
      "Basic SEO optimization",
      "Monthly performance reports",
      "Content strategy consultation"
    ],
    popular: false
  },
  {
    name: "Authority Builder",
    price: "$1,997",
    period: "/month", 
    description: "Comprehensive content strategy for landscaping businesses ready to dominate their market",
    features: [
      "8 blog posts per month",
      "Video content creation",
      "Advanced SEO optimization",
      "Email newsletter campaigns",
      "Social media management",
      "Quarterly strategy reviews"
    ],
    popular: true
  },
  {
    name: "Market Leader",
    price: "$2,997",
    period: "/month",
    description: "Premium content marketing for established landscaping businesses seeking market leadership",
    features: [
      "12+ pieces of content monthly",
      "Custom video production",
      "Thought leadership articles",
      "Industry publication outreach",
      "Speaking opportunity coordination",
      "Dedicated content manager"
    ],
    popular: false
  }
];

const caseStudy = {
  company: "Signature Landscapes",
  location: "Seattle, WA",
  results: [
    { metric: "Organic Traffic", improvement: "+420%" },
    { metric: "Blog Engagement", improvement: "+340%" },
    { metric: "Social Followers", improvement: "+280%" },
    { metric: "Content-Driven Leads", improvement: "+195%" }
  ]
};

export default function ContentMarketingPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <PenTool className="w-4 h-4" />
              <span className="text-sm font-semibold">Content Marketing for Landscaping Businesses</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Build Authority & Trust for Your{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscaping Business
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Strategic content marketing that positions your landscaping business as the local authority. 
              Educate customers, build trust, and drive more sales through valuable content.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <FileText className="w-5 h-5 mr-2" />
                View Content Examples
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                Get Content Strategy
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '420%', label: 'Avg Traffic Increase' },
                { number: '120+', label: 'Content Campaigns' },
                { number: '92%', label: 'Client Satisfaction' },
                { number: '6-Month', label: 'Authority Building' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Why Landscaping Businesses Choose Our Content Marketing
            </h2>
            <p className="text-lg text-gray-600">
              We create content strategies specifically designed for landscaping business owners. Every piece of content 
              is crafted to build your authority, educate customers, and drive business growth.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {contentFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Content Types Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Content That Drives Results for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              We create diverse content types that engage your audience and establish your landscaping business as the go-to expert.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {contentTypes.map((contentType, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{contentType.name}</h3>
                  <p className="text-gray-600 mb-4">{contentType.description}</p>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900 text-sm">Examples:</h4>
                    <ul className="space-y-1">
                      {contentType.examples.map((example, idx) => (
                        <li key={idx} className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="w-3 h-3 text-green-500 mr-2 flex-shrink-0" />
                          {example}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Real Results for Landscaping Business Owners
              </h2>
              <p className="text-lg text-gray-600">
                See how our content marketing strategy helped {caseStudy.company} become the recognized authority in {caseStudy.location}
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A premium landscaping business in {caseStudy.location} wanted to establish thought leadership and attract high-value clients. 
                      Our content strategy helped them become the go-to landscaping authority in their market.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Read Full Case Study
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Content Marketing Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Professional content marketing services designed specifically for landscaping business owners. 
              Build authority, educate customers, and drive long-term growth.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* FAQ Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Frequently Asked Questions About Content Marketing for Landscaping
              </h2>
              <p className="text-lg text-gray-600">
                Common questions landscaping business owners have about content marketing and digital storytelling.
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "What type of content works best for landscaping businesses?",
                  answer: "Before/after project showcases, seasonal landscaping tips, plant care guides, design inspiration, time-lapse videos of installations, and educational content about sustainable landscaping practices perform exceptionally well for landscaping businesses."
                },
                {
                  question: "How often should my landscaping business post content?",
                  answer: "We recommend 3-4 posts per week across platforms, with daily Instagram stories during peak season. Consistency matters more than frequency - regular, quality content builds trust and keeps your landscaping business top-of-mind."
                },
                {
                  question: "Can content marketing help during slow landscaping seasons?",
                  answer: "Absolutely! Winter content like planning guides, indoor plant care, and next season preparation keeps your audience engaged year-round. It's also perfect for showcasing completed projects and building anticipation for spring services."
                },
                {
                  question: "How do you measure content marketing success for landscaping businesses?",
                  answer: "We track engagement rates, website traffic from content, lead generation from blog posts, social media inquiries, and most importantly - how content influences the customer journey from awareness to booking landscaping services."
                },
                {
                  question: "Should my landscaping business create video content?",
                  answer: "Yes! Video content gets 10x more engagement. Time-lapse installations, plant care tutorials, design walkthroughs, and client testimonials are incredibly effective for showcasing your landscaping expertise and building trust with potential customers."
                }
              ].map((faq, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{faq.question}</h3>
                  <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Build Authority for Your Landscaping Business?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a custom content marketing strategy designed to position your landscaping business as the local authority and drive long-term growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <FileText className="w-5 h-5 mr-2" />
                Get Content Strategy
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule Consultation
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
