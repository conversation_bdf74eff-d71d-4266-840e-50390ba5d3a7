import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, Code, Zap, Shield, Users, Star, CheckCircle, Phone, Settings } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Custom Software Development for Landscaping Businesses | GroundUP Digital',
  description: 'Tailored software solutions designed specifically for landscaping business operations. Custom applications, integrations, and business automation.',
  keywords: 'custom software landscaping, landscaping business software, custom applications landscaping, business automation landscaping, tailored software solutions',
};

const softwareFeatures = [
  {
    icon: Code,
    title: 'Custom Application Development',
    description: 'Bespoke software applications built specifically for your landscaping business needs.',
    benefits: ['Tailored functionality', 'Scalable architecture', 'Modern technology stack', 'Future-proof design']
  },
  {
    icon: Zap,
    title: 'Business Process Automation',
    description: 'Streamline operations with intelligent automation and workflow optimization.',
    benefits: ['Automated workflows', 'Process optimization', 'Efficiency improvements', 'Cost reduction']
  },
  {
    icon: Shield,
    title: 'System Integration & APIs',
    description: 'Connect all your business systems for seamless data flow and operations.',
    benefits: ['Third-party integrations', 'Data synchronization', 'Unified platforms', 'API development']
  }
];

const softwareTypes = [
  {
    name: 'Business Management Platform',
    description: 'Comprehensive software suite for complete landscaping business management',
    features: ['Customer relationship management', 'Project management tools', 'Financial tracking', 'Inventory management', 'Employee scheduling', 'Reporting & analytics']
  },
  {
    name: 'Specialized Industry Tools',
    description: 'Custom tools designed for specific landscaping industry challenges',
    features: ['Plant database systems', 'Irrigation calculators', 'Seasonal planning tools', 'Equipment tracking', 'Maintenance scheduling', 'Weather integration']
  },
  {
    name: 'Client-Facing Applications',
    description: 'Custom software that enhances your client experience and engagement',
    features: ['Client portals', 'Project visualization tools', 'Service booking systems', 'Communication platforms', 'Payment processing', 'Feedback collection']
  },
  {
    name: 'Operational Efficiency Tools',
    description: 'Software solutions that optimize day-to-day landscaping operations',
    features: ['Route optimization', 'Time tracking systems', 'Quality control checklists', 'Material ordering', 'Crew coordination', 'Performance monitoring']
  }
];

const packages = [
  {
    name: "Custom Application",
    price: "$4,997",
    period: "/month",
    description: "Single custom application development for specific business needs",
    features: [
      "Custom application design & development",
      "User interface & experience design",
      "Database design & implementation",
      "Testing & quality assurance",
      "Deployment & launch support",
      "3 months post-launch support"
    ],
    popular: false
  },
  {
    name: "Business Suite",
    price: "$8,997",
    period: "/month", 
    description: "Comprehensive custom software suite for landscaping business operations",
    features: [
      "Multiple integrated applications",
      "Advanced system architecture",
      "Third-party integrations",
      "Custom reporting & analytics",
      "Ongoing development & updates",
      "Dedicated development team"
    ],
    popular: true
  },
  {
    name: "Enterprise Platform",
    price: "$15,997",
    period: "/month",
    description: "Complete custom software ecosystem for large landscaping organizations",
    features: [
      "Full-scale platform development",
      "Advanced automation & AI",
      "Multi-location support",
      "Custom integrations & APIs",
      "White-label solutions",
      "Dedicated technical team"
    ],
    popular: false
  }
];

const caseStudy = {
  company: "TerraForm Landscapes",
  location: "Portland, OR",
  results: [
    { metric: "Operational Efficiency", improvement: "+275%" },
    { metric: "Data Accuracy", improvement: "+95%" },
    { metric: "Process Automation", improvement: "+80%" },
    { metric: "Cost Reduction", improvement: "-45%" }
  ]
};

const developmentCapabilities = [
  { capability: "Web Applications", description: "Modern, responsive web-based business applications" },
  { capability: "Mobile Applications", description: "Native and cross-platform mobile app development" },
  { capability: "Database Systems", description: "Custom database design and management solutions" },
  { capability: "API Development", description: "RESTful APIs and system integration services" },
  { capability: "Cloud Solutions", description: "Scalable cloud-based software architecture" },
  { capability: "AI & Automation", description: "Intelligent automation and machine learning integration" }
];

export default function CustomSoftwarePage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Code className="w-4 h-4" />
              <span className="text-sm font-semibold">Custom Software Development</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Tailored Software Solutions for{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscaping Businesses
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Custom software development that addresses your unique landscaping business challenges with 
              bespoke applications, automation, and system integrations.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Code className="w-5 h-5 mr-2" />
                View Development Process
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                See Custom Solutions
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '275%', label: 'Efficiency Increase' },
                { number: '95%', label: 'Data Accuracy' },
                { number: '80%', label: 'Process Automation' },
                { number: '25+', label: 'Custom Solutions Built' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Custom Software Solutions Built for Landscaping Success
            </h2>
            <p className="text-lg text-gray-600">
              Our custom software development is designed specifically for landscaping business owners who need 
              tailored solutions that address their unique operational challenges and growth goals.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {softwareFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Development Capabilities */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Comprehensive Development Capabilities
            </h2>
            <p className="text-lg text-gray-600">
              Full-stack development expertise covering all aspects of custom software creation for landscaping businesses.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-6">
            {developmentCapabilities.map((item, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="flex items-center mb-3">
                    <Settings className="w-5 h-5 text-blue-600 mr-2" />
                    <h3 className="text-lg font-bold text-gray-900">{item.capability}</h3>
                  </div>
                  <p className="text-gray-600 text-sm">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Software Types Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Custom Software Solutions for Every Landscaping Need
            </h2>
            <p className="text-lg text-gray-600">
              Tailored software development covering all aspects of landscaping business operations and growth.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {softwareTypes.map((software, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{software.name}</h3>
                  <p className="text-gray-600 mb-4">{software.description}</p>
                  <ul className="space-y-2">
                    {software.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Custom Software Success Story: Business Transformation
              </h2>
              <p className="text-lg text-gray-600">
                See how {caseStudy.company} used our custom software to revolutionize their landscaping operations
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A growing landscaping business in {caseStudy.location} needed custom software to manage complex operations. 
                      Our tailored solution transformed their efficiency and competitive advantage.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Read Full Case Study
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Custom Software Development Packages
            </h2>
            <p className="text-lg text-gray-600">
              Tailored software development designed specifically for landscaping business owners. 
              Choose the package that addresses your unique business challenges and growth objectives.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Build Custom Software for Your Landscaping Business?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a free custom software consultation and discover how tailored solutions can transform 
              your landscaping business operations and competitive advantage.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Code className="w-5 h-5 mr-2" />
                Get Development Consultation
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule Discovery Call
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
