'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  TreePine, 
  Home, 
  Bug, 
  ArrowRight, 
  CheckCircle,
  TrendingUp,
  Users,
  Target
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Container, Section } from '@/components/ui/Container';
import { Badge } from '@/components/ui/Badge';

const niches = [
  {
    icon: TreePine,
    title: 'Landscaping Companies',
    description: 'Specialized digital marketing solutions for landscaping and lawn care businesses.',
    challenges: [
      'Seasonal demand fluctuations',
      'Local competition',
      'Showcasing visual work',
      'Weather-dependent scheduling'
    ],
    solutions: [
      'Seasonal SEO strategies',
      'Local search domination',
      'Visual portfolio websites',
      'Automated lead nurturing'
    ],
    results: {
      leads: '250%',
      revenue: '$45K',
      ranking: '#1'
    },
    color: 'green',
    bgGradient: 'from-green-50 to-emerald-50'
  },
  {
    icon: Home,
    title: 'Roofing Contractors',
    description: 'Proven marketing strategies to help roofing companies generate high-value leads.',
    challenges: [
      'High-ticket sales cycles',
      'Trust and credibility',
      'Emergency service needs',
      'Insurance claim processes'
    ],
    solutions: [
      'Trust-building content',
      'Emergency response SEO',
      'Insurance partnership marketing',
      'High-converting landing pages'
    ],
    results: {
      leads: '300%',
      revenue: '$78K',
      ranking: '#1'
    },
    color: 'blue',
    bgGradient: 'from-blue-50 to-indigo-50'
  },
  {
    icon: Bug,
    title: 'Pest Control Services',
    description: 'Digital marketing solutions that help pest control companies capture urgent leads.',
    challenges: [
      'Urgent service requests',
      'Seasonal pest patterns',
      'Health and safety concerns',
      'Recurring service models'
    ],
    solutions: [
      '24/7 lead capture systems',
      'Seasonal campaign optimization',
      'Educational content marketing',
      'Subscription service funnels'
    ],
    results: {
      leads: '280%',
      revenue: '$52K',
      ranking: '#1'
    },
    color: 'orange',
    bgGradient: 'from-orange-50 to-red-50'
  }
];

const stats = [
  { icon: Users, value: '500+', label: 'Local Service Clients' },
  { icon: TrendingUp, value: '300%', label: 'Average Lead Increase' },
  { icon: Target, value: '95%', label: 'Client Retention Rate' },
  { icon: CheckCircle, value: '24/7', label: 'Lead Generation' }
];

export const NichesSection: React.FC = () => {
  return (
    <Section background="gray" padding="xl">
      <Container>
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center max-w-4xl mx-auto mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-white rounded-full px-6 py-3 shadow-lg mb-6">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-semibold text-gray-700">Industry Specialization • Proven Results</span>
          </div>

          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            Tailored Solutions for{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600">
              Your Industry
            </span>
          </h2>

          <p className="text-xl text-gray-600 mb-8">
            We don't believe in one-size-fits-all marketing. Our strategies are specifically designed
            for the unique challenges and opportunities in your industry.
          </p>
          <p className="text-large text-gray-600">
            We understand the unique challenges and opportunities in each industry. 
            Our specialized approach delivers results that generic marketing agencies simply can't match.
          </p>
        </motion.div>

        {/* Niches Grid */}
        <div className="space-y-8 mb-16">
          {niches.map((niche, index) => {
            const Icon = niche.icon;
            return (
              <motion.div
                key={niche.title}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
              >
                <Card className="overflow-hidden">
                  <div className={`bg-gradient-to-r ${niche.bgGradient} p-8`}>
                    <div className="grid lg:grid-cols-3 gap-8 items-center">
                      {/* Left Column - Icon & Title */}
                      <div className="space-y-4">
                        <div className={`w-16 h-16 rounded-2xl bg-white shadow-lg flex items-center justify-center text-${niche.color}-600`}>
                          <Icon className="h-8 w-8" />
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900 mb-2">
                            {niche.title}
                          </h3>
                          <p className="text-gray-600">
                            {niche.description}
                          </p>
                        </div>
                      </div>

                      {/* Middle Column - Challenges & Solutions */}
                      <div className="lg:col-span-1 space-y-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3">Common Challenges:</h4>
                          <ul className="space-y-2">
                            {niche.challenges.map((challenge) => (
                              <li key={challenge} className="flex items-start text-sm text-gray-600">
                                <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 mr-3 flex-shrink-0" />
                                {challenge}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-3">Our Solutions:</h4>
                          <ul className="space-y-2">
                            {niche.solutions.map((solution) => (
                              <li key={solution} className="flex items-start text-sm text-gray-600">
                                <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                                {solution}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Right Column - Results */}
                      <div className="space-y-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-4">Typical Results:</h4>
                          <div className="grid grid-cols-3 gap-4">
                            <div className="text-center bg-white rounded-lg p-4 shadow-sm">
                              <div className={`text-2xl font-bold text-${niche.color}-600`}>
                                {niche.results.leads}
                              </div>
                              <div className="text-xs text-gray-600">Lead Increase</div>
                            </div>
                            <div className="text-center bg-white rounded-lg p-4 shadow-sm">
                              <div className={`text-2xl font-bold text-${niche.color}-600`}>
                                {niche.results.revenue}
                              </div>
                              <div className="text-xs text-gray-600">Monthly Revenue</div>
                            </div>
                            <div className="text-center bg-white rounded-lg p-4 shadow-sm">
                              <div className={`text-2xl font-bold text-${niche.color}-600`}>
                                {niche.results.ranking}
                              </div>
                              <div className="text-xs text-gray-600">Local Ranking</div>
                            </div>
                          </div>
                        </div>
                        <Button variant="primary" className="w-full group">
                          Get Industry-Specific Strategy
                          <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="bg-white rounded-2xl p-8 shadow-lg"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">
              Proven Results Across All Industries
            </h3>
            <p className="text-gray-600">
              Our specialized approach delivers consistent results for local service providers
            </p>
          </div>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Icon className="h-6 w-6" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-1">
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-600">
                    {stat.label}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      </Container>
    </Section>
  );
};
