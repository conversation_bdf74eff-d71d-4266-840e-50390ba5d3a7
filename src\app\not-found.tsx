import React from 'react';
import Link from 'next/link';
import { Search, Home, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Container, Section } from '@/components/ui/Container';

export default function NotFound() {
  return (
    <Section background="gradient" padding="xl">
      <Container>
        <div className="text-center max-w-2xl mx-auto">
          <div className="mb-8">
            {/* 404 Illustration */}
            <div className="text-8xl font-bold text-blue-600 mb-4">404</div>
            
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Page Not Found
            </h1>
            
            <p className="text-gray-600 mb-8">
              Sorry, we couldn't find the page you're looking for. 
              It might have been moved, deleted, or you entered the wrong URL.
            </p>
          </div>
          
          {/* Search Box */}
          <div className="mb-8">
            <div className="relative max-w-md mx-auto">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search our site..."
                className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              />
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Link href="/">
              <Button variant="primary" size="lg">
                <Home className="mr-2 h-5 w-5" />
                Go Home
              </Button>
            </Link>
            
            <Button 
              variant="outline" 
              size="lg"
              onClick={() => window.history.back()}
            >
              <ArrowLeft className="mr-2 h-5 w-5" />
              Go Back
            </Button>
          </div>
          
          {/* Popular Links */}
          <div className="bg-white rounded-lg p-6 shadow-lg">
            <h3 className="font-semibold text-gray-900 mb-4">Popular Pages</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <Link href="/services" className="text-blue-600 hover:text-blue-700">
                Our Services
              </Link>
              <Link href="/industries" className="text-blue-600 hover:text-blue-700">
                Industries We Serve
              </Link>
              <Link href="/case-studies" className="text-blue-600 hover:text-blue-700">
                Case Studies
              </Link>
              <Link href="/contact" className="text-blue-600 hover:text-blue-700">
                Contact Us
              </Link>
              <Link href="/about" className="text-blue-600 hover:text-blue-700">
                About Us
              </Link>
              <Link href="/blog" className="text-blue-600 hover:text-blue-700">
                Blog & Resources
              </Link>
            </div>
          </div>
        </div>
      </Container>
    </Section>
  );
}
