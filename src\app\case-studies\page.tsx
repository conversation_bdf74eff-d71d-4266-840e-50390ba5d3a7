'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  Users, 
  Target, 
  Star,
  ArrowRight,
  TreePine,
  Home,
  Bug,
  Calendar,
  DollarSign
} from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

const caseStudies = [
  {
    id: 1,
    title: 'GreenScape Landscaping: From 3 to 20 Leads Per Week',
    client: 'GreenScape Landscaping',
    industry: 'Landscaping',
    icon: TreePine,
    duration: '6 months',
    challenge: 'GreenScape was struggling with inconsistent lead flow and poor online visibility. They were getting only 2-3 leads per week and losing business to competitors.',
    solution: 'We implemented a comprehensive digital strategy including local SEO optimization, Google My Business enhancement, and a lead-focused website redesign.',
    results: {
      leads: { before: '3/week', after: '20/week', increase: '567%' },
      revenue: { before: '$8K/month', after: '$25K/month', increase: '213%' },
      ranking: { before: 'Page 3', after: '#1 Local', increase: 'Top 3' },
      reviews: { before: '3.2★', after: '4.8★', increase: '****★' }
    },
    testimonial: "GroundUPDigital transformed our business completely. We went from struggling to find customers to having a waiting list. The ROI has been incredible.",
    author: 'Mike Rodriguez, Owner',
    color: 'green',
    bgGradient: 'from-green-50 to-emerald-50'
  },
  {
    id: 2,
    title: 'Elite Roofing: Dominating Emergency Roof Repair Searches',
    client: 'Elite Roofing Solutions',
    industry: 'Roofing',
    icon: Home,
    duration: '8 months',
    challenge: 'Elite Roofing needed to capture emergency roof repair leads and build trust with homeowners dealing with insurance claims.',
    solution: 'We created emergency-focused landing pages, optimized for "roof repair near me" searches, and developed trust-building content around insurance processes.',
    results: {
      leads: { before: '5/week', after: '18/week', increase: '260%' },
      revenue: { before: '$15K/month', after: '$45K/month', increase: '200%' },
      ranking: { before: 'Page 2', after: '#1 Local', increase: 'Top 1' },
      conversion: { before: '2.1%', after: '8.5%', increase: '+6.4%' }
    },
    testimonial: "The emergency lead system they built has been a game-changer. We're now the go-to roofing company in our area for emergency repairs.",
    author: 'Sarah Thompson, Owner',
    color: 'blue',
    bgGradient: 'from-blue-50 to-indigo-50'
  },
  {
    id: 3,
    title: 'Pest Guard Pro: 24/7 Lead Capture System Success',
    client: 'Pest Guard Pro',
    industry: 'Pest Control',
    icon: Bug,
    duration: '4 months',
    challenge: 'Pest Guard Pro was missing after-hours leads and needed a system to capture urgent pest control requests around the clock.',
    solution: 'We implemented a 24/7 lead capture system with automated responses, emergency booking forms, and pest-specific landing pages.',
    results: {
      leads: { before: '8/week', after: '25/week', increase: '213%' },
      revenue: { before: '$12K/month', after: '$32K/month', increase: '167%' },
      afterHours: { before: '0%', after: '35%', increase: '+35%' },
      booking: { before: '15%', after: '42%', increase: '+27%' }
    },
    testimonial: "The 24/7 system captures leads even when we're sleeping. We've tripled our business in just 4 months.",
    author: 'David Chen, Owner',
    color: 'orange',
    bgGradient: 'from-orange-50 to-red-50'
  }
];

const overallStats = [
  { icon: TrendingUp, value: '300%', label: 'Average Lead Increase' },
  { icon: DollarSign, value: '$2.1M', label: 'Revenue Generated' },
  { icon: Users, value: '500+', label: 'Businesses Helped' },
  { icon: Star, value: '4.9★', label: 'Client Satisfaction' }
];

export default function CaseStudiesPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <Badge variant="primary" size="lg" className="mb-6">
                Success Stories
              </Badge>
              
              <h1 className="heading-1 text-gray-900 mb-6">
                Real Results for{' '}
                <span className="gradient-text">Real Businesses</span>
              </h1>
              
              <p className="text-large text-gray-600 mb-8">
                See how we've helped local service providers transform their digital presence 
                and generate more leads than ever before. These are real results from real clients.
              </p>
            </motion.div>
          </div>
        </Container>
      </Section>

      {/* Overall Stats */}
      <Section background="white" padding="lg">
        <Container>
          <div className="grid md:grid-cols-4 gap-8">
            {overallStats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <Card>
                    <CardContent className="space-y-4">
                      <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center mx-auto">
                        <Icon className="h-6 w-6" />
                      </div>
                      <div className="text-3xl font-bold text-gray-900">
                        {stat.value}
                      </div>
                      <div className="text-gray-600">
                        {stat.label}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </Container>
      </Section>

      {/* Case Studies */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="space-y-16">
            {caseStudies.map((study, index) => {
              const Icon = study.icon;
              return (
                <motion.div
                  key={study.id}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                >
                  <Card className="overflow-hidden">
                    <div className={`bg-gradient-to-r ${study.bgGradient} p-8`}>
                      <div className="grid lg:grid-cols-3 gap-8">
                        {/* Left Column - Overview */}
                        <div className="space-y-6">
                          <div className="flex items-center space-x-4">
                            <div className={`w-16 h-16 bg-white shadow-lg rounded-2xl flex items-center justify-center text-${study.color}-600`}>
                              <Icon className="h-8 w-8" />
                            </div>
                            <div>
                              <Badge variant="secondary" size="sm" className="mb-2">
                                {study.industry}
                              </Badge>
                              <h3 className="text-xl font-bold text-gray-900">
                                {study.client}
                              </h3>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-1" />
                              {study.duration}
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">Challenge:</h4>
                            <p className="text-gray-700 text-sm mb-4">{study.challenge}</p>
                            
                            <h4 className="font-semibold text-gray-900 mb-2">Solution:</h4>
                            <p className="text-gray-700 text-sm">{study.solution}</p>
                          </div>
                        </div>

                        {/* Middle Column - Results */}
                        <div className="space-y-6">
                          <h4 className="font-semibold text-gray-900 text-lg">Results Achieved:</h4>
                          
                          <div className="grid grid-cols-2 gap-4">
                            {Object.entries(study.results).map(([key, result]) => (
                              <div key={key} className="bg-white rounded-lg p-4 shadow-sm">
                                <div className="text-xs text-gray-500 uppercase tracking-wide mb-1">
                                  {key === 'leads' ? 'Weekly Leads' : 
                                   key === 'revenue' ? 'Monthly Revenue' :
                                   key === 'ranking' ? 'Search Ranking' :
                                   key === 'reviews' ? 'Review Rating' :
                                   key === 'conversion' ? 'Conversion Rate' :
                                   key === 'afterHours' ? 'After Hours' :
                                   key === 'booking' ? 'Booking Rate' : key}
                                </div>
                                <div className="flex items-center justify-between">
                                  <span className="text-sm text-gray-600">{result.before}</span>
                                  <ArrowRight className="h-3 w-3 text-gray-400" />
                                  <span className="text-sm font-bold text-gray-900">{result.after}</span>
                                </div>
                                <div className={`text-xs font-medium text-${study.color}-600 mt-1`}>
                                  +{result.increase}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Right Column - Testimonial */}
                        <div className="space-y-6">
                          <div className="bg-white rounded-lg p-6 shadow-sm">
                            <div className="flex items-start space-x-3 mb-4">
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                                ))}
                              </div>
                            </div>
                            
                            <blockquote className="text-gray-700 mb-4 italic">
                              "{study.testimonial}"
                            </blockquote>
                            
                            <div className="flex items-center space-x-3">
                              <div className={`w-10 h-10 bg-${study.color}-100 rounded-full flex items-center justify-center`}>
                                <Icon className={`h-5 w-5 text-${study.color}-600`} />
                              </div>
                              <div>
                                <div className="font-semibold text-gray-900 text-sm">
                                  {study.author}
                                </div>
                                <div className="text-gray-600 text-xs">
                                  {study.client}
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <Button variant="outline" className="w-full">
                            View Full Case Study
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="heading-2 text-gray-900 mb-6">
              Ready to Be Our Next Success Story?
            </h2>
            <p className="text-large text-gray-600 mb-8">
              Join hundreds of local service providers who have transformed their businesses 
              with our proven digital marketing strategies.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="primary" size="lg">
                Get Your Free Strategy Session
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button variant="outline" size="lg">
                Download Case Study PDF
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
