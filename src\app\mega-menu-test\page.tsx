'use client';

import React from 'react';
import { Container } from '@/components/ui/Container';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { 
  CheckCircle,
  AlertCircle,
  Info,
  Mouse,
  Keyboard,
  Smartphone,
  Monitor,
  Eye,
  Users
} from 'lucide-react';

const testCriteria = [
  {
    category: 'Desktop Functionality',
    icon: Monitor,
    tests: [
      'Hover over "Industries" menu item to open mega menu',
      'Click on different industry tabs to switch content',
      'Hover over service items to see hover effects',
      'Click on service links to navigate (test in new tab)',
      'Click on CTA buttons to verify functionality',
      'Move mouse away from mega menu to close it'
    ]
  },
  {
    category: 'Mobile Responsiveness',
    icon: Smartphone,
    tests: [
      'Open mobile menu using hamburger button',
      'Tap on "Industries" to expand industry list',
      'Tap on individual industry items to navigate',
      'Verify touch targets are at least 44px',
      'Check that content doesn\'t overflow horizontally',
      'Test scrolling within mobile menu'
    ]
  },
  {
    category: 'Keyboard Navigation',
    icon: Keyboard,
    tests: [
      'Tab through navigation items',
      'Press Enter/Space on "Industries" to open mega menu',
      'Use arrow keys to navigate between tabs',
      'Tab through service items within mega menu',
      'Press Escape to close mega menu',
      'Verify focus indicators are visible'
    ]
  },
  {
    category: 'Accessibility',
    icon: Eye,
    tests: [
      'Screen reader announces menu items correctly',
      'ARIA labels are present and descriptive',
      'Focus management works properly',
      'Color contrast meets WCAG standards',
      'Interactive elements have proper roles',
      'Keyboard shortcuts work as expected'
    ]
  },
  {
    category: 'Visual Design',
    icon: Users,
    tests: [
      'Mega menu has professional enterprise appearance',
      'Animations are smooth and not jarring',
      'Typography is consistent and readable',
      'Icons align properly with text',
      'Spacing and padding look balanced',
      'Hover states provide clear feedback'
    ]
  },
  {
    category: 'Performance',
    icon: Mouse,
    tests: [
      'Mega menu opens/closes without delay',
      'Tab switching is instantaneous',
      'No layout shifts when menu opens',
      'Images and icons load quickly',
      'Animations don\'t cause performance issues',
      'Menu works smoothly on slower devices'
    ]
  }
];

export default function MegaMenuTestPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <Container className="py-6">
          <div className="text-center">
            <h1 className="heading-1 text-gray-900 mb-4">
              Mega Menu Testing Page
            </h1>
            <p className="text-body text-gray-600 max-w-2xl mx-auto">
              Comprehensive testing guide for the new Industries mega menu. 
              Use this page to validate functionality, accessibility, and user experience.
            </p>
          </div>
        </Container>
      </div>

      {/* Instructions */}
      <div className="bg-blue-600 text-white py-4">
        <Container>
          <div className="flex items-center justify-center gap-2 text-sm">
            <Info className="w-4 h-4" />
            <span>Navigate to the header above and test the "Industries" mega menu using the criteria below</span>
          </div>
        </Container>
      </div>

      <Container className="section-padding">
        {/* Test Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-12">
          {testCriteria.map((category, index) => {
            const Icon = category.icon;
            return (
              <Card key={index} className="h-full">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="bg-blue-100 p-2 rounded-lg">
                      <Icon className="w-6 h-6 text-blue-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">{category.category}</h3>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {category.tests.map((test, testIndex) => (
                      <div key={testIndex} className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 text-sm leading-relaxed">{test}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Key Features */}
        <section className="mb-12">
          <h2 className="heading-3 text-gray-900 mb-6">Key Features Implemented</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                title: 'Enterprise Design',
                description: 'Professional, clean mega menu with tabbed interface',
                icon: '🏢'
              },
              {
                title: 'Responsive Layout',
                description: 'Adapts from desktop mega menu to mobile accordion',
                icon: '📱'
              },
              {
                title: 'Smooth Animations',
                description: 'Framer Motion powered transitions and interactions',
                icon: '✨'
              },
              {
                title: 'Accessibility First',
                description: 'WCAG compliant with keyboard navigation and ARIA labels',
                icon: '♿'
              },
              {
                title: 'Touch Friendly',
                description: 'Optimized touch targets for mobile devices',
                icon: '👆'
              },
              {
                title: 'Service Organization',
                description: 'Clear categorization of services by industry',
                icon: '📋'
              }
            ].map((feature, index) => (
              <Card key={index}>
                <CardContent className="text-center py-6">
                  <div className="text-3xl mb-3">{feature.icon}</div>
                  <h4 className="font-semibold text-gray-900 mb-2">{feature.title}</h4>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Browser Testing */}
        <section className="mb-12">
          <h2 className="heading-3 text-gray-900 mb-6">Browser & Device Testing</h2>
          <Card>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Desktop Browsers</h4>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div>• Chrome (latest)</div>
                    <div>• Firefox (latest)</div>
                    <div>• Safari (latest)</div>
                    <div>• Edge (latest)</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Mobile Devices</h4>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div>• iPhone (iOS Safari)</div>
                    <div>• Android (Chrome)</div>
                    <div>• iPad (Safari)</div>
                    <div>• Android Tablet</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Screen Sizes</h4>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div>• 320px (Mobile)</div>
                    <div>• 768px (Tablet)</div>
                    <div>• 1024px (Laptop)</div>
                    <div>• 1440px+ (Desktop)</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">Accessibility Tools</h4>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div>• Screen Reader</div>
                    <div>• Keyboard Only</div>
                    <div>• High Contrast</div>
                    <div>• Color Blindness</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Success Criteria */}
        <section>
          <Card className="bg-green-50 border-green-200">
            <CardHeader>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <h3 className="text-lg font-semibold text-green-800">Success Criteria</h3>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-green-700 space-y-2 text-sm">
                <p>✅ Mega menu opens smoothly on hover (desktop) and click (mobile)</p>
                <p>✅ All industry tabs switch content correctly</p>
                <p>✅ Service links navigate to correct pages</p>
                <p>✅ Mobile menu collapses appropriately with touch-friendly targets</p>
                <p>✅ Keyboard navigation works throughout the menu</p>
                <p>✅ Screen readers can navigate and announce content properly</p>
                <p>✅ Visual design maintains professional enterprise appearance</p>
                <p>✅ Performance remains smooth across all devices and browsers</p>
              </div>
            </CardContent>
          </Card>
        </section>
      </Container>
    </div>
  );
}
