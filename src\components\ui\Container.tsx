import React from 'react';
import { clsx } from 'clsx';

interface ContainerProps {
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  className?: string;
}

export const Container: React.FC<ContainerProps> = ({
  children,
  size = 'lg',
  className
}) => {
  const sizeClasses = {
    sm: 'max-w-3xl',
    md: 'max-w-5xl',
    lg: 'max-w-7xl',
    xl: 'max-w-8xl',
    full: 'max-w-full'
  };

  return (
    <div
      className={clsx(
        'mx-auto px-3 xs:px-4 sm:px-6 lg:px-8 xl:px-12',
        sizeClasses[size],
        className
      )}
    >
      {children}
    </div>
  );
};

interface SectionProps {
  children: React.ReactNode;
  className?: string;
  background?: 'white' | 'gray' | 'gradient';
  padding?: 'sm' | 'md' | 'lg' | 'xl';
}

export const Section: React.FC<SectionProps> = ({
  children,
  className,
  background = 'white',
  padding = 'lg'
}) => {
  const backgroundClasses = {
    white: 'bg-white',
    gray: 'bg-gray-50',
    gradient: 'bg-gradient-to-br from-blue-50 via-white to-green-50'
  };
  
  const paddingClasses = {
    sm: 'py-4 xs:py-6 sm:py-8 lg:py-12',
    md: 'py-6 xs:py-8 sm:py-12 lg:py-16',
    lg: 'py-8 xs:py-12 sm:py-16 lg:py-24',
    xl: 'py-12 xs:py-16 sm:py-24 lg:py-32'
  };

  return (
    <section
      className={clsx(
        backgroundClasses[background],
        paddingClasses[padding],
        className
      )}
    >
      {children}
    </section>
  );
};
