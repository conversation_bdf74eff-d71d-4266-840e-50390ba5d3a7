import { <PERSON><PERSON><PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, Brain, Target, TrendingUp, Zap, Star, CheckCircle, Phone, Bot } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'AI-Powered Lead Generation for Landscaping Businesses | GroundUP Digital',
  description: 'Advanced AI technology that identifies, qualifies, and nurtures high-value leads for your landscaping business. Predictive analytics and intelligent automation.',
  keywords: 'AI landscaping leads, artificial intelligence landscaping, automated lead generation landscaping, predictive analytics landscaping business',
};

const aiFeatures = [
  {
    icon: Brain,
    title: 'Predictive Lead Scoring',
    description: 'AI algorithms analyze customer data to identify the highest-value prospects for your landscaping business.',
    benefits: ['Property value analysis', 'Demographic targeting', 'Behavioral prediction', 'Conversion probability scoring']
  },
  {
    icon: Target,
    title: 'Intelligent Customer Targeting',
    description: 'Machine learning identifies ideal customers based on your best existing landscaping clients.',
    benefits: ['Lookalike audience creation', 'Geographic optimization', 'Seasonal demand prediction', 'Service preference matching']
  },
  {
    icon: Bot,
    title: 'Automated Lead Nurturing',
    description: 'AI-powered chatbots and email sequences that nurture leads 24/7 for your landscaping business.',
    benefits: ['Instant lead response', 'Personalized communication', 'Appointment scheduling', 'Follow-up automation']
  }
];

const aiCapabilities = [
  {
    name: 'Property Intelligence',
    description: 'AI analyzes property characteristics to identify landscaping opportunities',
    features: ['Satellite imagery analysis', 'Property size assessment', 'Landscape condition evaluation', 'Maintenance need prediction']
  },
  {
    name: 'Customer Journey Mapping',
    description: 'Track and optimize every touchpoint in your customer acquisition process',
    features: ['Behavioral tracking', 'Engagement scoring', 'Conversion optimization', 'Personalization engines']
  },
  {
    name: 'Predictive Analytics',
    description: 'Forecast demand and optimize your landscaping business operations',
    features: ['Seasonal demand forecasting', 'Revenue prediction', 'Capacity planning', 'Market trend analysis']
  },
  {
    name: 'Intelligent Automation',
    description: 'Automate repetitive tasks to focus on growing your landscaping business',
    features: ['Lead qualification', 'Appointment scheduling', 'Follow-up sequences', 'Proposal generation']
  }
];

const packages = [
  {
    name: "AI Starter",
    price: "$1,997",
    period: "/month",
    description: "Essential AI tools for landscaping businesses ready to automate lead generation",
    features: [
      "Basic lead scoring algorithm",
      "Automated email sequences",
      "Chatbot for website",
      "Monthly performance reports",
      "AI training & setup"
    ],
    popular: false
  },
  {
    name: "AI Growth Engine",
    price: "$3,997",
    period: "/month", 
    description: "Advanced AI system for landscaping companies ready to scale with intelligent automation",
    features: [
      "Advanced predictive analytics",
      "Multi-channel automation",
      "Property intelligence system",
      "Custom AI model training",
      "Dedicated AI specialist",
      "Quarterly optimization reviews"
    ],
    popular: true
  },
  {
    name: "AI Market Domination",
    price: "$7,997",
    period: "/month",
    description: "Enterprise AI solution for landscaping businesses ready to dominate their market",
    features: [
      "Custom AI development",
      "Advanced machine learning models",
      "Competitive intelligence AI",
      "Market prediction algorithms",
      "White-glove AI management",
      "Monthly strategy sessions"
    ],
    popular: false
  }
];

const caseStudy = {
  company: "TechScape Solutions",
  location: "San Francisco, CA",
  results: [
    { metric: "Lead Quality Score", improvement: "+380%" },
    { metric: "Conversion Rate", improvement: "+245%" },
    { metric: "Cost Per Acquisition", improvement: "-65%" },
    { metric: "Revenue Per Lead", improvement: "+190%" }
  ]
};

export default function AILeadGenerationPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Brain className="w-4 h-4" />
              <span className="text-sm font-semibold">AI-Powered Lead Generation for Landscaping</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Intelligent Lead Generation for{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Modern Landscaping Businesses
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Advanced AI technology that identifies, qualifies, and nurtures high-value leads for your landscaping business. 
              Predictive analytics and intelligent automation that works 24/7.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Brain className="w-5 h-5 mr-2" />
                See AI Demo
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                Get AI Assessment
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '380%', label: 'Lead Quality Increase' },
                { number: '24/7', label: 'AI Working' },
                { number: '65%', label: 'Cost Reduction' },
                { number: '50+', label: 'AI Models Deployed' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              AI Technology Built for Landscaping Business Success
            </h2>
            <p className="text-lg text-gray-600">
              Our AI systems are specifically trained on landscaping industry data to deliver superior results 
              for landscaping business owners. Every algorithm is optimized for your unique business needs.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {aiFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* AI Capabilities Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Advanced AI Capabilities for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Comprehensive AI solutions that transform how landscaping business owners attract, qualify, and convert customers.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {aiCapabilities.map((capability, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{capability.name}</h3>
                  <p className="text-gray-600 mb-4">{capability.description}</p>
                  <ul className="space-y-2">
                    {capability.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                AI Success Story: Landscaping Business Transformation
              </h2>
              <p className="text-lg text-gray-600">
                See how {caseStudy.company} used our AI technology to revolutionize their lead generation and scale their landscaping business
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A tech-forward landscaping business in {caseStudy.location} wanted to leverage AI to gain a competitive advantage. 
                      Our AI lead generation system helped them achieve unprecedented growth and efficiency.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Read Full AI Case Study
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              AI Lead Generation Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Advanced AI technology designed specifically for landscaping business owners. 
              Choose the AI solution that matches your growth ambitions.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* FAQ Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Frequently Asked Questions About AI Lead Generation for Landscaping
              </h2>
              <p className="text-lg text-gray-600">
                Common questions landscaping business owners have about AI-powered lead generation and automation.
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "How does AI lead generation work for landscaping businesses?",
                  answer: "Our AI analyzes online behavior patterns, property data, and seasonal trends to identify homeowners most likely to need landscaping services. It then automatically engages them through personalized outreach across multiple channels."
                },
                {
                  question: "Will AI replace human interaction in my landscaping business?",
                  answer: "No! AI handles initial lead identification and qualification, but human expertise remains essential for consultations, design, and service delivery. AI simply ensures you spend time with the highest-quality prospects."
                },
                {
                  question: "How accurate is AI at predicting landscaping leads?",
                  answer: "Our AI systems achieve 85%+ accuracy in identifying high-intent landscaping prospects by analyzing factors like property size, home value, recent purchases, seasonal patterns, and online behavior specific to landscaping services."
                },
                {
                  question: "Can AI help with seasonal landscaping lead generation?",
                  answer: "Absolutely! AI excels at seasonal prediction, automatically adjusting lead generation strategies for spring cleanup demand, summer maintenance needs, fall services, and winter planning, ensuring consistent leads year-round."
                },
                {
                  question: "How quickly can I see results from AI lead generation?",
                  answer: "Most landscaping businesses see qualified leads within 2-3 weeks of AI system deployment. The AI continuously learns and improves, with lead quality and volume typically increasing 40-60% within the first 90 days."
                }
              ].map((faq, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{faq.question}</h3>
                  <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Transform Your Landscaping Business with AI?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a free AI assessment and discover how artificial intelligence can revolutionize your landscaping business lead generation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Brain className="w-5 h-5 mr-2" />
                Get AI Assessment
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule AI Demo
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
