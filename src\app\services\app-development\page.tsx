import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'App Development for Local Service Businesses | GroundUPDigital',
  description: 'Custom mobile app development for landscapers, roofers, and pest control companies. Boost customer engagement and streamline operations with our tailored app solutions.',
  keywords: 'app development, mobile apps, local service apps, landscaping apps, roofing apps, pest control apps',
  openGraph: {
    title: 'App Development for Local Service Businesses | GroundUPDigital',
    description: 'Custom mobile app development for landscapers, roofers, and pest control companies. Boost customer engagement and streamline operations with our tailored app solutions.',
    type: 'website',
  },
};

export default function AppDevelopmentPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-green-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Custom App Development for <span className="text-blue-600">Local Service Businesses</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Transform your local service business with custom mobile apps designed to streamline operations, 
              enhance customer experience, and drive growth for landscapers, roofers, and pest control companies.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Free App Consultation
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                View App Portfolio
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              App Features That Drive Results
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: 'Customer Booking System',
                  description: 'Allow customers to book services directly through your app with real-time scheduling.',
                  icon: '📅'
                },
                {
                  title: 'Service Tracking',
                  description: 'Track service progress, send updates, and maintain detailed service histories.',
                  icon: '📍'
                },
                {
                  title: 'Payment Integration',
                  description: 'Secure payment processing with multiple payment options for customer convenience.',
                  icon: '💳'
                },
                {
                  title: 'Customer Portal',
                  description: 'Dedicated customer accounts with service history, invoices, and communication.',
                  icon: '👤'
                },
                {
                  title: 'Photo Documentation',
                  description: 'Before/after photos, progress updates, and visual service documentation.',
                  icon: '📸'
                },
                {
                  title: 'Push Notifications',
                  description: 'Keep customers informed with appointment reminders and service updates.',
                  icon: '🔔'
                }
              ].map((feature, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg border border-gray-100">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Industry-Specific Solutions */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Industry-Specific App Solutions
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  industry: 'Landscaping',
                  features: ['Seasonal service scheduling', 'Property mapping', 'Equipment tracking', 'Weather integration'],
                  color: 'green'
                },
                {
                  industry: 'Roofing',
                  features: ['Inspection reports', 'Damage documentation', 'Insurance integration', 'Material calculators'],
                  color: 'blue'
                },
                {
                  industry: 'Pest Control',
                  features: ['Treatment tracking', 'Chemical inventory', 'Recurring service management', 'Compliance reporting'],
                  color: 'red'
                }
              ].map((solution, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">{solution.industry} Apps</h3>
                  <ul className="space-y-3">
                    {solution.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Launch Your Custom App?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Get a free consultation and discover how a custom app can transform your local service business.
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Start Your App Project Today
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
