'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronUp, HelpCircle, MessageCircle, Phone, Mail } from 'lucide-react';
import { Section } from '@/components/ui/Section';
import { GlassCard } from '@/components/ui/GlassCard';
import { Button } from '@/components/ui/Button';
import { FAQSchema } from '@/components/seo/StructuredData';

interface FAQItem {
  question: string;
  answer: string;
  category: 'general' | 'services' | 'pricing' | 'process';
}

const faqData: FAQItem[] = [
  {
    category: 'general',
    question: "What makes GroundUPDigital different from other digital marketing agencies?",
    answer: "Unlike generic marketing agencies, we specialize exclusively in local service providers - landscapers, roofers, and pest control companies. This focus allows us to understand your unique challenges, seasonal patterns, and customer behavior. We've completed over 500 successful projects and maintain a 98% client satisfaction rate because we speak your industry's language and deliver results that matter to your business."
  },
  {
    category: 'services',
    question: "How long does it take to see results from digital marketing for landscaping companies?",
    answer: "For landscaping companies, you can expect to see initial results within 30-60 days for paid advertising campaigns, while SEO and organic growth typically show significant improvements within 3-6 months. Our clients typically see a 40-60% increase in qualified leads within the first quarter. The timeline depends on your current online presence, competition level, and the services you choose."
  },
  {
    category: 'services',
    question: "Do you provide website development specifically for roofing contractors?",
    answer: "Yes, we specialize in creating high-converting websites for roofing contractors. Our roofing websites include features like storm damage assessment forms, insurance claim assistance information, before/after project galleries, emergency contact options, and local SEO optimization. We understand the roofing industry's unique needs, including seasonal demand patterns and the importance of trust-building elements."
  },
  {
    category: 'services',
    question: "How does local SEO work for pest control companies?",
    answer: "Local SEO for pest control companies focuses on dominating search results for location-specific queries like 'pest control near me' or 'exterminator in [city]'. We optimize your Google My Business profile, create location-specific landing pages, build local citations, and develop content around common pest problems in your service area. This typically results in 150-300% more local visibility within 6 months."
  },
  {
    category: 'pricing',
    question: "What are your pricing packages for small landscaping businesses?",
    answer: "Our landscaping marketing packages start at $1,500/month for our Essential package, which includes website optimization, Google My Business management, and basic social media. Our Growth package at $2,500/month adds SEO, content marketing, and lead generation. The Premium package at $4,000/month includes comprehensive digital marketing with PPC advertising, advanced analytics, and dedicated account management."
  },
  {
    category: 'pricing',
    question: "Is there a setup fee for new roofing contractor clients?",
    answer: "Yes, we charge a one-time setup fee of $500-$1,500 depending on your chosen package. This covers initial website audit, competitor analysis, Google My Business optimization, tracking setup, and campaign creation. This investment ensures your campaigns launch with maximum effectiveness from day one."
  },
  {
    category: 'process',
    question: "What's included in your free digital marketing consultation?",
    answer: "Our free 60-minute consultation includes a comprehensive audit of your current online presence, competitor analysis, identification of immediate opportunities, custom strategy recommendations, and a detailed proposal with projected ROI. We'll also provide a free local SEO checklist and actionable tips you can implement immediately, regardless of whether you choose to work with us."
  },
  {
    category: 'process',
    question: "How do you measure success for local service provider marketing campaigns?",
    answer: "We track key performance indicators specific to local service providers: qualified lead volume, cost per lead, conversion rates, local search rankings, Google My Business engagement, phone calls generated, and ultimately, your return on investment. We provide monthly reports with clear metrics and insights, plus quarterly strategy reviews to optimize performance."
  },
  {
    category: 'services',
    question: "Do you handle social media marketing for pest control companies?",
    answer: "Absolutely! Social media for pest control companies requires a unique approach focusing on education, seasonal pest awareness, and building trust. We create content around pest prevention tips, seasonal pest alerts, before/after treatment photos (with permission), customer testimonials, and community engagement. This helps establish your expertise and keeps your business top-of-mind year-round."
  },
  {
    category: 'general',
    question: "What areas in Texas do you serve for local service provider marketing?",
    answer: "We primarily serve the major Texas markets including Austin, Dallas, Houston, San Antonio, and Fort Worth, along with their surrounding suburbs and metropolitan areas. However, we also work with clients throughout Texas and can adapt our local SEO strategies for any Texas city or region where you provide services."
  },
  {
    category: 'process',
    question: "How often will I receive reports and updates on my marketing campaigns?",
    answer: "You'll receive detailed monthly reports via email, plus access to a real-time dashboard where you can monitor your campaigns 24/7. We also schedule quarterly strategy calls to review performance, discuss optimizations, and plan for upcoming seasons or business goals. For Premium clients, we provide bi-weekly check-ins during peak seasons."
  },
  {
    category: 'services',
    question: "Can you help with Google Ads for seasonal landscaping services?",
    answer: "Yes, we excel at managing seasonal Google Ads campaigns for landscaping services. We understand the seasonal nature of landscaping - ramping up spring cleanup and lawn care campaigns, summer maintenance advertising, fall cleanup promotions, and winter planning services. Our seasonal campaign management typically reduces cost-per-click by 30-40% while increasing relevant traffic during peak seasons."
  }
];

const categories = [
  { id: 'all', label: 'All Questions', icon: HelpCircle },
  { id: 'general', label: 'General', icon: MessageCircle },
  { id: 'services', label: 'Services', icon: Phone },
  { id: 'pricing', label: 'Pricing', icon: Mail },
  { id: 'process', label: 'Process', icon: HelpCircle }
];

export const FAQSection: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [openItems, setOpenItems] = useState<Set<number>>(new Set());

  const filteredFAQs = activeCategory === 'all' 
    ? faqData 
    : faqData.filter(faq => faq.category === activeCategory);

  const toggleItem = (index: number) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index);
    } else {
      newOpenItems.add(index);
    }
    setOpenItems(newOpenItems);
  };

  return (
    <Section id="faq" className="py-20 bg-gradient-to-br from-blue-50 to-white">
      <div className="container mx-auto px-4">
        {/* Schema Markup */}
        <FAQSchema faqs={faqData.map(faq => ({ question: faq.question, answer: faq.answer }))} />
        
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Frequently Asked <span className="gradient-text-blue">Questions</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Get answers to common questions about digital marketing for landscaping, roofing, 
            and pest control companies. Can't find what you're looking for? Contact us directly.
          </p>
        </motion.div>

        {/* Category Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeCategory === category.id
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-white text-gray-600 hover:bg-blue-50 hover:text-blue-600 shadow-md'
              }`}
            >
              <category.icon className="h-5 w-5 mr-2" />
              {category.label}
            </button>
          ))}
        </motion.div>

        {/* FAQ Items */}
        <div className="max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeCategory}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              {filteredFAQs.map((faq, index) => (
                <motion.div
                  key={`${activeCategory}-${index}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <GlassCard className="overflow-hidden">
                    <button
                      onClick={() => toggleItem(index)}
                      className="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
                      aria-expanded={openItems.has(index)}
                    >
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-gray-900 pr-8">
                          {faq.question}
                        </h3>
                        <div className="flex-shrink-0">
                          {openItems.has(index) ? (
                            <ChevronUp className="h-6 w-6 text-blue-600" />
                          ) : (
                            <ChevronDown className="h-6 w-6 text-gray-400" />
                          )}
                        </div>
                      </div>
                    </button>
                    
                    <AnimatePresence>
                      {openItems.has(index) && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="overflow-hidden"
                        >
                          <div className="px-6 pb-6">
                            <p className="text-gray-700 leading-relaxed">
                              {faq.answer}
                            </p>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </GlassCard>
                </motion.div>
              ))}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Contact CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mt-16"
        >
          <GlassCard className="max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Still Have Questions?
            </h3>
            <p className="text-gray-600 mb-6">
              Our digital marketing experts are here to help. Get personalized answers 
              and a free consultation for your local service business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                variant="primary" 
                size="lg"
                onClick={() => {
                  const contactSection = document.getElementById('contact');
                  if (contactSection) {
                    contactSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
              >
                <Phone className="mr-2 h-5 w-5" />
                Schedule Free Consultation
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                onClick={() => {
                  window.location.href = 'mailto:<EMAIL>';
                }}
              >
                <Mail className="mr-2 h-5 w-5" />
                Email Your Question
              </Button>
            </div>
          </GlassCard>
        </motion.div>
      </div>
    </Section>
  );
};
