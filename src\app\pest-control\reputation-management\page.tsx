import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Reputation Management for Pest Control Companies | Build Trust | GroundUPDigital',
  description: 'Protect and enhance your pest control company\'s online reputation. Manage reviews, build trust, and attract more customers with professional reputation management.',
  keywords: 'pest control reputation management, exterminator reviews, online reputation, pest control trust, Google reviews',
  openGraph: {
    title: 'Reputation Management for Pest Control Companies | Build Trust | GroundUPDigital',
    description: 'Protect and enhance your pest control company\'s online reputation. Manage reviews, build trust, and attract more customers with professional reputation management.',
    type: 'website',
  },
};

export default function PestControlReputationManagementPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-yellow-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Reputation Management for <span className="text-green-600">Pest Control Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Protect and enhance your pest control company's online reputation. Build trust, manage reviews, 
              and attract more customers with professional reputation management that showcases your expertise and reliability.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Get Reputation Audit
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View Success Stories
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Reputation Impact */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Why Reputation Matters for Pest Control Companies
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '94%', label: 'Of customers read online reviews', icon: '👀' },
                { metric: '88%', label: 'Trust reviews as much as personal recommendations', icon: '🤝' },
                { metric: '72%', label: 'More likely to choose 5-star rated pest control', icon: '⭐' },
                { metric: '400%', label: 'More leads with strong online reputation', icon: '📈' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-green-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Reputation Management Services */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Comprehensive Reputation Management Services
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  service: 'Review Monitoring & Response',
                  description: 'Monitor all review platforms and respond professionally to every review.',
                  features: ['24/7 review monitoring', 'Professional response templates', 'Crisis management', 'Multi-platform coverage'],
                  icon: '👁️'
                },
                {
                  service: 'Review Generation',
                  description: 'Systematically generate positive reviews from satisfied customers.',
                  features: ['Automated review requests', 'Follow-up sequences', 'Multiple platform targeting', 'Timing optimization'],
                  icon: '⭐'
                },
                {
                  service: 'Negative Review Management',
                  description: 'Address negative reviews professionally and minimize their impact.',
                  features: ['Damage control strategies', 'Professional responses', 'Issue resolution', 'Reputation recovery'],
                  icon: '🛡️'
                },
                {
                  service: 'Trust Building Content',
                  description: 'Create content that builds trust and showcases your expertise.',
                  features: ['Educational content', 'Before/after galleries', 'Safety information', 'Certification displays'],
                  icon: '📝'
                },
                {
                  service: 'Crisis Communication',
                  description: 'Manage reputation crises and protect your brand during difficult times.',
                  features: ['Crisis response plans', 'Media management', 'Stakeholder communication', 'Damage mitigation'],
                  icon: '🚨'
                },
                {
                  service: 'Reputation Analytics',
                  description: 'Track and analyze your online reputation with detailed reporting.',
                  features: ['Reputation scoring', 'Competitor analysis', 'Trend monitoring', 'Performance reports'],
                  icon: '📊'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.service}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Trust Building for Pest Control */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Building Trust in Pest Control Services
              </h2>
              <p className="text-xl text-green-100">
                Specialized reputation strategies for pest control companies to build customer confidence.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  strategy: 'Safety & Compliance',
                  description: 'Highlight safety protocols and regulatory compliance to build trust.',
                  elements: ['EPA certifications', 'Safety protocols', 'Pet-safe treatments', 'Family protection', 'Licensed technicians'],
                  icon: '🛡️'
                },
                {
                  strategy: 'Expertise Demonstration',
                  description: 'Showcase knowledge and experience in pest identification and treatment.',
                  elements: ['Pest identification guides', 'Treatment explanations', 'Prevention tips', 'Educational content', 'Expert credentials'],
                  icon: '🎓'
                },
                {
                  strategy: 'Results Documentation',
                  description: 'Document successful treatments and customer satisfaction.',
                  elements: ['Before/after photos', 'Treatment timelines', 'Success stories', 'Customer testimonials', 'Guarantee information'],
                  icon: '📸'
                }
              ].map((strategy, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{strategy.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{strategy.strategy}</h3>
                  <p className="text-green-100 mb-4">{strategy.description}</p>
                  <ul className="space-y-2">
                    {strategy.elements.map((element, elementIndex) => (
                      <li key={elementIndex} className="text-sm text-green-100">
                        • {element}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Review Platform Coverage */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Complete Review Platform Coverage
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  platform: 'Google My Business',
                  importance: 'Most Critical',
                  features: ['Local search visibility', 'Map pack rankings', 'Customer photos', 'Q&A management'],
                  icon: '🔍'
                },
                {
                  platform: 'Better Business Bureau',
                  importance: 'High Trust Factor',
                  features: ['BBB accreditation', 'Complaint resolution', 'Trust badges', 'Industry credibility'],
                  icon: '🏆'
                },
                {
                  platform: 'Angie\'s List / Angi',
                  importance: 'Home Services Focus',
                  features: ['Home service leads', 'Verified reviews', 'Service awards', 'Project galleries'],
                  icon: '🏠'
                },
                {
                  platform: 'Nextdoor',
                  importance: 'Neighborhood Trust',
                  features: ['Local recommendations', 'Neighbor referrals', 'Community engagement', 'Local presence'],
                  icon: '🏘️'
                }
              ].map((platform, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                  <div className="text-4xl mb-4">{platform.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{platform.platform}</h3>
                  <p className="text-sm text-green-600 font-semibold mb-4">{platform.importance}</p>
                  <ul className="space-y-2">
                    {platform.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="text-sm text-gray-600">
                        • {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Seasonal Reputation Management */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seasonal Reputation Management
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  season: 'Spring',
                  focus: 'Prevention & Early Treatment',
                  messaging: 'Proactive pest prevention and early intervention services',
                  reviews: 'Highlight prevention success and early treatment effectiveness',
                  icon: '🌸'
                },
                {
                  season: 'Summer',
                  focus: 'Peak Activity Response',
                  messaging: 'Fast response to peak pest activity and outdoor threats',
                  reviews: 'Showcase rapid response and effective summer pest control',
                  icon: '☀️'
                },
                {
                  season: 'Fall',
                  focus: 'Winter Preparation',
                  messaging: 'Preparing homes for winter and preventing indoor invasions',
                  reviews: 'Emphasize thorough preparation and prevention strategies',
                  icon: '🍂'
                },
                {
                  season: 'Winter',
                  focus: 'Indoor Protection',
                  messaging: 'Protecting homes from indoor pests during cold months',
                  reviews: 'Highlight indoor pest control expertise and reliability',
                  icon: '❄️'
                }
              ].map((season, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                  <div className="text-4xl mb-4">{season.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{season.season}</h3>
                  <h4 className="text-lg font-medium text-green-600 mb-3">{season.focus}</h4>
                  <p className="text-sm text-gray-600 mb-4 italic">"{season.messaging}"</p>
                  <div className="bg-green-50 rounded-lg p-3">
                    <h5 className="font-semibold text-green-800 text-sm mb-1">Review Strategy:</h5>
                    <p className="text-green-700 text-xs">{season.reviews}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Review Response Templates */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Professional Review Response Templates
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Positive Review Responses:</h3>
                <div className="space-y-6">
                  <div className="bg-green-50 border-l-4 border-green-400 p-6 rounded-lg">
                    <h4 className="font-semibold text-green-800 mb-2">5-Star Termite Treatment</h4>
                    <p className="text-green-700 text-sm italic">
                      "Thank you, [Customer Name]! We're thrilled we could eliminate your termite problem effectively. 
                      Our team takes pride in thorough treatments and protecting your home's value. 
                      We appreciate your trust in [Company Name] and look forward to your annual inspections!"
                    </p>
                  </div>
                  <div className="bg-green-50 border-l-4 border-green-400 p-6 rounded-lg">
                    <h4 className="font-semibold text-green-800 mb-2">Emergency Wasp Removal</h4>
                    <p className="text-green-700 text-sm italic">
                      "We're so glad we could help with your wasp emergency safely and quickly! 
                      Thank you for choosing [Company Name] for your urgent pest control needs. 
                      Our technicians are always ready to protect your family from dangerous pests."
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Negative Review Responses:</h3>
                <div className="space-y-6">
                  <div className="bg-red-50 border-l-4 border-red-400 p-6 rounded-lg">
                    <h4 className="font-semibold text-red-800 mb-2">Treatment Effectiveness Concern</h4>
                    <p className="text-red-700 text-sm italic">
                      "We sincerely apologize that our initial treatment didn't meet your expectations, [Customer Name]. 
                      Pest control sometimes requires follow-up treatments, and we stand behind our guarantee. 
                      Please contact us at [phone] so we can schedule a complimentary re-treatment immediately."
                    </p>
                  </div>
                  <div className="bg-red-50 border-l-4 border-red-400 p-6 rounded-lg">
                    <h4 className="font-semibold text-red-800 mb-2">Safety Concerns</h4>
                    <p className="text-red-700 text-sm italic">
                      "Thank you for bringing this safety concern to our attention, [Customer Name]. 
                      The safety of your family and pets is our top priority. We'd like to discuss this immediately 
                      and ensure all safety protocols were followed. Please call us at [phone] today."
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Reputation Management Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Guardian Pest Control: From 3.1 to 4.8 Stars in 8 Months
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Guardian Pest Control was struggling with negative reviews from treatment delays and communication issues. 
                    Our comprehensive reputation management strategy transformed their online presence and customer trust.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">4.8★</div>
                      <div className="text-sm text-gray-600">Average Rating</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">280%</div>
                      <div className="text-sm text-gray-600">Lead Increase</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Reputation Transformation Results:</h4>
                  <ul className="space-y-3">
                    {[
                      'Improved from 3.1 to 4.8-star average rating',
                      '200+ new positive reviews generated',
                      '85% reduction in negative review impact',
                      '280% increase in qualified leads',
                      '45% improvement in conversion rates',
                      '92% customer satisfaction score'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Implementation Process */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Reputation Management Implementation Process
            </h2>
            <div className="grid md:grid-cols-5 gap-8">
              {[
                { step: '1', title: 'Reputation Audit', description: 'Comprehensive analysis of current online reputation', duration: '1 week' },
                { step: '2', title: 'Strategy Development', description: 'Create custom reputation management plan', duration: '1 week' },
                { step: '3', title: 'Platform Setup', description: 'Optimize profiles and implement monitoring', duration: '2 weeks' },
                { step: '4', title: 'Review Generation', description: 'Launch systematic review collection', duration: '2 weeks' },
                { step: '5', title: 'Ongoing Management', description: 'Continuous monitoring and optimization', duration: 'Ongoing' }
              ].map((phase, index) => (
                <div key={index} className="text-center">
                  <div className="bg-green-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                    {phase.step}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{phase.title}</h3>
                  <p className="text-gray-600 mb-2">{phase.description}</p>
                  <span className="text-sm text-green-600 font-semibold">{phase.duration}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Transform Your Pest Control Company's Reputation?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Get a free reputation audit and discover how we can help you build trust, 
              manage reviews, and attract more customers to your pest control business.
            </p>
            <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
              Get Your Free Reputation Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
