import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Web Design for Landscaping Companies | Convert Visitors to Customers | GroundUPDigital',
  description: 'Professional web design for landscaping companies that showcases your work and converts visitors into customers. Mobile-optimized, SEO-friendly landscaping websites.',
  keywords: 'landscaping web design, lawn care website design, landscape design websites, landscaping company websites',
  openGraph: {
    title: 'Web Design for Landscaping Companies | Convert Visitors to Customers | GroundUPDigital',
    description: 'Professional web design for landscaping companies that showcases your work and converts visitors into customers. Mobile-optimized, SEO-friendly landscaping websites.',
    type: 'website',
  },
};

export default function LandscapingWebDesignPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-blue-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Web Design for <span className="text-green-600">Landscaping Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Showcase your landscaping expertise with a professional website that converts visitors into customers. 
              Our landscaping-focused web design drives leads and grows your business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                View Website Examples
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                Get Free Design Consultation
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Website Features */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Essential Features for Landscaping Websites
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Portfolio Galleries',
                  description: 'Stunning before/after galleries that showcase your landscaping transformations.',
                  benefits: ['High-resolution image galleries', 'Before/after sliders', 'Project categorization', 'Mobile-optimized viewing'],
                  icon: '📸'
                },
                {
                  feature: 'Service Area Mapping',
                  description: 'Interactive maps showing your service areas and coverage zones.',
                  benefits: ['Interactive service maps', 'Location-based content', 'Local SEO optimization', 'Service area targeting'],
                  icon: '🗺️'
                },
                {
                  feature: 'Quote Request Forms',
                  description: 'Easy-to-use forms that capture leads and project details.',
                  benefits: ['Multi-step quote forms', 'Project detail capture', 'Automatic follow-up', 'Lead qualification'],
                  icon: '📋'
                },
                {
                  feature: 'Service Showcases',
                  description: 'Dedicated pages for each landscaping service you offer.',
                  benefits: ['Lawn care services', 'Landscape design', 'Tree services', 'Seasonal maintenance'],
                  icon: '🌿'
                },
                {
                  feature: 'Customer Reviews',
                  description: 'Prominent display of customer testimonials and reviews.',
                  benefits: ['Google review integration', 'Testimonial showcases', 'Star rating displays', 'Social proof elements'],
                  icon: '⭐'
                },
                {
                  feature: 'Emergency Contact',
                  description: 'Easy access to emergency landscaping services.',
                  benefits: ['Prominent contact buttons', 'Click-to-call functionality', 'Emergency service forms', '24/7 availability display'],
                  icon: '🚨'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg border border-gray-100">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.feature}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Design Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Our Landscaping Web Design Process
            </h2>
            <div className="grid lg:grid-cols-5 gap-8">
              {[
                {
                  step: '1',
                  title: 'Discovery',
                  description: 'Understand your landscaping business, services, and target customers.',
                  icon: '🔍'
                },
                {
                  step: '2',
                  title: 'Strategy',
                  description: 'Develop a website strategy focused on lead generation and conversions.',
                  icon: '📋'
                },
                {
                  step: '3',
                  title: 'Design',
                  description: 'Create beautiful, professional designs that showcase your work.',
                  icon: '🎨'
                },
                {
                  step: '4',
                  title: 'Development',
                  description: 'Build a fast, mobile-responsive website optimized for search engines.',
                  icon: '💻'
                },
                {
                  step: '5',
                  title: 'Launch',
                  description: 'Launch your website and provide ongoing support and optimization.',
                  icon: '🚀'
                }
              ].map((process, index) => (
                <div key={index} className="text-center">
                  <div className="bg-green-600 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    {process.step}
                  </div>
                  <div className="text-4xl mb-4">{process.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{process.title}</h3>
                  <p className="text-gray-600 text-sm">{process.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Conversion Optimization */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Conversion-Optimized for Landscaping Businesses
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">What Makes Our Websites Convert:</h3>
                <ul className="space-y-4">
                  {[
                    'Strategic placement of quote request forms on every page',
                    'Click-to-call buttons prominently displayed for mobile users',
                    'Trust signals like certifications, insurance, and reviews',
                    'Clear service descriptions with transparent pricing',
                    'Before/after galleries that demonstrate quality work',
                    'Local SEO optimization for "near me" searches',
                    'Fast loading speeds (under 3 seconds)',
                    'Mobile-first responsive design'
                  ].map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Typical Results:</h3>
                <div className="space-y-6">
                  {[
                    { metric: '300%', description: 'Increase in website leads', icon: '📈' },
                    { metric: '65%', description: 'Improvement in conversion rate', icon: '🎯' },
                    { metric: '45%', description: 'More phone calls from website', icon: '📞' },
                    { metric: '80%', description: 'Increase in quote requests', icon: '📋' }
                  ].map((result, index) => (
                    <div key={index} className="flex items-center bg-white p-4 rounded-lg shadow-lg">
                      <div className="text-3xl mr-4">{result.icon}</div>
                      <div>
                        <div className="text-2xl font-bold text-green-600">{result.metric}</div>
                        <div className="text-gray-600">{result.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Website Examples */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Landscaping Website Examples
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  type: 'Full-Service Landscaping',
                  features: ['Complete service offerings', 'Large project galleries', 'Commercial & residential', 'Multiple service areas'],
                  description: 'Perfect for established landscaping companies offering comprehensive services.'
                },
                {
                  type: 'Lawn Care Specialist',
                  features: ['Recurring service focus', 'Subscription pricing', 'Service scheduling', 'Customer portals'],
                  description: 'Ideal for lawn care companies focused on maintenance and recurring services.'
                },
                {
                  type: 'Landscape Design Studio',
                  features: ['Design portfolio showcase', 'Project process walkthrough', 'Consultation booking', 'Design inspiration galleries'],
                  description: 'Designed for landscape architects and design-focused businesses.'
                }
              ].map((example, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{example.type}</h3>
                  <p className="text-gray-600 mb-6">{example.description}</p>
                  <ul className="space-y-3 mb-6">
                    {example.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <button className="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    View Example
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Mobile Optimization */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Mobile-First Design for Landscaping
            </h2>
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Why Mobile Matters for Landscaping:</h3>
                <ul className="space-y-4 mb-8">
                  {[
                    '68% of landscaping searches happen on mobile devices',
                    '85% of customers call directly from mobile search results',
                    'Mobile users are 3x more likely to need immediate service',
                    'Google prioritizes mobile-friendly websites in search results'
                  ].map((stat, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{stat}</span>
                    </li>
                  ))}
                </ul>
                <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                  Test Your Current Website
                </button>
              </div>
              <div className="bg-gray-100 rounded-lg p-8">
                <h4 className="font-semibold text-gray-900 mb-4">Mobile Optimization Features:</h4>
                <ul className="space-y-3">
                  {[
                    'Touch-friendly navigation and buttons',
                    'Fast loading on mobile networks',
                    'Easy-to-read text without zooming',
                    'Optimized images for mobile viewing',
                    'Click-to-call phone numbers',
                    'Mobile-friendly contact forms',
                    'GPS integration for directions',
                    'Mobile payment options'
                  ].map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-gray-600">
                      <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready for a Website That Grows Your Business?
            </h2>
            <p className="text-xl text-green-100 mb-8">
              Get a free website consultation and see how our landscaping-focused web design can increase your leads and revenue.
            </p>
            <button className="bg-white text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Get Your Free Web Design Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
