import React from 'react';
import { Metadata } from 'next';
import { BlogDetailContent } from '@/components/blog/BlogDetailContent';

// Sample blog post data - in real app, this would come from API/database
const blogPost = {
  id: 1,
  title: '10 Local SEO Tips Every Landscaper Should Know in 2024',
  slug: 'local-seo-tips-landscapers-2024',
  excerpt: 'Discover the latest local SEO strategies that are helping landscaping companies dominate their local markets and generate more qualified leads.',
  content: `
    <h2 id="introduction">Introduction</h2>
    <p>Local SEO has become more crucial than ever for landscaping businesses. With 97% of consumers searching online for local services, your digital presence can make or break your business growth.</p>

    <h2 id="optimize-google-business-profile">1. Optimize Your Google Business Profile</h2>
    <p>Your Google Business Profile is your digital storefront. Make sure it's complete with accurate business information, high-quality photos, and regular updates.</p>

    <h3 id="key-elements">Key Elements to Include:</h3>
    <ul>
      <li>Complete business information (name, address, phone)</li>
      <li>High-quality photos of your work</li>
      <li>Regular posts and updates</li>
      <li>Customer reviews and responses</li>
    </ul>

    <h2 id="target-location-keywords">2. Target Location-Specific Keywords</h2>
    <p>Focus on keywords that include your service area. Instead of just "landscaping," target "landscaping services in [your city]" or "lawn care near me."</p>

    <h2 id="build-local-citations">3. Build Local Citations</h2>
    <p>Ensure your business information is consistent across all online directories and platforms. This builds trust with search engines and improves your local rankings.</p>

    <h2 id="encourage-reviews">4. Encourage Customer Reviews</h2>
    <p>Positive reviews are crucial for local SEO. Develop a system to consistently ask satisfied customers for reviews on Google, Yelp, and other relevant platforms.</p>

    <h2 id="create-location-content">5. Create Location-Specific Content</h2>
    <p>Write blog posts about local landscaping challenges, seasonal tips for your area, and showcase local projects. This helps you rank for location-specific searches.</p>
  `,
  author: 'Sarah Johnson',
  authorBio: 'Sarah is a digital marketing specialist with over 8 years of experience helping local service businesses grow their online presence.',
  date: '2024-01-15',
  category: 'SEO',
  tags: ['Local SEO', 'Landscaping', 'Digital Marketing', 'Google Business Profile'],
  readTime: '8 min read',
  featured: true,
  image: '/blog/local-seo-landscaping.jpg'
};



// Generate metadata for SEO
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  // In a real app, you'd fetch the blog post data here
  return {
    title: `${blogPost.title} | GroundUP Digital Blog`,
    description: blogPost.excerpt,
    keywords: blogPost.tags.join(', '),
    openGraph: {
      title: blogPost.title,
      description: blogPost.excerpt,
      type: 'article',
      publishedTime: blogPost.date,
      authors: [blogPost.author],
    },
  };
}

export default function BlogDetailPage({ params }: { params: { slug: string } }) {
  // In a real app, you would fetch the blog post based on the slug
  // For now, we'll use the sample data

  return <BlogDetailContent blogPost={blogPost} />;
}
