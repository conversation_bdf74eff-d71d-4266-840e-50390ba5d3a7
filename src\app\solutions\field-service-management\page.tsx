import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, MapPin, Clock, Truck, Users, Star, CheckCircle, Phone, Route, Wrench } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Field Service Management for Landscaping Businesses | GroundUP Digital',
  description: 'Comprehensive field operations management for landscaping crews. GPS tracking, route optimization, equipment management, and work order systems.',
  keywords: 'landscaping field service management, crew tracking software, landscaping operations management, field service software landscaping, crew management system',
};

const fieldFeatures = [
  {
    icon: MapPin,
    title: 'GPS Tracking & Route Optimization',
    description: 'Real-time crew location tracking and intelligent route planning for maximum efficiency.',
    benefits: ['Real-time GPS tracking', 'Optimized route planning', 'Fuel cost reduction', 'Arrival time estimates']
  },
  {
    icon: Clock,
    title: 'Time & Labor Management',
    description: 'Comprehensive time tracking and labor management for accurate project costing.',
    benefits: ['Mobile time clock', 'Project time tracking', 'Labor cost analysis', 'Overtime monitoring']
  },
  {
    icon: Truck,
    title: 'Equipment & Asset Tracking',
    description: 'Complete equipment management system for landscaping tools and machinery.',
    benefits: ['Equipment location tracking', 'Maintenance scheduling', 'Usage monitoring', 'Asset optimization']
  }
];

const managementModules = [
  {
    name: 'Work Order Management',
    description: 'Digital work orders with real-time updates and completion tracking',
    features: ['Digital work order creation', 'Photo documentation', 'Client signature capture', 'Progress updates', 'Material tracking', 'Quality control checklists']
  },
  {
    name: 'Crew Scheduling & Dispatch',
    description: 'Intelligent crew scheduling and automated dispatch system',
    features: ['Drag-and-drop scheduling', 'Skill-based assignments', 'Automated notifications', 'Schedule optimization', 'Emergency dispatch', 'Crew availability tracking']
  },
  {
    name: 'Inventory & Materials',
    description: 'Complete inventory management for landscaping materials and supplies',
    features: ['Real-time inventory tracking', 'Automatic reorder points', 'Material usage reporting', 'Vendor management', 'Cost tracking', 'Waste reduction analytics']
  },
  {
    name: 'Customer Communication',
    description: 'Automated customer updates and communication throughout service delivery',
    features: ['Arrival notifications', 'Service completion updates', 'Photo sharing', 'Feedback collection', 'Appointment reminders', 'Emergency communications']
  }
];

const packages = [
  {
    name: "Field Essentials",
    price: "$997",
    period: "/month",
    description: "Core field management tools for small landscaping crews",
    features: [
      "GPS tracking for up to 5 crews",
      "Basic work order management",
      "Time tracking & reporting",
      "Customer notifications",
      "Mobile crew app",
      "Basic reporting dashboard"
    ],
    popular: false
  },
  {
    name: "Operations Pro",
    price: "$1,997",
    period: "/month", 
    description: "Comprehensive field management for growing landscaping companies",
    features: [
      "Unlimited crew tracking",
      "Advanced scheduling & dispatch",
      "Equipment & inventory management",
      "Route optimization",
      "Advanced analytics & reporting",
      "Integration with business systems"
    ],
    popular: true
  },
  {
    name: "Enterprise Command",
    price: "$3,997",
    period: "/month",
    description: "Complete field operations platform for large landscaping organizations",
    features: [
      "Multi-location management",
      "Advanced workforce analytics",
      "Custom workflow automation",
      "API integrations",
      "Dedicated account management",
      "Custom feature development"
    ],
    popular: false
  }
];

const caseStudy = {
  company: "ProLawn Services",
  location: "Tampa, FL",
  results: [
    { metric: "Route Efficiency", improvement: "+45%" },
    { metric: "Fuel Costs", improvement: "-30%" },
    { metric: "Customer Satisfaction", improvement: "+85%" },
    { metric: "Administrative Time", improvement: "-60%" }
  ]
};

const operationalBenefits = [
  { benefit: "Reduced Fuel Costs", description: "Optimize routes to minimize travel time and fuel consumption" },
  { benefit: "Improved Productivity", description: "Better crew coordination and reduced downtime" },
  { benefit: "Enhanced Customer Service", description: "Real-time updates and accurate arrival times" },
  { benefit: "Better Cost Control", description: "Accurate time and material tracking for each project" },
  { benefit: "Compliance Management", description: "Automated safety checks and regulatory compliance" },
  { benefit: "Data-Driven Decisions", description: "Comprehensive analytics for operational optimization" }
];

export default function FieldServiceManagementPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Route className="w-4 h-4" />
              <span className="text-sm font-semibold">Field Service Management for Landscaping</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Streamline Field Operations for{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscaping Businesses
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Comprehensive field service management that helps landscaping business owners optimize crew operations, 
              reduce costs, and deliver exceptional customer service.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Route className="w-5 h-5 mr-2" />
                See System Demo
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                View ROI Calculator
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '45%', label: 'Route Efficiency' },
                { number: '30%', label: 'Fuel Savings' },
                { number: '60%', label: 'Admin Time Reduction' },
                { number: '80+', label: 'Systems Deployed' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Complete Field Management Solutions for Landscaping Operations
            </h2>
            <p className="text-lg text-gray-600">
              Our field service management system is designed specifically for landscaping business owners who want to 
              optimize crew efficiency, reduce operational costs, and improve customer satisfaction.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {fieldFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Operational Benefits */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Operational Benefits for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              See how field service management transforms landscaping business operations and profitability.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-6">
            {operationalBenefits.map((item, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="flex items-center mb-3">
                    <Wrench className="w-5 h-5 text-green-600 mr-2" />
                    <h3 className="text-lg font-bold text-gray-900">{item.benefit}</h3>
                  </div>
                  <p className="text-gray-600 text-sm">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Management Modules Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Comprehensive Management Modules
            </h2>
            <p className="text-lg text-gray-600">
              Complete field service management covering every aspect of landscaping operations.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {managementModules.map((module, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{module.name}</h3>
                  <p className="text-gray-600 mb-4">{module.description}</p>
                  <ul className="space-y-2">
                    {module.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Field Management Success Story: Operational Excellence
              </h2>
              <p className="text-lg text-gray-600">
                See how {caseStudy.company} used our field service management system to optimize operations and reduce costs
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A growing landscaping business in {caseStudy.location} with 15 crews needed better field operations management. 
                      Our comprehensive system transformed their efficiency and customer service delivery.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Read Full Case Study
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Field Service Management Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Comprehensive field management solutions designed specifically for landscaping business owners. 
              Choose the package that optimizes your field operations and crew efficiency.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Optimize Your Landscaping Field Operations?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a free field service management consultation and discover how to reduce costs, improve efficiency, 
              and enhance customer satisfaction for your landscaping business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Route className="w-5 h-5 mr-2" />
                Get Operations Consultation
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule Demo
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
