import React from 'react';
import { clsx } from 'clsx';

interface SectionProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
  as?: 'section' | 'div' | 'article' | 'aside';
  background?: 'white' | 'gray' | 'blue' | 'gradient' | 'transparent';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '4xl' | '6xl' | '7xl' | 'full';
  centered?: boolean;
}

export const Section: React.FC<SectionProps> = ({
  children,
  className,
  id,
  as: Component = 'section',
  background = 'transparent',
  padding = 'lg',
  maxWidth = '7xl',
  centered = true
}) => {
  const backgroundClasses = {
    white: 'bg-white',
    gray: 'bg-gray-50',
    blue: 'bg-blue-50',
    gradient: 'bg-gradient-to-br from-blue-50 via-white to-green-50',
    transparent: ''
  };

  const paddingClasses = {
    none: '',
    sm: 'py-8',
    md: 'py-12',
    lg: 'py-16',
    xl: 'py-20'
  };

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '4xl': 'max-w-4xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl',
    full: 'max-w-full'
  };

  return (
    <Component
      id={id}
      className={clsx(
        'relative',
        backgroundClasses[background],
        paddingClasses[padding],
        className
      )}
    >
      {centered ? (
        <div className={clsx(
          'container mx-auto px-4 sm:px-6 lg:px-8',
          maxWidthClasses[maxWidth]
        )}>
          {children}
        </div>
      ) : (
        children
      )}
    </Component>
  );
};

// Specialized section components
interface HeroSectionProps extends Omit<SectionProps, 'background' | 'padding'> {
  variant?: 'default' | 'centered' | 'split';
}

export const HeroSection: React.FC<HeroSectionProps> = ({
  children,
  className,
  variant = 'default',
  ...props
}) => {
  const variantClasses = {
    default: 'min-h-screen flex items-center',
    centered: 'min-h-screen flex items-center justify-center text-center',
    split: 'min-h-screen'
  };

  return (
    <Section
      background="gradient"
      padding="xl"
      className={clsx(
        variantClasses[variant],
        className
      )}
      {...props}
    >
      {children}
    </Section>
  );
};

interface ContentSectionProps extends SectionProps {
  title?: string;
  subtitle?: string;
  description?: string;
  headerCentered?: boolean;
}

export const ContentSection: React.FC<ContentSectionProps> = ({
  children,
  title,
  subtitle,
  description,
  headerCentered = true,
  className,
  ...props
}) => {
  return (
    <Section className={className} {...props}>
      {(title || subtitle || description) && (
        <div className={clsx(
          'mb-12',
          headerCentered && 'text-center'
        )}>
          {subtitle && (
            <p className="text-sm font-semibold text-blue-600 uppercase tracking-wide mb-2">
              {subtitle}
            </p>
          )}
          {title && (
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {title}
            </h2>
          )}
          {description && (
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {description}
            </p>
          )}
        </div>
      )}
      {children}
    </Section>
  );
};
