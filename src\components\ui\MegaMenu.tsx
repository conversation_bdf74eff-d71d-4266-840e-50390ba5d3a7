'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  Globe,
  MousePointer,
  Users,
  Code,
  Wifi,
  Zap,
  Shield,
  BarChart,
  Cloud,
  Smartphone,
  ArrowRight,
  ExternalLink,
  <PERSON>Cart,
  Palette,
  Route,
  Monitor
} from 'lucide-react';

// Icon mapping for dynamic icon rendering
const iconMap = {
  Search,
  Globe,
  MousePointer,
  Users,
  Code,
  Wifi,
  Zap,
  Shield,
  BarChart,
  Cloud,
  Smartphone,
  ShoppingCart,
  Palette,
  Route,
  Monitor
};



interface Service {
  name: string;
  href: string;
  description: string;
  icon: string;
}

interface ServiceCategory {
  title: string;
  description: string;
  services: Service[];
}

interface MegaMenuProps {
  landscapingServices: Record<string, ServiceCategory>;
  isOpen: boolean;
  onClose: () => void;
}

export const MegaMenu: React.FC<MegaMenuProps> = ({ landscapingServices, isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState('foundational');

  const categoryKeys = Object.keys(landscapingServices);
  const activeCategory = landscapingServices[activeTab];

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }

    // Arrow key navigation for service category tabs
    if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
      e.preventDefault();
      const currentIndex = categoryKeys.indexOf(activeTab);
      let nextIndex;

      if (e.key === 'ArrowDown') {
        nextIndex = currentIndex < categoryKeys.length - 1 ? currentIndex + 1 : 0;
      } else {
        nextIndex = currentIndex > 0 ? currentIndex - 1 : categoryKeys.length - 1;
      }

      setActiveTab(categoryKeys[nextIndex]);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2, ease: "easeOut" }}
          className="absolute top-full bg-white shadow-2xl border border-gray-100 rounded-xl mt-2 mega-menu overflow-hidden max-h-[75vh] md:max-h-[70vh] w-[95vw] max-w-[1000px] xl:max-w-[1100px]"
          style={{
            left: '50%',
            transform: 'translateX(-50%)',
            minWidth: '800px'
          }}
          onMouseLeave={onClose}
          onKeyDown={handleKeyDown}
          onClick={(e) => e.stopPropagation()}
          role="menu"
          aria-label="Solutions mega menu"
        >
          {/* Desktop & Tablet Mega Menu */}
          <div className="hidden md:block">
            <div className="w-full">
              {/* Two-Column Layout - Content Driven */}
              <div className="flex h-[400px] md:h-[420px] lg:h-[440px] py-2 md:py-3 bg-white rounded-xl shadow-sm w-full overflow-hidden">
                {/* Left Side - Vertical Category Tabs */}
                <div className="w-48 md:w-56 lg:w-64 bg-gradient-to-br from-slate-50 to-blue-50 border-r border-gray-200 rounded-l-xl flex-shrink-0 overflow-y-auto">
                  <div className="p-3 md:p-4">
                    <h3 className="text-base md:text-lg font-bold text-gray-900 mb-3 md:mb-4">Solutions Portfolio</h3>
                    <div className="space-y-1.5 md:space-y-2" role="tablist" aria-orientation="vertical">
                      {categoryKeys.map((key) => {
                        const category = landscapingServices[key];
                        const getIcon = () => {
                          if (key === 'foundational') return '🌱';
                          if (key === 'transformation') return '🚀';
                          return '⚡';
                        };
                        return (
                          <button
                            key={key}
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setActiveTab(key);
                            }}
                            onMouseDown={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                            className={`w-full p-3 lg:p-3.5 text-left transition-all duration-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 group cursor-pointer touch-target ${
                              activeTab === key
                                ? 'bg-white text-blue-600 shadow-lg border-l-4 border-blue-600'
                                : 'text-gray-700 hover:text-gray-900 hover:bg-white hover:shadow-md'
                            }`}
                            role="tab"
                            aria-selected={activeTab === key}
                            aria-controls={`panel-${key}`}
                            tabIndex={activeTab === key ? 0 : -1}
                            id={`tab-${key}`}
                          >
                            <div className="flex items-center gap-2 md:gap-3">
                              <span className="text-lg md:text-xl flex-shrink-0">
                                {getIcon()}
                              </span>
                              <div className="flex-1 min-w-0">
                                <div className="font-semibold text-xs md:text-sm mb-0.5 leading-tight">{category.title}</div>
                                <div className="text-xs text-gray-500">{category.services.length} solutions</div>
                              </div>
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>

                {/* Right Side - Services Content */}
                <div className="flex-1 p-2 md:p-3 lg:p-4 bg-white rounded-r-xl min-w-0 flex flex-col">
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={activeTab}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                      id={`panel-${activeTab}`}
                      role="tabpanel"
                      aria-labelledby={`tab-${activeTab}`}
                      className="flex flex-col h-full"
                    >
                      {/* Category Header - Compact */}
                      <div className="mb-3 md:mb-4">
                        <div className="flex items-center justify-between gap-3">
                          <div className="flex items-center gap-2 md:gap-3 flex-1 min-w-0">
                            <span className="text-xl md:text-2xl flex-shrink-0">
                              {activeTab === 'foundational' ? '🌱' : activeTab === 'transformation' ? '🚀' : '⚡'}
                            </span>
                            <div className="flex-1 min-w-0">
                              <h3 className="text-base md:text-lg lg:text-xl font-bold text-gray-900 mb-1 leading-tight">{activeCategory.title}</h3>
                              <p className="text-xs md:text-sm text-gray-600 leading-relaxed">{activeCategory.description}</p>
                            </div>
                          </div>
                          <Link
                            href="/solutions"
                            className="inline-flex items-center gap-1.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-3 py-1.5 md:py-2 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 font-semibold text-xs shadow-lg hover:shadow-xl transform hover:scale-105 touch-target flex-shrink-0"
                            onClick={onClose}
                          >
                            View All
                            <ExternalLink className="w-3 h-3" />
                          </Link>
                        </div>
                      </div>

                      {/* Solutions Grid - Compact */}
                      <div className="flex-1 overflow-y-auto">
                        <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-1.5 md:gap-2 pr-1">
                        {activeCategory.services.map((service, index) => {
                          const IconComponent = iconMap[service.icon as keyof typeof iconMap];
                          return (
                            <motion.div
                              key={service.name}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ duration: 0.3, delay: index * 0.05 }}
                            >
                              <Link
                                href={service.href}
                                onClick={onClose}
                                className="block p-2 md:p-2.5 rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-200 group h-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 bg-white hover:bg-blue-50 touch-target min-h-[50px] md:min-h-[55px]"
                                role="menuitem"
                                aria-label={`${service.name} - ${service.description}`}
                              >
                                <div className="flex items-start gap-2">
                                  <div className="bg-blue-50 p-1 rounded-md group-hover:bg-blue-100 transition-colors flex-shrink-0">
                                    {IconComponent && <IconComponent className="w-3 h-3 text-blue-600" />}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <h4 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors text-xs leading-tight">
                                      {service.name}
                                    </h4>
                                    <p className="text-xs text-gray-600 leading-relaxed line-clamp-2 mt-0.5">
                                      {service.description}
                                    </p>
                                  </div>
                                </div>
                              </Link>
                            </motion.div>
                          );
                        })}
                        </div>
                      </div>

                      {/* Bottom CTA - Compact */}
                      <div className="mt-2 pt-2 md:pt-3 border-t border-gray-200 bg-gray-50 -mx-2 md:-mx-3 px-2 md:px-3 py-2 md:py-3 rounded-b-xl flex-shrink-0">
                        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 mb-0.5 text-xs md:text-sm">Ready to transform your business?</h4>
                            <p className="text-xs text-gray-600 leading-relaxed">Get a free consultation tailored to your landscaping business</p>
                          </div>
                          <div className="flex gap-2 flex-shrink-0">
                            <Link
                              href="/why-groundup"
                              onClick={onClose}
                              className="flex items-center gap-1.5 bg-gray-600 text-white px-3 py-1.5 rounded-lg hover:bg-gray-700 transition-all duration-200 font-semibold focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 text-xs touch-target shadow-md"
                            >
                              Why GroundUP
                            </Link>
                            <Link
                              href="/partnership"
                              onClick={onClose}
                              className="flex items-center gap-1.5 bg-blue-600 text-white px-3 py-1.5 rounded-lg hover:bg-blue-700 transition-all duration-200 font-semibold focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-xs touch-target shadow-md"
                            >
                              Get Started
                              <ArrowRight className="w-3 h-3" />
                            </Link>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </AnimatePresence>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Solutions Menu */}
          <div className="md:hidden max-h-[75vh] overflow-y-auto">
            <div className="p-3 xs:p-4">
              <div className="space-y-2 xs:space-y-3">
                {categoryKeys.map((key) => {
                  const category = landscapingServices[key];
                  const getIcon = () => {
                    if (key === 'foundational') return '🌱';
                    if (key === 'transformation') return '🚀';
                    return '⚡';
                  };
                  return (
                    <div key={key} className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                      <Link
                        href="/solutions"
                        onClick={onClose}
                        className="flex items-center gap-3 p-3 xs:p-4 hover:bg-gradient-to-r hover:from-blue-50 hover:to-white transition-all duration-300 touch-target-lg min-h-[50px] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg"
                        aria-label={`View ${category.title} - ${category.services.length} solutions available`}
                      >
                        <span className="text-xl xs:text-2xl flex-shrink-0">
                          {getIcon()}
                        </span>
                        <div className="flex-1 min-w-0">
                          <div className="font-bold text-gray-900 mb-0.5 text-sm xs:text-base leading-tight">{category.title}</div>
                          <div className="text-xs text-gray-500">{category.services.length} solutions available</div>
                        </div>
                        <ArrowRight className="w-4 h-4 xs:w-5 xs:h-5 text-blue-600 flex-shrink-0" />
                      </Link>
                    </div>
                  );
                })}
              </div>

              <div className="mt-4 xs:mt-5 pt-3 xs:pt-4 border-t border-gray-200">
                <Link
                  href="/partnership"
                  onClick={onClose}
                  className="block w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white text-center py-2.5 xs:py-3 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 font-bold text-sm xs:text-base shadow-lg touch-target-lg"
                >
                  Start Your Partnership
                </Link>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
