'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  Globe, 
  MousePointer, 
  Users, 
  Code, 
  Wifi, 
  Zap, 
  Shield, 
  BarChart, 
  Cloud, 
  Smartphone,
  ArrowRight,
  ExternalLink
} from 'lucide-react';

// Icon mapping for dynamic icon rendering
const iconMap = {
  Search,
  Globe,
  MousePointer,
  Users,
  Code,
  Wifi,
  Zap,
  Shield,
  BarChart,
  Cloud,
  Smartphone,
};



interface Service {
  name: string;
  href: string;
  description: string;
  icon: string;
}

interface ServiceCategory {
  title: string;
  description: string;
  services: Service[];
}

interface MegaMenuProps {
  landscapingServices: Record<string, ServiceCategory>;
  isOpen: boolean;
  onClose: () => void;
}

export const MegaMenu: React.FC<MegaMenuProps> = ({ landscapingServices, isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState('foundational');

  const categoryKeys = Object.keys(landscapingServices);
  const activeCategory = landscapingServices[activeTab];

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }

    // Arrow key navigation for service category tabs
    if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
      e.preventDefault();
      const currentIndex = categoryKeys.indexOf(activeTab);
      let nextIndex;

      if (e.key === 'ArrowDown') {
        nextIndex = currentIndex < categoryKeys.length - 1 ? currentIndex + 1 : 0;
      } else {
        nextIndex = currentIndex > 0 ? currentIndex - 1 : categoryKeys.length - 1;
      }

      setActiveTab(categoryKeys[nextIndex]);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2, ease: "easeOut" }}
          className="absolute top-full left-0 right-0 bg-white shadow-2xl border border-gray-100 rounded-xl mt-2 mega-menu overflow-hidden max-w-[100vw] max-h-[85vh] md:max-h-[80vh]"
          onMouseLeave={onClose}
          onKeyDown={handleKeyDown}
          onClick={(e) => e.stopPropagation()}
          role="menu"
          aria-label="Industries mega menu"
        >
          {/* Desktop & Tablet Mega Menu */}
          <div className="hidden md:block">
            <div className="flex justify-center px-2 md:px-4 lg:px-6">
              {/* Two-Column Layout - Responsive Width */}
              <div className="flex h-[500px] md:h-[550px] lg:h-[580px] py-3 md:py-4 lg:py-6 bg-white rounded-xl shadow-sm max-w-[1100px] xl:max-w-[1200px] w-full overflow-hidden">
                {/* Left Side - Vertical Industry Tabs */}
                <div className="w-56 md:w-64 lg:w-72 bg-gradient-to-br from-slate-50 to-blue-50 border-r border-gray-200 rounded-l-xl flex-shrink-0 overflow-y-auto">
                  <div className="p-4 md:p-6">
                    <h3 className="text-lg md:text-xl font-bold text-gray-900 mb-4 md:mb-6">Solutions for Landscaping</h3>
                    <div className="space-y-2 md:space-y-3" role="tablist" aria-orientation="vertical">
                      {categoryKeys.map((key) => {
                        const category = landscapingServices[key];
                        return (
                          <button
                            key={key}
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setActiveTab(key);
                            }}
                            onMouseDown={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                            className={`w-full p-4 lg:p-5 text-left transition-all duration-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 group cursor-pointer touch-target ${
                              activeTab === key
                                ? 'bg-white text-blue-600 shadow-lg border-l-4 border-blue-600'
                                : 'text-gray-700 hover:text-gray-900 hover:bg-white hover:shadow-md'
                            }`}
                            role="tab"
                            aria-selected={activeTab === key}
                            aria-controls={`panel-${key}`}
                            tabIndex={activeTab === key ? 0 : -1}
                            id={`tab-${key}`}
                          >
                            <div className="flex items-center gap-3 md:gap-4">
                              <span className="text-xl md:text-2xl flex-shrink-0">
                                {key === 'foundational' ? '🌱' : '🚀'}
                              </span>
                              <div className="flex-1 min-w-0">
                                <div className="font-semibold text-sm md:text-base mb-1 leading-tight">{category.title}</div>
                                <div className="text-xs md:text-sm text-gray-500">{category.services.length} solutions</div>
                              </div>
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>

                {/* Right Side - Services Content */}
                <div className="flex-1 p-3 md:p-4 lg:p-6 bg-white rounded-r-xl min-w-0 flex flex-col">
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={activeTab}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                      id={`panel-${activeTab}`}
                      role="tabpanel"
                      aria-labelledby={`tab-${activeTab}`}
                      className="flex flex-col h-full"
                    >
                      {/* Category Header */}
                      <div className="mb-4 md:mb-6">
                        <div className="flex flex-col lg:flex-row items-start justify-between gap-3 md:gap-4">
                          <div className="flex-1 w-full lg:w-auto">
                            <div className="flex items-center gap-2 md:gap-3 mb-2 md:mb-3">
                              <span className="text-2xl md:text-3xl">
                                {activeTab === 'foundational' ? '🌱' : '🚀'}
                              </span>
                              <div className="flex-1 min-w-0">
                                <h3 className="text-lg md:text-xl lg:text-2xl font-bold text-gray-900 mb-1 leading-tight">{activeCategory.title}</h3>
                                <p className="text-xs md:text-sm text-gray-600 leading-relaxed">{activeCategory.description}</p>
                              </div>
                            </div>
                            <Link
                              href="/solutions"
                              className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-3 md:px-4 py-2 md:py-2.5 rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 font-semibold text-xs md:text-sm shadow-lg hover:shadow-xl transform hover:scale-105 touch-target"
                              onClick={onClose}
                            >
                              View All Solutions
                              <ExternalLink className="w-3 h-3 md:w-4 md:h-4" />
                            </Link>
                          </div>

                          {/* Category Image */}
                          <div className="w-full lg:w-40 xl:w-48 h-24 md:h-28 lg:h-32 rounded-lg overflow-hidden shadow-lg bg-gradient-to-br from-green-100 to-blue-100 flex-shrink-0 flex items-center justify-center">
                            <span className="text-4xl md:text-5xl">
                              {activeTab === 'foundational' ? '🌱' : '🚀'}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Solutions Grid - Enhanced */}
                      <div className="flex-1 overflow-y-auto mb-4 md:mb-6">
                        <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2 md:gap-3 pr-2">
                        {activeCategory.services.map((service, index) => {
                          const IconComponent = iconMap[service.icon as keyof typeof iconMap];
                          return (
                            <motion.div
                              key={service.name}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ duration: 0.3, delay: index * 0.1 }}
                            >
                              <Link
                                href={service.href}
                                onClick={onClose}
                                className="block p-2 md:p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-200 group h-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 bg-white hover:bg-blue-50 touch-target min-h-[60px]"
                                role="menuitem"
                                aria-label={`${service.name} - ${service.description}`}
                              >
                                <div className="flex items-start gap-2 md:gap-3">
                                  <div className="bg-blue-50 p-1 md:p-1.5 rounded-md group-hover:bg-blue-100 transition-colors flex-shrink-0">
                                    {IconComponent && <IconComponent className="w-3 h-3 md:w-4 md:h-4 text-blue-600" />}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <h4 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors text-xs md:text-sm leading-tight">
                                      {service.name}
                                    </h4>
                                    <p className="text-xs text-gray-600 leading-relaxed line-clamp-1 hidden lg:block mt-1">
                                      {service.description}
                                    </p>
                                  </div>
                                </div>
                              </Link>
                            </motion.div>
                          );
                        })}
                        </div>
                      </div>

                      {/* Bottom CTA - Always Visible */}
                      <div className="mt-auto pt-3 md:pt-4 border-t border-gray-200 bg-gray-50 -mx-3 md:-mx-4 lg:-mx-6 px-3 md:px-4 lg:px-6 py-3 md:py-4 rounded-b-xl flex-shrink-0">
                        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 mb-1 text-sm md:text-base">Ready to transform your landscaping business?</h4>
                            <p className="text-xs md:text-sm text-gray-600 leading-relaxed">Get a free digital growth strategy consultation tailored to your landscaping business</p>
                          </div>
                          <Link
                            href="/client-success"
                            onClick={onClose}
                            className="flex items-center gap-2 bg-blue-600 text-white px-4 md:px-6 py-2 md:py-3 rounded-lg hover:bg-blue-700 transition-all duration-200 font-semibold focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm md:text-base touch-target flex-shrink-0 shadow-md"
                          >
                            View Success Stories
                            <ArrowRight className="w-3 h-3 md:w-4 md:h-4" />
                          </Link>
                        </div>
                      </div>
                    </motion.div>
                  </AnimatePresence>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Solutions Menu */}
          <div className="md:hidden max-h-[85vh] overflow-y-auto">
            <div className="p-4 xs:p-6 max-w-[100vw] overflow-hidden">
              <div className="space-y-3 xs:space-y-4">
                {categoryKeys.map((key) => {
                  const category = landscapingServices[key];
                  return (
                    <div key={key} className="border border-gray-200 rounded-lg xs:rounded-xl overflow-hidden shadow-sm">
                      <Link
                        href="/solutions"
                        onClick={onClose}
                        className="flex items-center gap-3 xs:gap-4 p-4 xs:p-6 hover:bg-gradient-to-r hover:from-blue-50 hover:to-white transition-all duration-300 touch-target-lg min-h-[60px] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg"
                        aria-label={`View ${category.title} - ${category.services.length} solutions available`}
                      >
                        <span className="text-2xl xs:text-3xl flex-shrink-0">
                          {key === 'foundational' ? '🌱' : '🚀'}
                        </span>
                        <div className="flex-1 min-w-0">
                          <div className="font-bold text-gray-900 mb-1 text-base xs:text-lg leading-tight">{category.title}</div>
                          <div className="text-xs xs:text-sm text-gray-500">{category.services.length} solutions available</div>
                        </div>
                        <ArrowRight className="w-5 h-5 xs:w-6 xs:h-6 text-blue-600 flex-shrink-0" />
                      </Link>
                    </div>
                  );
                })}
              </div>

              <div className="mt-6 xs:mt-8 pt-4 xs:pt-6 border-t border-gray-200">
                <Link
                  href="/partnership"
                  onClick={onClose}
                  className="block w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white text-center py-3 xs:py-4 rounded-lg xs:rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 font-bold text-base xs:text-lg shadow-lg touch-target-lg"
                >
                  Start Your Partnership
                </Link>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
