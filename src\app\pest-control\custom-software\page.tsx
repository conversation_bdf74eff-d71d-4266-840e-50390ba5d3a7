import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Custom Software Development for Pest Control Companies | GroundUPDigital',
  description: 'Custom software solutions for pest control businesses. Build specialized tools for pest management, treatment tracking, compliance, and customer management.',
  keywords: 'pest control software development, custom exterminator apps, pest management software, compliance software',
  openGraph: {
    title: 'Custom Software Development for Pest Control Companies | GroundUPDigital',
    description: 'Custom software solutions for pest control businesses. Build specialized tools for pest management, treatment tracking, compliance, and customer management.',
    type: 'website',
  },
};

export default function PestControlCustomSoftwarePage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-yellow-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Custom Software for <span className="text-green-600">Pest Control Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Build specialized software solutions tailored to your pest control business needs. 
              From pest identification systems to compliance management, we create tools that give you a competitive advantage.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Discuss Your Project
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View Software Examples
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Custom Software Types */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Specialized Software Solutions for Pest Control
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  software: 'Pest Identification System',
                  description: 'AI-powered pest identification with treatment recommendations and protocols.',
                  features: ['Photo recognition', 'Pest database', 'Treatment protocols', 'Severity assessment', 'Prevention strategies'],
                  icon: '🔍'
                },
                {
                  software: 'Treatment Management Platform',
                  description: 'Comprehensive platform for managing treatments, chemicals, and effectiveness tracking.',
                  features: ['Treatment scheduling', 'Chemical tracking', 'Effectiveness monitoring', 'Follow-up automation', 'Compliance reporting'],
                  icon: '💉'
                },
                {
                  software: 'Compliance Management System',
                  description: 'Ensure regulatory compliance with automated documentation and reporting.',
                  features: ['EPA compliance', 'Chemical documentation', 'Safety protocols', 'Audit trails', 'Regulatory reporting'],
                  icon: '📜'
                },
                {
                  software: 'Customer Portal System',
                  description: 'Self-service portal for customers to manage their pest control services.',
                  features: ['Service scheduling', 'Treatment history', 'Payment processing', 'Communication tools', 'Pest education'],
                  icon: '💻'
                },
                {
                  software: 'Technician Management App',
                  description: 'Mobile app for technician coordination, documentation, and field operations.',
                  features: ['Job assignments', 'Service documentation', 'Photo capture', 'Chemical usage tracking', 'Customer communication'],
                  icon: '👨‍🔧'
                },
                {
                  software: 'Business Intelligence Dashboard',
                  description: 'Advanced analytics and reporting for pest control business optimization.',
                  features: ['Performance analytics', 'Treatment effectiveness', 'Revenue tracking', 'Customer insights', 'Predictive analytics'],
                  icon: '📊'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg border border-gray-100">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.software}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <button className="w-full mt-4 bg-green-600 text-white py-2 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Learn More
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* AI-Powered Pest Identification */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                AI-Powered Pest Identification Suite
              </h2>
              <p className="text-xl text-green-100">
                Advanced artificial intelligence for accurate pest identification and treatment recommendations.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  component: 'Image Recognition',
                  description: 'Advanced computer vision for accurate pest identification from photos.',
                  capabilities: ['95% accuracy rate', 'Real-time processing', 'Multiple angle analysis', 'Damage assessment', 'Species classification'],
                  icon: '📷'
                },
                {
                  component: 'Treatment Recommendations',
                  description: 'AI-powered treatment suggestions based on pest type and severity.',
                  capabilities: ['Treatment protocols', 'Chemical selection', 'Application methods', 'Safety considerations', 'Effectiveness prediction'],
                  icon: '🎯'
                },
                {
                  component: 'Learning System',
                  description: 'Machine learning that improves accuracy over time with usage.',
                  capabilities: ['Continuous learning', 'Regional adaptation', 'Seasonal adjustments', 'Treatment feedback', 'Performance optimization'],
                  icon: '🧠'
                }
              ].map((component, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{component.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{component.component}</h3>
                  <p className="text-green-100 mb-4">{component.description}</p>
                  <ul className="space-y-2">
                    {component.capabilities.map((capability, capIndex) => (
                      <li key={capIndex} className="text-sm text-green-100">
                        • {capability}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Development Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Our Custom Software Development Process
            </h2>
            <div className="grid md:grid-cols-6 gap-8">
              {[
                { step: '1', title: 'Discovery', description: 'Understand your pest control needs', duration: '1-2 weeks' },
                { step: '2', title: 'Planning', description: 'Create detailed project roadmap', duration: '1 week' },
                { step: '3', title: 'Design', description: 'UI/UX design and system architecture', duration: '2-3 weeks' },
                { step: '4', title: 'Development', description: 'Build your custom software solution', duration: '8-16 weeks' },
                { step: '5', title: 'Testing', description: 'Comprehensive testing and QA', duration: '2-3 weeks' },
                { step: '6', title: 'Launch', description: 'Deployment, training, and support', duration: 'Ongoing' }
              ].map((phase, index) => (
                <div key={index} className="text-center">
                  <div className="bg-green-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold mx-auto mb-4">
                    {phase.step}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{phase.title}</h3>
                  <p className="text-gray-600 mb-2 text-sm">{phase.description}</p>
                  <span className="text-xs text-green-600 font-semibold">{phase.duration}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Technology Stack */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Modern Technology Stack for Pest Control Software
            </h2>
            <div className="grid lg:grid-cols-4 gap-8">
              {[
                {
                  category: 'Frontend Development',
                  technologies: ['React/Next.js', 'Vue.js', 'Angular', 'React Native', 'Flutter'],
                  description: 'Modern, responsive interfaces for web and mobile applications.',
                  icon: '💻'
                },
                {
                  category: 'AI & Machine Learning',
                  technologies: ['TensorFlow', 'PyTorch', 'Computer Vision', 'Natural Language', 'Predictive Analytics'],
                  description: 'Advanced AI for pest identification and treatment optimization.',
                  icon: '🤖'
                },
                {
                  category: 'Backend Development',
                  technologies: ['Node.js', 'Python/Django', 'PHP/Laravel', 'Ruby on Rails', '.NET Core'],
                  description: 'Robust server-side solutions for data processing and API development.',
                  icon: '⚙️'
                },
                {
                  category: 'Cloud & DevOps',
                  technologies: ['AWS', 'Google Cloud', 'Azure', 'Docker', 'Kubernetes'],
                  description: 'Reliable cloud infrastructure and deployment automation.',
                  icon: '☁️'
                }
              ].map((stack, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4 text-center">{stack.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">{stack.category}</h3>
                  <p className="text-gray-600 mb-4 text-sm">{stack.description}</p>
                  <ul className="space-y-2">
                    {stack.technologies.map((tech, techIndex) => (
                      <li key={techIndex} className="text-sm text-gray-600 text-center">
                        {tech}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Integration Capabilities */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seamless Integration Capabilities
            </h2>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  category: 'Business Systems',
                  integrations: ['QuickBooks/Xero', 'Salesforce', 'HubSpot', 'Microsoft Office', 'Google Workspace'],
                  description: 'Connect with your existing business and productivity tools.',
                  icon: '🔗'
                },
                {
                  category: 'Compliance & Regulatory',
                  integrations: ['EPA Databases', 'State Regulations', 'Chemical Databases', 'Safety Protocols', 'Audit Systems'],
                  description: 'Integrate with regulatory systems and compliance databases.',
                  icon: '📜'
                },
                {
                  category: 'Weather & Environmental',
                  integrations: ['Weather APIs', 'Environmental Data', 'Seasonal Patterns', 'Climate Data', 'Pest Activity Tracking'],
                  description: 'Leverage environmental data for better pest management.',
                  icon: '🌍'
                }
              ].map((category, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4 text-center">{category.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">{category.category}</h3>
                  <p className="text-gray-600 mb-4">{category.description}</p>
                  <ul className="space-y-2">
                    {category.integrations.map((integration, intIndex) => (
                      <li key={intIndex} className="text-sm text-gray-600 text-center">
                        • {integration}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Custom Software Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    PestGuard Pro: Custom AI Identification System
                  </h3>
                  <p className="text-gray-600 mb-6">
                    PestGuard Pro needed a specialized pest identification system for their technicians. 
                    Our custom AI-powered solution improved accuracy and treatment effectiveness dramatically.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">95%</div>
                      <div className="text-sm text-gray-600">Identification Accuracy</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">60%</div>
                      <div className="text-sm text-gray-600">Faster Treatments</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Custom Software Features:</h4>
                  <ul className="space-y-3">
                    {[
                      'AI-powered pest identification with 95% accuracy',
                      'Real-time treatment recommendations',
                      'Automated compliance documentation',
                      'Mobile app for field technicians',
                      'Integrated chemical usage tracking',
                      'Customer portal with treatment history'
                    ].map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Models */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Flexible Development Pricing Models
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  model: 'Fixed Price Project',
                  description: 'Best for well-defined pest control software projects with clear requirements.',
                  benefits: ['Predictable costs', 'Clear deliverables', 'Fixed timeline', 'Comprehensive documentation'],
                  pricing: 'Starting at $30,000',
                  bestFor: 'Pest identification systems'
                },
                {
                  model: 'Time & Materials',
                  description: 'Flexible approach for complex pest control software with evolving requirements.',
                  benefits: ['Flexible scope', 'Iterative development', 'Regular feedback', 'Adaptable timeline'],
                  pricing: '$150-200/hour',
                  bestFor: 'Complex compliance platforms'
                },
                {
                  model: 'Dedicated Team',
                  description: 'Long-term partnership with dedicated developers for ongoing projects.',
                  benefits: ['Dedicated resources', 'Deep business knowledge', 'Ongoing support', 'Scalable team'],
                  pricing: '$8,000-15,000/month',
                  bestFor: 'Large-scale pest control platforms'
                }
              ].map((model, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg border border-gray-100">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{model.model}</h3>
                  <p className="text-gray-600 mb-6">{model.description}</p>
                  <ul className="space-y-3 mb-6">
                    {model.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                  <div className="border-t pt-6">
                    <div className="text-2xl font-bold text-green-600 mb-2">{model.pricing}</div>
                    <div className="text-sm text-gray-600 mb-4">Best for: {model.bestFor}</div>
                    <button className="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                      Get Quote
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Compliance & Security */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Compliance & Security for Pest Control Software
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Regulatory Compliance:</h3>
                <ul className="space-y-4">
                  {[
                    'EPA compliance for chemical usage and reporting',
                    'State and local regulatory requirements',
                    'OSHA safety protocol documentation',
                    'Chemical inventory and usage tracking',
                    'Technician certification management',
                    'Audit trail and documentation requirements',
                    'Environmental impact reporting',
                    'Customer safety and notification protocols'
                  ].map((compliance, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{compliance}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Security Features:</h3>
                <div className="space-y-6">
                  {[
                    { feature: 'Data Encryption', description: 'End-to-end encryption for all sensitive data', icon: '🔒' },
                    { feature: 'Access Control', description: 'Role-based access control and user permissions', icon: '👤' },
                    { feature: 'Audit Logging', description: 'Comprehensive audit trails for all system activities', icon: '📝' },
                    { feature: 'Backup & Recovery', description: 'Automated backups and disaster recovery plans', icon: '💾' }
                  ].map((security, index) => (
                    <div key={index} className="flex items-center bg-white p-4 rounded-lg shadow-lg">
                      <div className="text-3xl mr-4">{security.icon}</div>
                      <div>
                        <div className="text-lg font-semibold text-gray-900">{security.feature}</div>
                        <div className="text-gray-600">{security.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Build Your Custom Pest Control Software?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Let's discuss your unique business needs and create a custom software solution 
              that gives you a competitive advantage in the pest control industry.
            </p>
            <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
              Schedule Your Custom Software Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
