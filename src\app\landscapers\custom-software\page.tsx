import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Custom Software Development for Landscaping Companies | GroundUPDigital',
  description: 'Custom software solutions for landscaping businesses. Build specialized tools for project management, crew scheduling, equipment tracking, and customer management.',
  keywords: 'landscaping software development, custom landscaping apps, lawn care software, landscape design software',
  openGraph: {
    title: 'Custom Software Development for Landscaping Companies | GroundUPDigital',
    description: 'Custom software solutions for landscaping businesses. Build specialized tools for project management, crew scheduling, equipment tracking, and customer management.',
    type: 'website',
  },
};

export default function LandscapingCustomSoftwarePage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-blue-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Custom Software for <span className="text-green-600">Landscaping Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Build specialized software solutions tailored to your landscaping business needs. 
              From project management to crew scheduling, we create tools that give you a competitive advantage.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Discuss Your Project
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View Software Examples
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Custom Software Types */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Specialized Software Solutions for Landscapers
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  software: 'Project Management Suite',
                  description: 'Comprehensive project tracking from design to completion with client portals.',
                  features: ['Design visualization tools', 'Timeline management', 'Resource allocation', 'Client communication portal', 'Progress tracking'],
                  icon: '🏗️'
                },
                {
                  software: 'Crew Scheduling System',
                  description: 'Intelligent crew scheduling with skills matching and route optimization.',
                  features: ['Skills-based scheduling', 'Route optimization', 'Time tracking', 'Performance analytics', 'Mobile crew app'],
                  icon: '👥'
                },
                {
                  software: 'Equipment Management Platform',
                  description: 'Track equipment usage, maintenance schedules, and operational costs.',
                  features: ['Equipment tracking', 'Maintenance scheduling', 'Usage analytics', 'Cost tracking', 'Replacement planning'],
                  icon: '🚜'
                },
                {
                  software: 'Customer Portal System',
                  description: 'Self-service customer portal for scheduling, payments, and communication.',
                  features: ['Online scheduling', 'Payment processing', 'Service history', 'Communication tools', 'Feedback system'],
                  icon: '💻'
                },
                {
                  software: 'Inventory Management Tool',
                  description: 'Smart inventory tracking with automated reordering and supplier integration.',
                  features: ['Real-time inventory', 'Automated reordering', 'Supplier integration', 'Cost optimization', 'Usage forecasting'],
                  icon: '📦'
                },
                {
                  software: 'Financial Analytics Dashboard',
                  description: 'Advanced financial reporting and business intelligence for landscapers.',
                  features: ['Revenue analytics', 'Profit margins', 'Cost analysis', 'Forecasting', 'Custom reports'],
                  icon: '📊'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg border border-gray-100">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.software}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <button className="w-full mt-4 bg-green-600 text-white py-2 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Learn More
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Development Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Our Custom Software Development Process
            </h2>
            <div className="grid md:grid-cols-6 gap-8">
              {[
                { step: '1', title: 'Discovery', description: 'Understand your business needs and challenges', duration: '1-2 weeks' },
                { step: '2', title: 'Planning', description: 'Create detailed project roadmap and specifications', duration: '1 week' },
                { step: '3', title: 'Design', description: 'UI/UX design and system architecture planning', duration: '2-3 weeks' },
                { step: '4', title: 'Development', description: 'Build your custom software solution', duration: '8-16 weeks' },
                { step: '5', title: 'Testing', description: 'Comprehensive testing and quality assurance', duration: '2-3 weeks' },
                { step: '6', title: 'Launch', description: 'Deployment, training, and ongoing support', duration: 'Ongoing' }
              ].map((phase, index) => (
                <div key={index} className="text-center">
                  <div className="bg-green-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold mx-auto mb-4">
                    {phase.step}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{phase.title}</h3>
                  <p className="text-gray-600 mb-2 text-sm">{phase.description}</p>
                  <span className="text-xs text-green-600 font-semibold">{phase.duration}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Technology Stack */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Modern Technology Stack for Landscaping Software
            </h2>
            <div className="grid lg:grid-cols-4 gap-8">
              {[
                {
                  category: 'Frontend Development',
                  technologies: ['React/Next.js', 'Vue.js', 'Angular', 'React Native', 'Flutter'],
                  description: 'Modern, responsive user interfaces for web and mobile applications.',
                  icon: '💻'
                },
                {
                  category: 'Backend Development',
                  technologies: ['Node.js', 'Python/Django', 'PHP/Laravel', 'Ruby on Rails', '.NET Core'],
                  description: 'Robust server-side solutions for data processing and API development.',
                  icon: '⚙️'
                },
                {
                  category: 'Database & Storage',
                  technologies: ['PostgreSQL', 'MySQL', 'MongoDB', 'Redis', 'AWS S3'],
                  description: 'Scalable data storage solutions for your business information.',
                  icon: '🗄️'
                },
                {
                  category: 'Cloud & DevOps',
                  technologies: ['AWS', 'Google Cloud', 'Azure', 'Docker', 'Kubernetes'],
                  description: 'Reliable cloud infrastructure and deployment automation.',
                  icon: '☁️'
                }
              ].map((stack, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4 text-center">{stack.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">{stack.category}</h3>
                  <p className="text-gray-600 mb-4 text-sm">{stack.description}</p>
                  <ul className="space-y-2">
                    {stack.technologies.map((tech, techIndex) => (
                      <li key={techIndex} className="text-sm text-gray-600 text-center">
                        {tech}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Integration Capabilities */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Seamless Integration Capabilities
              </h2>
              <p className="text-xl text-green-100">
                Connect your custom software with existing business tools and third-party services.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  category: 'Business Systems',
                  integrations: ['QuickBooks/Xero', 'Salesforce', 'HubSpot', 'Microsoft Office', 'Google Workspace'],
                  description: 'Connect with your existing business and productivity tools.',
                  icon: '🔗'
                },
                {
                  category: 'Payment & Financial',
                  integrations: ['Stripe', 'PayPal', 'Square', 'Bank APIs', 'Credit Card Processing'],
                  description: 'Integrate payment processing and financial management systems.',
                  icon: '💳'
                },
                {
                  category: 'Maps & Weather',
                  integrations: ['Google Maps', 'Weather APIs', 'GPS Tracking', 'Route Optimization', 'Location Services'],
                  description: 'Leverage location and weather data for better service delivery.',
                  icon: '🌍'
                }
              ].map((category, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{category.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{category.category}</h3>
                  <p className="text-green-100 mb-4">{category.description}</p>
                  <ul className="space-y-2">
                    {category.integrations.map((integration, intIndex) => (
                      <li key={intIndex} className="text-sm text-green-100">
                        • {integration}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Custom Software Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Premier Landscapes: Custom Project Management Suite
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Premier Landscapes needed a specialized project management system for large commercial landscaping projects. 
                    Our custom solution streamlined their operations and improved client satisfaction.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">50%</div>
                      <div className="text-sm text-gray-600">Faster Project Delivery</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">90%</div>
                      <div className="text-sm text-gray-600">Client Satisfaction</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Custom Software Features:</h4>
                  <ul className="space-y-3">
                    {[
                      '3D landscape visualization tools',
                      'Real-time project timeline tracking',
                      'Automated client progress updates',
                      'Resource allocation optimization',
                      'Mobile crew management app',
                      'Integrated billing and invoicing'
                    ].map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Models */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Flexible Development Pricing Models
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  model: 'Fixed Price Project',
                  description: 'Best for well-defined projects with clear requirements and scope.',
                  benefits: ['Predictable costs', 'Clear deliverables', 'Fixed timeline', 'Comprehensive documentation'],
                  pricing: 'Starting at $25,000',
                  bestFor: 'Small to medium projects'
                },
                {
                  model: 'Time & Materials',
                  description: 'Flexible approach for evolving requirements and iterative development.',
                  benefits: ['Flexible scope', 'Iterative development', 'Regular feedback', 'Adaptable timeline'],
                  pricing: '$150-200/hour',
                  bestFor: 'Complex or evolving projects'
                },
                {
                  model: 'Dedicated Team',
                  description: 'Long-term partnership with dedicated developers for ongoing projects.',
                  benefits: ['Dedicated resources', 'Deep business knowledge', 'Ongoing support', 'Scalable team'],
                  pricing: '$8,000-15,000/month',
                  bestFor: 'Large-scale or ongoing development'
                }
              ].map((model, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg border border-gray-100">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{model.model}</h3>
                  <p className="text-gray-600 mb-6">{model.description}</p>
                  <ul className="space-y-3 mb-6">
                    {model.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                  <div className="border-t pt-6">
                    <div className="text-2xl font-bold text-green-600 mb-2">{model.pricing}</div>
                    <div className="text-sm text-gray-600 mb-4">Best for: {model.bestFor}</div>
                    <button className="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                      Get Quote
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Build Your Custom Landscaping Software?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Let's discuss your unique business needs and create a custom software solution 
              that gives you a competitive advantage in the landscaping industry.
            </p>
            <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
              Schedule Your Custom Software Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
