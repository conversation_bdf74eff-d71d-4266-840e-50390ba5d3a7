import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Reputation Management for Roofing Companies | Build Trust & Credibility | GroundUPDigital',
  description: 'Protect and enhance your roofing company\'s online reputation. Manage reviews, build trust, and attract more customers with professional reputation management services.',
  keywords: 'roofing reputation management, roofing reviews, online reputation, roofing company trust, Google reviews',
  openGraph: {
    title: 'Reputation Management for Roofing Companies | Build Trust & Credibility | GroundUPDigital',
    description: 'Protect and enhance your roofing company\'s online reputation. Manage reviews, build trust, and attract more customers with professional reputation management services.',
    type: 'website',
  },
};

export default function RoofingReputationManagementPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Reputation Management for <span className="text-blue-600">Roofing Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Protect and enhance your roofing company's online reputation. Build trust, manage reviews, 
              and attract more customers with professional reputation management that showcases your expertise.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Reputation Audit
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                View Success Stories
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Reputation Impact */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Why Reputation Matters for Roofing Companies
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '92%', label: 'Of customers read online reviews', icon: '👀' },
                { metric: '84%', label: 'Trust reviews as much as personal recommendations', icon: '🤝' },
                { metric: '68%', label: 'More likely to choose 5-star rated roofers', icon: '⭐' },
                { metric: '300%', label: 'More leads with strong online reputation', icon: '📈' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Reputation Management Services */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Comprehensive Reputation Management Services
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  service: 'Review Monitoring & Response',
                  description: 'Monitor all review platforms and respond professionally to every review.',
                  features: ['24/7 review monitoring', 'Professional response templates', 'Crisis management', 'Multi-platform coverage'],
                  icon: '👁️'
                },
                {
                  service: 'Review Generation',
                  description: 'Systematically generate positive reviews from satisfied customers.',
                  features: ['Automated review requests', 'Follow-up sequences', 'Multiple platform targeting', 'Timing optimization'],
                  icon: '⭐'
                },
                {
                  service: 'Negative Review Management',
                  description: 'Address negative reviews professionally and minimize their impact.',
                  features: ['Damage control strategies', 'Professional responses', 'Issue resolution', 'Reputation recovery'],
                  icon: '🛡️'
                },
                {
                  service: 'Online Presence Optimization',
                  description: 'Optimize your online presence across all platforms and directories.',
                  features: ['Google My Business optimization', 'Directory listings', 'Social media management', 'Content creation'],
                  icon: '🌐'
                },
                {
                  service: 'Crisis Communication',
                  description: 'Manage reputation crises and protect your brand during difficult times.',
                  features: ['Crisis response plans', 'Media management', 'Stakeholder communication', 'Damage mitigation'],
                  icon: '🚨'
                },
                {
                  service: 'Reputation Analytics',
                  description: 'Track and analyze your online reputation with detailed reporting.',
                  features: ['Reputation scoring', 'Competitor analysis', 'Trend monitoring', 'Performance reports'],
                  icon: '📊'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.service}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Review Platform Coverage */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Complete Review Platform Coverage
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  platform: 'Google My Business',
                  importance: 'Most Important',
                  features: ['Local search visibility', 'Map pack rankings', 'Customer photos', 'Q&A management'],
                  icon: '🔍'
                },
                {
                  platform: 'Better Business Bureau',
                  importance: 'High Trust Factor',
                  features: ['BBB accreditation', 'Complaint resolution', 'Trust badges', 'Industry credibility'],
                  icon: '🏆'
                },
                {
                  platform: 'Angie\'s List / Angi',
                  importance: 'Home Services Focus',
                  features: ['Home improvement leads', 'Verified reviews', 'Project galleries', 'Service awards'],
                  icon: '🏠'
                },
                {
                  platform: 'Facebook & Social Media',
                  importance: 'Social Proof',
                  features: ['Social engagement', 'Community building', 'Visual content', 'Local connections'],
                  icon: '📱'
                }
              ].map((platform, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                  <div className="text-4xl mb-4">{platform.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{platform.platform}</h3>
                  <p className="text-sm text-blue-600 font-semibold mb-4">{platform.importance}</p>
                  <ul className="space-y-2">
                    {platform.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="text-sm text-gray-600">
                        • {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Emergency Reputation Management */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Emergency Reputation Management for Roofing Companies
              </h2>
              <p className="text-xl text-blue-100">
                Protect your reputation during roofing emergencies and storm damage situations.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  scenario: 'Storm Damage Response',
                  description: 'Manage reputation during high-volume storm damage periods.',
                  strategies: ['Proactive communication', 'Expectation management', 'Progress updates', 'Transparency protocols'],
                  icon: '⛈️'
                },
                {
                  scenario: 'Project Delays',
                  description: 'Handle reputation impact when projects face unexpected delays.',
                  strategies: ['Early notification', 'Explanation protocols', 'Compensation offers', 'Recovery plans'],
                  icon: '⏰'
                },
                {
                  scenario: 'Quality Issues',
                  description: 'Address reputation concerns when quality issues arise.',
                  strategies: ['Immediate response', 'Corrective action', 'Follow-up verification', 'Relationship repair'],
                  icon: '🔧'
                }
              ].map((scenario, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{scenario.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{scenario.scenario}</h3>
                  <p className="text-blue-100 mb-4">{scenario.description}</p>
                  <ul className="space-y-2">
                    {scenario.strategies.map((strategy, strategyIndex) => (
                      <li key={strategyIndex} className="text-sm text-blue-100">
                        • {strategy}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Review Response Templates */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Professional Review Response Templates
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Positive Review Responses:</h3>
                <div className="space-y-6">
                  <div className="bg-green-50 border-l-4 border-green-400 p-6 rounded-lg">
                    <h4 className="font-semibold text-green-800 mb-2">5-Star Emergency Repair</h4>
                    <p className="text-green-700 text-sm italic">
                      "Thank you, [Customer Name]! We're thrilled we could help with your emergency roof repair. 
                      Our team takes pride in providing fast, reliable service when you need it most. 
                      We appreciate your trust in [Company Name] and look forward to serving you again!"
                    </p>
                  </div>
                  <div className="bg-green-50 border-l-4 border-green-400 p-6 rounded-lg">
                    <h4 className="font-semibold text-green-800 mb-2">Complete Roof Replacement</h4>
                    <p className="text-green-700 text-sm italic">
                      "We're so happy you're pleased with your new roof! Thank you for choosing [Company Name] 
                      for such an important investment. Our team worked hard to ensure quality workmanship 
                      and we're glad it shows. Thank you for the wonderful review!"
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Negative Review Responses:</h3>
                <div className="space-y-6">
                  <div className="bg-red-50 border-l-4 border-red-400 p-6 rounded-lg">
                    <h4 className="font-semibold text-red-800 mb-2">Addressing Delays</h4>
                    <p className="text-red-700 text-sm italic">
                      "We sincerely apologize for the delays you experienced, [Customer Name]. 
                      Weather and material availability created unexpected challenges, but we should have 
                      communicated better. We'd like to discuss how we can make this right. 
                      Please contact us at [phone] so we can resolve this personally."
                    </p>
                  </div>
                  <div className="bg-red-50 border-l-4 border-red-400 p-6 rounded-lg">
                    <h4 className="font-semibold text-red-800 mb-2">Quality Concerns</h4>
                    <p className="text-red-700 text-sm italic">
                      "Thank you for bringing this to our attention, [Customer Name]. 
                      This doesn't meet our quality standards and we want to make it right immediately. 
                      Our project manager will contact you today to schedule a time to address these issues. 
                      Your satisfaction is our priority."
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Reputation Management Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Summit Roofing: From 2.8 to 4.9 Stars in 6 Months
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Summit Roofing was struggling with negative reviews from storm damage delays. 
                    Our comprehensive reputation management strategy transformed their online presence and customer trust.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">4.9★</div>
                      <div className="text-sm text-gray-600">Average Rating</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">250%</div>
                      <div className="text-sm text-gray-600">Lead Increase</div>
                    </div>
                  </div>
                  <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Reputation Transformation Results:</h4>
                  <ul className="space-y-3">
                    {[
                      'Improved from 2.8 to 4.9-star average rating',
                      '150+ new positive reviews generated',
                      '90% reduction in negative review impact',
                      '250% increase in qualified leads',
                      '40% improvement in conversion rates',
                      '95% customer satisfaction score'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Transform Your Roofing Company's Reputation?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Get a free reputation audit and discover how we can help you build trust, 
              manage reviews, and attract more customers to your roofing business.
            </p>
            <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Get Your Free Reputation Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
