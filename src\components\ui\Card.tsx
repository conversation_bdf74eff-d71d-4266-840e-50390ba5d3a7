'use client';

import React from 'react';
import { clsx } from 'clsx';
import { MotionWrapper } from './MotionWrapper';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  padding?: 'sm' | 'md' | 'lg';
}

export const Card: React.FC<CardProps> = ({
  children,
  className,
  hover = true,
  padding = 'md'
}) => {
  const paddingClasses = {
    sm: 'p-3 xs:p-4',
    md: 'p-4 xs:p-6',
    lg: 'p-6 xs:p-8'
  };

  return (
    <MotionWrapper
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      whileHover={hover ? { y: -5, scale: 1.02 } : {}}
      className={clsx(
        'bg-white rounded-lg xs:rounded-xl shadow-lg border border-gray-100 overflow-hidden',
        paddingClasses[padding],
        hover && 'hover:shadow-xl transition-all duration-300 hover:-translate-y-1',
        className
      )}
    >
      {children}
    </MotionWrapper>
  );
};

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export const CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => (
  <div className={clsx('mb-3 xs:mb-4', className)}>
    {children}
  </div>
);

interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

export const CardContent: React.FC<CardContentProps> = ({ children, className }) => (
  <div className={clsx(className)}>
    {children}
  </div>
);

interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const CardFooter: React.FC<CardFooterProps> = ({ children, className }) => (
  <div className={clsx('mt-4 pt-4 border-t border-gray-100', className)}>
    {children}
  </div>
);
