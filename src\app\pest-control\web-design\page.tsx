import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Web Design for Pest Control Companies | Convert Visitors to Customers | GroundUPDigital',
  description: 'Professional web design for pest control companies that builds trust and converts visitors into customers. Emergency-focused, mobile-optimized pest control websites.',
  keywords: 'pest control web design, exterminator websites, pest management web design, pest control company websites',
  openGraph: {
    title: 'Web Design for Pest Control Companies | Convert Visitors to Customers | GroundUPDigital',
    description: 'Professional web design for pest control companies that builds trust and converts visitors into customers. Emergency-focused, mobile-optimized pest control websites.',
    type: 'website',
  },
};

export default function PestControlWebDesignPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-yellow-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Web Design for <span className="text-green-600">Pest Control Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Build trust and convert visitors into customers with professional pest control websites. 
              Our emergency-focused web design drives leads and establishes your credibility in the pest control industry.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                View Website Examples
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                Get Free Design Consultation
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Trust-Building Features */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Trust-Building Features for Pest Control Websites
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Emergency Contact Prominence',
                  description: 'Prominent emergency contact information and 24/7 availability messaging.',
                  benefits: ['Click-to-call buttons', '24/7 emergency messaging', 'Fast response guarantees', 'Emergency contact forms'],
                  icon: '🚨'
                },
                {
                  feature: 'Credentials & Certifications',
                  description: 'Display licenses, insurance, and industry certifications prominently.',
                  benefits: ['License displays', 'Insurance verification', 'Industry certifications', 'EPA compliance'],
                  icon: '🏆'
                },
                {
                  feature: 'Before/After Galleries',
                  description: 'Showcase your pest control work with high-quality project galleries.',
                  benefits: ['Treatment documentation', 'Problem identification', 'Solution showcases', 'Mobile-optimized viewing'],
                  icon: '📸'
                },
                {
                  feature: 'Customer Reviews',
                  description: 'Prominent display of customer testimonials and Google reviews.',
                  benefits: ['Google review integration', 'Video testimonials', 'Star rating displays', 'Review widgets'],
                  icon: '⭐'
                },
                {
                  feature: 'Safety Information',
                  description: 'Dedicated sections for safety protocols and family-friendly treatments.',
                  benefits: ['Safety protocols', 'Pet-safe treatments', 'Family protection info', 'Chemical safety data'],
                  icon: '🛡️'
                },
                {
                  feature: 'Service Area Mapping',
                  description: 'Clear service area maps and coverage information.',
                  benefits: ['Interactive maps', 'Coverage areas', 'Response time info', 'Local presence'],
                  icon: '🗺️'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg border border-gray-100">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.feature}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Emergency-Focused Design */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Emergency-Focused Website Design
              </h2>
              <p className="text-xl text-green-100">
                Capture emergency pest control leads with websites designed for urgent situations.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Immediate Contact Options',
                  description: 'Multiple ways for customers to reach you instantly during pest emergencies.',
                  elements: ['Large phone buttons', 'Emergency contact forms', 'Live chat integration', 'Text messaging options'],
                  icon: '📞'
                },
                {
                  feature: 'Pest Identification Tools',
                  description: 'Help customers identify pests and understand urgency levels.',
                  elements: ['Pest identification guides', 'Urgency level indicators', 'Treatment timelines', 'Prevention tips'],
                  icon: '🔍'
                },
                {
                  feature: 'Trust & Urgency Messaging',
                  description: 'Build trust while conveying urgency for pest emergencies.',
                  elements: ['24/7 availability', 'Licensed & insured badges', 'Fast response guarantees', 'Emergency testimonials'],
                  icon: '⚡'
                }
              ].map((feature, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{feature.feature}</h3>
                  <p className="text-green-100 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.elements.map((element, elementIndex) => (
                      <li key={elementIndex} className="text-sm text-green-100">
                        • {element}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Website Types */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Pest Control Website Types & Examples
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  type: 'Emergency Pest Control Specialist',
                  features: ['24/7 emergency messaging', 'Pest identification tools', 'Rapid response guarantees', 'Safety protocol displays'],
                  description: 'Perfect for pest control companies specializing in emergency treatments and urgent pest problems.',
                  bestFor: 'Emergency & urgent pest specialists'
                },
                {
                  type: 'Full-Service Pest Management',
                  features: ['Complete service offerings', 'Residential & commercial', 'Treatment galleries', 'Multiple contact options'],
                  description: 'Ideal for established pest control companies offering comprehensive pest management services.',
                  bestFor: 'Established pest control companies'
                },
                {
                  type: 'Eco-Friendly Pest Control',
                  features: ['Green treatment focus', 'Family-safe messaging', 'Pet-friendly solutions', 'Environmental certifications'],
                  description: 'Designed for pest control companies specializing in eco-friendly and family-safe treatments.',
                  bestFor: 'Eco-friendly pest control specialists'
                }
              ].map((type, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{type.type}</h3>
                  <p className="text-gray-600 mb-6">{type.description}</p>
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-700 mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {type.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                          <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4">
                    <span className="text-sm font-semibold text-green-800">Best For: </span>
                    <span className="text-sm text-green-700">{type.bestFor}</span>
                  </div>
                  <button className="w-full mt-4 bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    View Example
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Seasonal Optimization */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seasonal Website Optimization for Pest Control
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  season: 'Spring',
                  pests: ['Ants', 'Termites', 'Wasps', 'Spiders'],
                  focus: 'Prevention & Early Treatment',
                  messaging: 'Get ahead of pest problems with spring prevention services',
                  icon: '🌸'
                },
                {
                  season: 'Summer',
                  pests: ['Mosquitoes', 'Flies', 'Wasps', 'Ticks'],
                  focus: 'Outdoor Pest Control',
                  messaging: 'Protect your family from summer pests and outdoor threats',
                  icon: '☀️'
                },
                {
                  season: 'Fall',
                  pests: ['Rodents', 'Stink Bugs', 'Spiders', 'Cluster Flies'],
                  focus: 'Winter Preparation',
                  messaging: 'Seal your home before pests move in for winter',
                  icon: '🍂'
                },
                {
                  season: 'Winter',
                  pests: ['Mice', 'Rats', 'Cockroaches', 'Silverfish'],
                  focus: 'Indoor Pest Control',
                  messaging: 'Keep your home pest-free during the cold months',
                  icon: '❄️'
                }
              ].map((season, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                  <div className="text-4xl mb-4">{season.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{season.season}</h3>
                  <h4 className="text-lg font-medium text-green-600 mb-3">{season.focus}</h4>
                  <div className="mb-4">
                    <h5 className="font-semibold text-gray-700 mb-2">Common Pests:</h5>
                    <ul className="text-sm text-gray-600">
                      {season.pests.map((pest, pestIndex) => (
                        <li key={pestIndex}>{pest}</li>
                      ))}
                    </ul>
                  </div>
                  <p className="text-sm text-gray-600 italic">"{season.messaging}"</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Mobile Optimization */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Mobile-First Design for Pest Emergencies
            </h2>
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Why Mobile Matters for Pest Control:</h3>
                <ul className="space-y-4 mb-8">
                  {[
                    '85% of pest emergency searches happen on mobile devices',
                    '94% of customers call directly from mobile search results',
                    'Pest emergencies require immediate mobile access',
                    'Google prioritizes mobile-friendly websites in search results',
                    'Emergency pest searches spike 400% on mobile during infestations'
                  ].map((stat, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{stat}</span>
                    </li>
                  ))}
                </ul>
                <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                  Test Your Current Website
                </button>
              </div>
              <div className="bg-gray-100 rounded-lg p-8">
                <h4 className="font-semibold text-gray-900 mb-4">Mobile Optimization Features:</h4>
                <ul className="space-y-3">
                  {[
                    'Large, touch-friendly emergency contact buttons',
                    'Fast loading on mobile networks (under 3 seconds)',
                    'Easy-to-read text without zooming required',
                    'Optimized pest identification images',
                    'One-tap calling for emergency situations',
                    'Mobile-friendly contact and estimate forms',
                    'GPS integration for directions to your location',
                    'Swipe-friendly before/after galleries'
                  ].map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-gray-600">
                      <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Pest Control Website Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Guardian Pest Control: 600% Increase in Emergency Calls
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Guardian Pest Control was losing emergency calls to competitors with outdated website design. 
                    Our emergency-focused website redesign transformed their lead generation.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">600%</div>
                      <div className="text-sm text-gray-600">Emergency Call Increase</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">90%</div>
                      <div className="text-sm text-gray-600">Mobile Conversion Rate</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Website Results:</h4>
                  <ul className="space-y-3">
                    {[
                      '600% increase in emergency phone calls',
                      '90% mobile conversion rate improvement',
                      '400% more termite inspection requests',
                      '75% increase in recurring service signups',
                      '4.8-star average customer rating',
                      '50% faster page loading speeds'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready for a Website That Converts Pest Control Leads?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Get a free website consultation and see how our pest control-focused web design can increase your emergency leads and revenue.
            </p>
            <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
              Get Your Free Pest Control Web Design Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
