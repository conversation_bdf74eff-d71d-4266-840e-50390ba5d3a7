'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { AnimatePresence } from 'framer-motion';
import { Menu, X, ChevronDown } from 'lucide-react';
import { Button } from '../ui/Button';
import { MotionWrapper } from '../ui/MotionWrapper';
import { MegaMenu } from '../ui/MegaMenu';

// TypeScript interfaces
interface NavigationSubmenuItem {
  name: string;
  href: string;
}

interface NavigationItem {
  name: string;
  href: string;
  submenu?: NavigationSubmenuItem[];
  megaMenu?: boolean;
  industries?: any;
}

// Landscaping-specific services data for solutions menu
const landscapingServices = {
  foundational: {
    title: 'Foundational Growth',
    description: 'Essential digital marketing services for landscaping business growth',
    services: [
      {
        name: 'Hyper-Local SEO',
        href: '/solutions/seo',
        description: 'Dominate local search results',
        icon: 'Search'
      },
      {
        name: 'Business Websites',
        href: '/solutions/web-design',
        description: 'Convert visitors into customers',
        icon: 'Globe'
      },
      {
        name: 'Precision PPC',
        href: '/solutions/ppc',
        description: 'High-intent advertising campaigns',
        icon: 'MousePointer'
      },
      {
        name: 'Content Marketing',
        href: '/solutions/content-marketing',
        description: 'Authority-building content strategy',
        icon: 'BarChart'
      },
      {
        name: 'Reputation Management',
        href: '/solutions/reputation-management',
        description: 'Build trust and credibility',
        icon: 'Shield'
      }
    ]
  },
  transformation: {
    title: 'Digital Transformation',
    description: 'Advanced technologies for competitive advantages',
    services: [
      {
        name: 'AI Lead Generation',
        href: '/solutions/ai-lead-generation',
        description: 'Intelligent lead qualification',
        icon: 'Zap'
      },
      {
        name: 'AR/VR Visualization',
        href: '/solutions/ar-vr-visualization',
        description: 'Immersive client presentations',
        icon: 'Code'
      },
      {
        name: 'Smart IoT Systems',
        href: '/solutions/iot-integration',
        description: 'Connected landscape solutions',
        icon: 'Wifi'
      },
      {
        name: 'CRM & Sales Tools',
        href: '/solutions/crm-sales',
        description: 'Streamlined business systems',
        icon: 'Users'
      },
      {
        name: 'Market Intelligence',
        href: '/solutions/market-intelligence',
        description: 'Competitive insights & analytics',
        icon: 'BarChart'
      }
    ]
  },
  operational: {
    title: 'Operational Excellence',
    description: 'Streamline operations and enhance customer experience',
    services: [
      {
        name: 'Mobile Apps',
        href: '/solutions/mobile-app-development',
        description: 'Custom business operations apps',
        icon: 'Smartphone'
      },
      {
        name: 'Design Software',
        href: '/solutions/design-software-studio',
        description: 'Professional design tools',
        icon: 'Palette'
      },
      {
        name: 'Field Management',
        href: '/solutions/field-service-management',
        description: 'Crew & operations optimization',
        icon: 'Route'
      },
      {
        name: 'Customer Portals',
        href: '/solutions/customer-portals',
        description: 'Enhanced client relationships',
        icon: 'Monitor'
      },
      {
        name: 'E-commerce Platform',
        href: '/solutions/ecommerce-integration',
        description: 'Online sales & booking systems',
        icon: 'ShoppingCart'
      }
    ]
  }
};

const navigation = [
  { name: 'Home', href: '/' },
  {
    name: 'Solutions',
    href: '/solutions',
    megaMenu: true,
    landscapingServices: landscapingServices
  },
  {
    name: 'Why GroundUP',
    href: '/why-groundup',
    submenu: [
      { name: 'Our Approach', href: '/why-groundup' },
      { name: 'Landscaping Expertise', href: '/why-groundup/expertise' },
      { name: 'Technology Advantage', href: '/why-groundup/technology' },
      { name: 'Success Methodology', href: '/why-groundup/methodology' },
    ]
  },
  {
    name: 'Resources',
    href: '/resources',
    submenu: [
      { name: 'Industry Insights', href: '/resources/insights' },
      { name: 'Growth Guides', href: '/resources/guides' },
      { name: 'Landscaping Academy', href: '/resources/academy' },
      { name: 'ROI Calculator', href: '/resources/roi-calculator' },
    ]
  },
  { name: 'Partnership', href: '/partnership' },
];

export const Header: React.FC = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [megaMenuOpen, setMegaMenuOpen] = useState(false);

  // Close mobile menu when clicking outside or on escape
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setMobileMenuOpen(false);
        setActiveSubmenu(null);
        setMegaMenuOpen(false);
      }
    };

    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as Element;
      // Don't close if clicking inside the mega menu or navigation
      if (!target.closest('nav') && !target.closest('[role="menu"]')) {
        setActiveSubmenu(null);
        setMegaMenuOpen(false);
      }
    };

    if (mobileMenuOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden'; // Prevent background scroll
    } else {
      document.body.style.overflow = 'unset';
    }

    document.addEventListener('click', handleClickOutside);

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.removeEventListener('click', handleClickOutside);
      document.body.style.overflow = 'unset';
    };
  }, [mobileMenuOpen]);

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50 border-b border-gray-100">
      <nav className="container">
        <div className="flex justify-between items-center py-3 xs:py-4 sm:py-5 lg:py-6">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2 touch-target">
              <div className="text-lg xs:text-xl sm:text-2xl font-bold text-blue-600 hover:scale-105 transition-transform duration-200">
                GroundUP<span className="text-green-600">Digital</span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-2 xl:space-x-4 relative">
            {navigation.map((item) => (
              <div
                key={item.name}
                className="relative group"
                onMouseEnter={() => {
                  if (item.megaMenu) {
                    setMegaMenuOpen(true);
                    setActiveSubmenu(null);
                  } else if (item.submenu) {
                    setActiveSubmenu(item.name);
                    setMegaMenuOpen(false);
                  } else {
                    setActiveSubmenu(null);
                    setMegaMenuOpen(false);
                  }
                }}
                onMouseLeave={() => {
                  // Don't close mega menu on mouse leave from nav item
                  // It will be handled by the mega menu component itself
                  if (!item.megaMenu) {
                    setActiveSubmenu(null);
                  }
                }}
              >
                <Link
                  href={item.href}
                  className="flex items-center text-gray-700 hover:text-blue-600 font-medium transition-all duration-200 px-2 xl:px-3 py-2 rounded-lg hover:bg-blue-50 text-sm xl:text-base touch-target focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  aria-expanded={item.megaMenu ? megaMenuOpen : (item.submenu ? activeSubmenu === item.name : undefined)}
                  aria-haspopup={item.submenu || item.megaMenu ? "true" : undefined}
                >
                  {item.name}
                  {(item.submenu || item.megaMenu) && <ChevronDown className="ml-1 h-3 w-3 xl:h-4 xl:w-4 transition-transform group-hover:rotate-180" />}
                </Link>

                {/* Regular Submenu */}
                <AnimatePresence>
                  {item.submenu && activeSubmenu === item.name && !item.megaMenu && (
                    <MotionWrapper
                      initial={{ opacity: 0, y: 10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: 10, scale: 0.95 }}
                      className="absolute top-full left-0 mt-3 w-64 xl:w-72 bg-white rounded-xl shadow-2xl border border-gray-100 py-3 backdrop-blur-sm z-50"
                    >
                      <div className="px-3 py-2 border-b border-gray-100">
                        <h3 className="text-xs xl:text-sm font-semibold text-gray-900">{item.name}</h3>
                      </div>
                      <div className="max-h-96 overflow-y-auto">
                        {item.submenu.map((subitem) => (
                          <Link
                            key={subitem.name}
                            href={subitem.href}
                            className="flex items-center px-4 py-2 xl:py-3 text-xs xl:text-sm text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-green-50 hover:text-blue-600 transition-all duration-200 group touch-target"
                          >
                            <span className="w-1.5 h-1.5 xl:w-2 xl:h-2 bg-blue-400 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                            {subitem.name}
                          </Link>
                        ))}
                      </div>
                    </MotionWrapper>
                  )}
                </AnimatePresence>

              </div>
            ))}

          </div>

          {/* CTA Button */}
          <div className="hidden lg:flex">
            <Link href="/contact">
              <Button
                variant="primary"
                size="md"
                className="shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 text-xs xl:text-sm px-3 xl:px-6 py-2 xl:py-3"
              >
                <span className="hidden xl:inline">Get Free Consultation</span>
                <span className="xl:hidden">Free Audit</span>
              </Button>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="p-2 text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600 rounded-lg hover:bg-blue-50 transition-all duration-200 touch-target-lg"
              aria-label={mobileMenuOpen ? "Close mobile menu" : "Open mobile menu"}
              aria-expanded={mobileMenuOpen}
            >
              {mobileMenuOpen ? <X className="h-5 w-5 xs:h-6 xs:w-6" /> : <Menu className="h-5 w-5 xs:h-6 xs:w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <MotionWrapper
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden border-t border-gray-200 py-4 max-h-[calc(100vh-80px)] overflow-y-auto"
            >
              <div className="space-y-1">
                {navigation.map((item, index) => (
                  <div key={item.name} className="border-b border-gray-100 last:border-b-0 pb-3 last:pb-0">
                    {/* Handle mega menu and submenu items */}
                    {item.megaMenu ? (
                      <Link
                        href={item.href}
                        className="flex items-center justify-between text-gray-700 hover:text-blue-600 font-medium py-3 px-2 rounded-lg hover:bg-blue-50 transition-all duration-200 touch-target"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <span className="text-base">{item.name}</span>
                        <ChevronDown className="h-4 w-4" />
                      </Link>
                    ) : item.submenu ? (
                      <div>
                        <div
                          className="flex items-center justify-between text-gray-700 font-medium py-3 px-2 cursor-pointer"
                          onClick={() => {
                            setActiveSubmenu(activeSubmenu === item.name ? null : item.name);
                          }}
                        >
                          <span className="text-base">{item.name}</span>
                          <ChevronDown
                            className={`h-4 w-4 transition-transform ${
                              activeSubmenu === item.name ? 'rotate-180' : ''
                            }`}
                          />
                        </div>

                        {/* Mobile Submenu List */}
                        <AnimatePresence>
                          {activeSubmenu === item.name && (
                            <MotionWrapper
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              className="ml-4 mt-2 space-y-2"
                            >
                              {item.submenu.map((subItem) => (
                                <Link
                                  key={subItem.name}
                                  href={subItem.href}
                                  className="block p-3 rounded-md hover:bg-blue-50 transition-all duration-200 text-sm touch-target text-gray-600 hover:text-blue-600"
                                  onClick={() => {
                                    setMobileMenuOpen(false);
                                    setActiveSubmenu(null);
                                  }}
                                >
                                  {subItem.name}
                                </Link>
                              ))}
                            </MotionWrapper>
                          )}
                        </AnimatePresence>
                      </div>
                    ) : (
                      <Link
                        href={item.href}
                        className="flex items-center justify-between text-gray-700 hover:text-blue-600 font-medium py-3 px-2 rounded-lg hover:bg-blue-50 transition-all duration-200 touch-target"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <span className="text-base">{item.name}</span>
                        {item.submenu && (
                          <ChevronDown
                            className={`h-4 w-4 transition-transform ${
                              activeSubmenu === item.name ? 'rotate-180' : ''
                            }`}
                            onClick={(e) => {
                              e.preventDefault();
                              setActiveSubmenu(activeSubmenu === item.name ? null : item.name);
                            }}
                          />
                        )}
                      </Link>
                    )}

                    {/* Mobile Submenu */}
                    {item.submenu && (
                      <AnimatePresence>
                        {activeSubmenu === item.name && (
                          <MotionWrapper
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="ml-4 mt-2 space-y-1 max-h-64 overflow-y-auto"
                          >
                            {item.submenu.map((subitem) => (
                              <Link
                                key={subitem.name}
                                href={subitem.href}
                                className="flex items-center text-gray-600 hover:text-blue-600 py-2 px-3 rounded-md hover:bg-blue-50 transition-all duration-200 text-sm touch-target"
                                onClick={() => {
                                  setMobileMenuOpen(false);
                                  setActiveSubmenu(null);
                                }}
                              >
                                <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-3"></span>
                                {subitem.name}
                              </Link>
                            ))}
                          </MotionWrapper>
                        )}
                      </AnimatePresence>
                    )}
                  </div>
                ))}

                {/* Mobile CTA Button */}
                <div className="pt-6 px-2">
                  <Link href="/contact" onClick={() => setMobileMenuOpen(false)}>
                    <Button variant="primary" size="lg" className="w-full touch-target-lg">
                      Get Free Consultation
                    </Button>
                  </Link>
                </div>
              </div>
            </MotionWrapper>
          )}
        </AnimatePresence>
      </nav>

      {/* Solutions Menu - Positioned relative to entire header */}
      {megaMenuOpen && (
        <MegaMenu
          landscapingServices={landscapingServices}
          isOpen={megaMenuOpen}
          onClose={() => setMegaMenuOpen(false)}
        />
      )}
    </header>
  );
};
