import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, Smartphone, Users, Calendar, Camera, DollarSign, Star, CheckCircle, Phone, MapPin } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Mobile App Development for Landscaping Businesses | GroundUP Digital',
  description: 'Custom mobile apps that streamline landscaping business operations and enhance client experiences. Crew management, client communication, and project tracking.',
  keywords: 'landscaping mobile app, landscaping business app, crew management app, landscaping project tracking, field service app landscaping',
};

const appFeatures = [
  {
    icon: Users,
    title: 'Crew Management & Scheduling',
    description: 'Streamline your landscaping crew operations with intelligent scheduling and task management.',
    benefits: ['Real-time crew scheduling', 'Task assignment & tracking', 'Time clock integration', 'Performance analytics']
  },
  {
    icon: Camera,
    title: 'Project Documentation',
    description: 'Professional photo documentation and progress tracking for all landscaping projects.',
    benefits: ['Before/after photo capture', 'Progress timeline creation', 'Client photo sharing', 'Quality control checks']
  },
  {
    icon: DollarSign,
    title: 'Mobile Invoicing & Payments',
    description: 'Complete billing solution that lets you invoice and collect payments on-site.',
    benefits: ['On-site invoice generation', 'Mobile payment processing', 'Automatic payment reminders', 'Financial reporting']
  }
];

const appTypes = [
  {
    name: 'Crew Operations App',
    description: 'Internal app for landscaping crews to manage daily operations and communicate with office',
    features: ['GPS tracking & check-ins', 'Work order management', 'Equipment tracking', 'Time & material logging', 'Photo documentation', 'Crew communication']
  },
  {
    name: 'Client Experience App',
    description: 'Branded app for your landscaping clients to interact with your business',
    features: ['Service request submission', 'Project progress viewing', 'Maintenance reminders', 'Photo galleries', 'Invoice & payment access', 'Direct messaging']
  },
  {
    name: 'Sales & Estimating App',
    description: 'Mobile tool for landscaping sales teams to create estimates and close deals on-site',
    features: ['Property measurement tools', 'Instant estimate generation', 'Digital contract signing', 'Photo-based proposals', 'CRM integration', 'Follow-up automation']
  },
  {
    name: 'Maintenance Management App',
    description: 'Specialized app for landscaping maintenance operations and recurring services',
    features: ['Route optimization', 'Service checklists', 'Recurring schedule management', 'Client notifications', 'Equipment maintenance tracking', 'Seasonal planning']
  }
];

const packages = [
  {
    name: "Essential Mobile",
    price: "$2,997",
    period: "/month",
    description: "Core mobile app functionality for small to medium landscaping businesses",
    features: [
      "Single platform app (iOS or Android)",
      "Basic crew management features",
      "Photo documentation system",
      "Simple invoicing integration",
      "6 months development & launch",
      "Basic training & support"
    ],
    popular: false
  },
  {
    name: "Professional Suite",
    price: "$4,997",
    period: "/month", 
    description: "Comprehensive mobile solution for growing landscaping companies",
    features: [
      "Cross-platform app (iOS & Android)",
      "Advanced crew & project management",
      "Client-facing app features",
      "Payment processing integration",
      "CRM & business system integration",
      "Ongoing updates & optimization"
    ],
    popular: true
  },
  {
    name: "Enterprise Platform",
    price: "$7,997",
    period: "/month",
    description: "Custom mobile ecosystem for large landscaping operations",
    features: [
      "Multiple specialized apps",
      "Advanced analytics & reporting",
      "Multi-location support",
      "Custom feature development",
      "White-label client solutions",
      "Dedicated development team"
    ],
    popular: false
  }
];

const caseStudy = {
  company: "GreenFlow Landscapes",
  location: "Charlotte, NC",
  results: [
    { metric: "Operational Efficiency", improvement: "+65%" },
    { metric: "Client Satisfaction", improvement: "+85%" },
    { metric: "Invoice Processing Time", improvement: "-70%" },
    { metric: "Project Documentation", improvement: "+300%" }
  ]
};

export default function MobileAppDevelopmentPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Smartphone className="w-4 h-4" />
              <span className="text-sm font-semibold">Mobile Apps for Landscaping Businesses</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Custom Mobile Apps That Transform{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscaping Operations
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Streamline your landscaping business operations and enhance client experiences with custom mobile apps 
              designed specifically for landscaping business owners and their teams.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Smartphone className="w-5 h-5 mr-2" />
                See App Demo
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                View App Portfolio
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '65%', label: 'Efficiency Increase' },
                { number: '85%', label: 'Client Satisfaction' },
                { number: '70%', label: 'Time Savings' },
                { number: '40+', label: 'Apps Developed' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Mobile Solutions Built for Landscaping Business Success
            </h2>
            <p className="text-lg text-gray-600">
              Our mobile apps are designed specifically for landscaping business owners who want to streamline operations, 
              improve client communication, and grow their business through technology.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {appFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* App Types Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Specialized Apps for Every Landscaping Business Need
            </h2>
            <p className="text-lg text-gray-600">
              We develop different types of mobile applications to address specific operational needs of landscaping businesses.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {appTypes.map((appType, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{appType.name}</h3>
                  <p className="text-gray-600 mb-4">{appType.description}</p>
                  <ul className="space-y-2">
                    {appType.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Mobile App Success Story: Transforming Operations
              </h2>
              <p className="text-lg text-gray-600">
                See how {caseStudy.company} used our mobile app solution to streamline operations and enhance client satisfaction
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A growing landscaping business in {caseStudy.location} needed to streamline crew operations and improve client communication. 
                      Our custom mobile app solution transformed their daily operations and client relationships.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Read Full Case Study
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Mobile App Development Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Custom mobile app development designed specifically for landscaping business owners. 
              Choose the package that fits your operational needs and growth goals.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Streamline Your Landscaping Business with Mobile Technology?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a free mobile app consultation and discover how custom mobile solutions can transform your landscaping business operations.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Smartphone className="w-5 h-5 mr-2" />
                Get App Consultation
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule Demo
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
