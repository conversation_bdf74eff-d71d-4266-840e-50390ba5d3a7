import React from 'react';
import { Metadata } from 'next';
import { ServiceSchema, FAQSchema, BreadcrumbSchema } from '@/components/seo/Schema';
import { generateServiceSchema, generateFAQSchema, generateBreadcrumbSchema } from '@/lib/seo';

export const metadata: Metadata = {
  title: 'SEO for Landscaping Companies | Dominate Local Search | GroundUPDigital',
  description: 'Dominate local search results with our specialized SEO services for landscaping companies. Rank #1 for lawn care, landscape design, and tree services in your area.',
  keywords: 'landscaping SEO, lawn care SEO, landscape design SEO, tree service SEO, local landscaping marketing',
  openGraph: {
    title: 'SEO for Landscaping Companies | Dominate Local Search | GroundUPDigital',
    description: 'Dominate local search results with our specialized SEO services for landscaping companies. Rank #1 for lawn care, landscape design, and tree services in your area.',
    type: 'website',
  },
};

// FAQ data for landscaping SEO
const landscapingSEOFAQs = [
  {
    question: "How long does it take to see SEO results for my landscaping business?",
    answer: "Most landscaping businesses see initial improvements in local search rankings within 2-3 months, with significant results typically achieved within 4-6 months. Emergency and seasonal services often see faster results due to higher search intent."
  },
  {
    question: "What keywords should my landscaping business target for SEO?",
    answer: "We target high-value keywords like 'landscaping near me', 'lawn care service', 'landscape design', 'tree removal', and location-specific terms. We also optimize for seasonal keywords and emergency services to capture year-round demand."
  },
  {
    question: "How important is Google My Business for landscaping SEO?",
    answer: "Google My Business is crucial for landscaping companies as 78% of local mobile searches result in offline purchases. We optimize your GMB profile with photos, reviews, posts, and accurate business information to improve local visibility."
  },
  {
    question: "Can SEO help my landscaping business during slow seasons?",
    answer: "Yes! We create year-round SEO strategies that promote seasonal services like spring cleanup, summer maintenance, fall preparation, and winter planning. This helps maintain consistent lead flow throughout the year."
  },
  {
    question: "How do you measure SEO success for landscaping companies?",
    answer: "We track local search rankings, organic traffic, phone calls, quote requests, and actual customer conversions. Our clients typically see a 300-400% increase in qualified leads within 6 months."
  }
];

// Breadcrumb data
const breadcrumbs = [
  { name: 'Home', url: 'https://www.groundupdigital.com' },
  { name: 'Landscapers', url: 'https://www.groundupdigital.com/landscapers' },
  { name: 'SEO Services', url: 'https://www.groundupdigital.com/landscapers/seo' }
];

export default function LandscapingSEOPage() {
  const serviceSchema = generateServiceSchema(
    'SEO for Landscaping Companies',
    'Specialized SEO services for landscaping businesses to dominate local search results and attract more customers.',
    'Starting at $1,500/month'
  );
  const faqSchema = generateFAQSchema(landscapingSEOFAQs);
  const breadcrumbSchema = generateBreadcrumbSchema(breadcrumbs);

  return (
    <div className="min-h-screen bg-white">
      {/* Schema Markup */}
      <ServiceSchema data={serviceSchema} />
      <FAQSchema data={faqSchema} />
      <BreadcrumbSchema data={breadcrumbSchema} />
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-blue-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              SEO for <span className="text-green-600">Landscaping Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Dominate local search results and attract more customers with our specialized SEO services 
              designed specifically for landscaping, lawn care, and tree service businesses.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Get Free SEO Audit
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View SEO Results
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Landscaping SEO Results That Drive Business
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '#1', label: 'Local Search Rankings', icon: '🏆' },
                { metric: '400%', label: 'Increase in Organic Traffic', icon: '📈' },
                { metric: '85%', label: 'More Qualified Leads', icon: '🎯' },
                { metric: '6 Months', label: 'Average Time to Page 1', icon: '⏰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-green-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Landscaping Keywords */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              High-Value Landscaping Keywords We Target
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  category: 'Lawn Care Services',
                  keywords: ['lawn care near me', 'lawn mowing service', 'lawn maintenance', 'grass cutting service', 'lawn fertilization'],
                  searchVolume: '12,000+ monthly searches'
                },
                {
                  category: 'Landscape Design',
                  keywords: ['landscape design', 'landscaping ideas', 'garden design', 'landscape architect', 'outdoor design'],
                  searchVolume: '8,500+ monthly searches'
                },
                {
                  category: 'Tree Services',
                  keywords: ['tree removal', 'tree trimming', 'tree cutting', 'arborist near me', 'tree pruning'],
                  searchVolume: '15,000+ monthly searches'
                },
                {
                  category: 'Hardscaping',
                  keywords: ['patio installation', 'retaining wall', 'walkway installation', 'outdoor kitchen', 'fire pit installation'],
                  searchVolume: '6,200+ monthly searches'
                },
                {
                  category: 'Irrigation',
                  keywords: ['sprinkler system', 'irrigation installation', 'sprinkler repair', 'drip irrigation', 'lawn sprinklers'],
                  searchVolume: '9,800+ monthly searches'
                },
                {
                  category: 'Seasonal Services',
                  keywords: ['spring cleanup', 'fall cleanup', 'leaf removal', 'snow removal', 'winter prep'],
                  searchVolume: '11,500+ monthly searches'
                }
              ].map((category, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{category.category}</h3>
                  <p className="text-sm text-green-600 font-semibold mb-4">{category.searchVolume}</p>
                  <ul className="space-y-2">
                    {category.keywords.map((keyword, keywordIndex) => (
                      <li key={keywordIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        "{keyword}"
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* SEO Strategy */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Our Landscaping SEO Strategy
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  strategy: 'Local SEO Optimization',
                  description: 'Dominate local search results for landscaping services in your service area.',
                  tactics: ['Google My Business optimization', 'Local citation building', 'Location-based content', 'Local schema markup'],
                  icon: '📍'
                },
                {
                  strategy: 'Service-Specific Pages',
                  description: 'Dedicated pages for each landscaping service you offer to capture specific searches.',
                  tactics: ['Lawn care service pages', 'Landscape design galleries', 'Tree service content', 'Seasonal service pages'],
                  icon: '🌿'
                },
                {
                  strategy: 'Content Marketing',
                  description: 'Educational content that positions you as the local landscaping expert.',
                  tactics: ['Landscaping guides', 'Plant care tips', 'Seasonal advice', 'Before/after showcases'],
                  icon: '📝'
                },
                {
                  strategy: 'Technical SEO',
                  description: 'Ensure your website performs perfectly for both users and search engines.',
                  tactics: ['Site speed optimization', 'Mobile responsiveness', 'Schema markup', 'Core Web Vitals'],
                  icon: '⚙️'
                },
                {
                  strategy: 'Review & Reputation',
                  description: 'Build trust and authority through positive reviews and reputation management.',
                  tactics: ['Review generation campaigns', 'Review response management', 'Reputation monitoring', 'Trust signals'],
                  icon: '⭐'
                },
                {
                  strategy: 'Seasonal Optimization',
                  description: 'Optimize for seasonal landscaping trends and demand patterns.',
                  tactics: ['Spring cleanup campaigns', 'Summer maintenance focus', 'Fall preparation content', 'Winter service promotion'],
                  icon: '🍂'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg border-l-4 border-green-600">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.strategy}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.tactics.map((tactic, tacticIndex) => (
                      <li key={tacticIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {tactic}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Local SEO Focus */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Local SEO for Landscaping Companies
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Why Local SEO Matters:</h3>
                <ul className="space-y-4">
                  {[
                    '78% of local mobile searches result in offline purchases',
                    '46% of all Google searches are looking for local information',
                    '88% of consumers trust online reviews as much as personal recommendations',
                    'Local searches are 3x more likely to convert than general searches',
                    '72% of consumers visit a store within 5 miles of their location'
                  ].map((stat, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{stat}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Our Local SEO Process:</h3>
                <div className="space-y-6">
                  {[
                    { step: '1', title: 'Local Keyword Research', description: 'Identify high-value local landscaping keywords in your service area.' },
                    { step: '2', title: 'Google My Business Optimization', description: 'Optimize your GMB profile for maximum local visibility.' },
                    { step: '3', title: 'Local Citation Building', description: 'Build consistent NAP citations across local directories.' },
                    { step: '4', title: 'Location-Based Content', description: 'Create content targeting specific neighborhoods and areas.' },
                    { step: '5', title: 'Review Management', description: 'Generate and manage customer reviews for local authority.' }
                  ].map((process, index) => (
                    <div key={index} className="flex items-start">
                      <div className="bg-green-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-4 flex-shrink-0">
                        {process.step}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">{process.title}</h4>
                        <p className="text-gray-600 text-sm">{process.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Landscaping SEO Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    GreenScape Landscaping: From Page 3 to #1
                  </h3>
                  <p className="text-gray-600 mb-6">
                    GreenScape Landscaping was struggling to compete with larger companies and was buried on page 3 
                    of Google search results. Our comprehensive SEO strategy transformed their online presence.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">#1</div>
                      <div className="text-sm text-gray-600">Google Rankings</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">450%</div>
                      <div className="text-sm text-gray-600">Traffic Increase</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Results After 6 Months:</h4>
                  <ul className="space-y-3">
                    {[
                      'Ranking #1 for "landscaping services [city]"',
                      'Ranking #1 for "lawn care near me"',
                      'Ranking #2 for "landscape design [city]"',
                      '85% increase in phone calls',
                      '65% increase in quote requests',
                      '4.9-star average Google rating'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Dominate Local Search?
            </h2>
            <p className="text-xl text-green-100 mb-8">
              Get a free SEO audit and discover how we can get your landscaping business ranking #1 in local search results.
            </p>
            <button className="bg-white text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Get Your Free Landscaping SEO Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
