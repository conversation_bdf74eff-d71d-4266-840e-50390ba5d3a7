'use client';

import React from 'react';

interface MotionWrapperProps {
  children: React.ReactNode;
  as?: keyof JSX.IntrinsicElements;
  className?: string;
  initial?: any;
  animate?: any;
  whileInView?: any;
  whileHover?: any;
  whileTap?: any;
  viewport?: any;
  transition?: any;
  exit?: any;
  [key: string]: any;
}

export const MotionWrapper: React.FC<MotionWrapperProps> = ({
  children,
  as = 'div',
  className,
  initial,
  animate,
  whileInView,
  whileHover,
  whileTap,
  viewport,
  transition,
  exit,
  ...otherProps
}) => {
  const Component = as as any;

  // For now, just render without animations to get the site working
  return (
    <Component className={className} {...otherProps}>
      {children}
    </Component>
  );
};

// Pre-configured motion components for common use cases
export const FadeInUp: React.FC<{ children: React.ReactNode; className?: string; delay?: number }> = ({
  children,
  className,
  delay = 0
}) => (
  <div className={className}>
    {children}
  </div>
);

export const FadeInLeft: React.FC<{ children: React.ReactNode; className?: string; delay?: number }> = ({
  children,
  className,
  delay = 0
}) => (
  <div className={className}>
    {children}
  </div>
);

export const FadeInRight: React.FC<{ children: React.ReactNode; className?: string; delay?: number }> = ({
  children,
  className,
  delay = 0
}) => (
  <div className={className}>
    {children}
  </div>
);

export const ScaleOnHover: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className
}) => (
  <div className={className}>
    {children}
  </div>
);
