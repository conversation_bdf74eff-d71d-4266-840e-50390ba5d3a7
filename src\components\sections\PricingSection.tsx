'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Check, X, Star, TrendingUp, Users, Zap, Crown, Shield, Phone, Calendar } from 'lucide-react';
import { Section } from '@/components/ui/Section';
import { GlassCard, GradientCard } from '@/components/ui/GlassCard';
import { Button } from '@/components/ui/Button';

interface PricingTier {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    setup: number;
  };
  popular?: boolean;
  features: {
    included: string[];
    excluded?: string[];
  };
  idealFor: string[];
  results: string;
  icon: React.ComponentType<any>;
  color: 'blue' | 'green' | 'purple' | 'orange';
}

const pricingTiers: PricingTier[] = [
  {
    id: 'essential',
    name: 'Essential',
    description: 'Perfect for new businesses getting started with digital marketing',
    price: {
      monthly: 1500,
      setup: 500
    },
    features: {
      included: [
        'Professional website audit & optimization',
        'Google My Business setup & optimization',
        'Basic local SEO (10 keywords)',
        'Monthly performance reports',
        'Social media profile setup',
        'Online review monitoring',
        'Email support',
        'Mobile-responsive website updates'
      ],
      excluded: [
        'Google Ads management',
        'Content creation',
        'Advanced SEO',
        'Social media management'
      ]
    },
    idealFor: [
      'New local service businesses',
      'Companies with limited marketing budget',
      'Businesses wanting to establish online presence'
    ],
    results: 'Typically see 40-60% increase in online visibility within 3 months',
    icon: Shield,
    color: 'blue'
  },
  {
    id: 'growth',
    name: 'Growth',
    description: 'Comprehensive marketing for established businesses ready to scale',
    price: {
      monthly: 2500,
      setup: 750
    },
    popular: true,
    features: {
      included: [
        'Everything in Essential package',
        'Advanced local SEO (25 keywords)',
        'Google Ads management ($500 ad spend included)',
        'Content marketing (4 blog posts/month)',
        'Social media management (3 platforms)',
        'Lead generation landing pages',
        'Conversion tracking & analytics',
        'Bi-weekly strategy calls',
        'Priority phone & email support',
        'Competitor analysis & monitoring'
      ],
      excluded: [
        'Advanced PPC campaigns',
        'Video marketing',
        'Marketing automation'
      ]
    },
    idealFor: [
      'Growing service businesses',
      'Companies ready to invest in growth',
      'Businesses with seasonal demand patterns'
    ],
    results: 'Average 150-250% increase in qualified leads within 6 months',
    icon: TrendingUp,
    color: 'green'
  },
  {
    id: 'premium',
    name: 'Premium',
    description: 'Full-service digital marketing for market leaders',
    price: {
      monthly: 4000,
      setup: 1000
    },
    features: {
      included: [
        'Everything in Growth package',
        'Advanced PPC campaigns (Google, Bing, Facebook)',
        'Marketing automation & email sequences',
        'Video marketing & production',
        'Advanced analytics & custom reporting',
        'Reputation management & PR',
        'Landing page A/B testing',
        'Dedicated account manager',
        'Weekly strategy sessions',
        'Custom integrations & tools',
        'Priority support (24/7 emergency line)'
      ]
    },
    idealFor: [
      'Established market leaders',
      'Multi-location service providers',
      'Companies targeting aggressive growth'
    ],
    results: 'Typically achieve 300-500% ROI and market dominance within 12 months',
    icon: Crown,
    color: 'purple'
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    description: 'Custom solutions for large service provider networks',
    price: {
      monthly: 0, // Custom pricing
      setup: 0
    },
    features: {
      included: [
        'Custom strategy development',
        'Multi-location management',
        'Advanced reporting & dashboards',
        'Dedicated team of specialists',
        'Custom integrations & API access',
        'White-label solutions available',
        'Training & consultation services',
        'Priority development resources'
      ]
    },
    idealFor: [
      'Franchise networks',
      'Multi-location enterprises',
      'Companies with unique requirements'
    ],
    results: 'Custom ROI targets and KPIs based on business objectives',
    icon: Zap,
    color: 'orange'
  }
];

const addOns = [
  {
    name: 'Emergency Response Marketing',
    description: 'Rapid campaign deployment for storm damage, pest outbreaks, etc.',
    price: 500,
    period: 'per activation'
  },
  {
    name: 'Video Testimonial Production',
    description: 'Professional customer testimonial videos',
    price: 1200,
    period: 'per video'
  },
  {
    name: 'Advanced Analytics Dashboard',
    description: 'Custom reporting with real-time metrics',
    price: 300,
    period: 'per month'
  },
  {
    name: 'Additional Service Areas',
    description: 'Expand marketing to new geographic regions',
    price: 400,
    period: 'per area/month'
  }
];

export const PricingSection: React.FC = () => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('monthly');

  const getDiscountedPrice = (price: number) => {
    return billingCycle === 'annual' ? Math.round(price * 0.85) : price;
  };

  return (
    <Section id="pricing" className="py-20 bg-gradient-to-br from-blue-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Transparent <span className="gradient-text-blue">Pricing</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">
            Choose the perfect digital marketing package for your local service business. 
            No hidden fees, no long-term contracts, just results-driven marketing that grows your business.
          </p>
          
          {/* Billing Toggle */}
          <div className="flex items-center justify-center mb-8">
            <span className={`mr-3 ${billingCycle === 'monthly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
              Monthly
            </span>
            <button
              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'annual' : 'monthly')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                billingCycle === 'annual' ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  billingCycle === 'annual' ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`ml-3 ${billingCycle === 'annual' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
              Annual
            </span>
            {billingCycle === 'annual' && (
              <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                Save 15%
              </span>
            )}
          </div>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid lg:grid-cols-2 xl:grid-cols-4 gap-8 mb-16">
          {pricingTiers.map((tier, index) => (
            <motion.div
              key={tier.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className={`relative ${tier.popular ? 'lg:scale-105' : ''}`}
            >
              {tier.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-blue-600 to-green-600 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center">
                    <Star className="h-4 w-4 mr-1" />
                    Most Popular
                  </span>
                </div>
              )}
              
              <GlassCard className={`h-full ${tier.popular ? 'ring-2 ring-blue-500' : ''}`}>
                <div className="text-center mb-6">
                  <tier.icon className={`h-12 w-12 mx-auto mb-4 ${
                    tier.color === 'blue' ? 'text-blue-600' :
                    tier.color === 'green' ? 'text-green-600' :
                    tier.color === 'purple' ? 'text-purple-600' :
                    'text-orange-600'
                  }`} />
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{tier.name}</h3>
                  <p className="text-gray-600 text-sm mb-4">{tier.description}</p>
                  
                  {tier.price.monthly > 0 ? (
                    <div className="mb-4">
                      <div className="text-4xl font-bold text-gray-900">
                        ${getDiscountedPrice(tier.price.monthly).toLocaleString()}
                        <span className="text-lg text-gray-500 font-normal">/month</span>
                      </div>
                      {tier.price.setup > 0 && (
                        <div className="text-sm text-gray-500">
                          + ${tier.price.setup} setup fee
                        </div>
                      )}
                      {billingCycle === 'annual' && (
                        <div className="text-sm text-green-600 font-medium">
                          Save ${(tier.price.monthly * 12 * 0.15).toLocaleString()} annually
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-2xl font-bold text-gray-900 mb-4">
                      Custom Pricing
                    </div>
                  )}
                </div>

                {/* Features */}
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-3">What's Included:</h4>
                  <ul className="space-y-2">
                    {tier.features.included.map((feature, idx) => (
                      <li key={idx} className="flex items-start">
                        <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  {tier.features.excluded && (
                    <div className="mt-4">
                      <h5 className="font-medium text-gray-700 mb-2 text-sm">Not Included:</h5>
                      <ul className="space-y-1">
                        {tier.features.excluded.map((feature, idx) => (
                          <li key={idx} className="flex items-start">
                            <X className="h-4 w-4 text-gray-400 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-500 text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                {/* Ideal For */}
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-2">Ideal For:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {tier.idealFor.map((item, idx) => (
                      <li key={idx}>• {item}</li>
                    ))}
                  </ul>
                </div>

                {/* Expected Results */}
                <div className="mb-6 p-3 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-900 mb-1 text-sm">Expected Results:</h4>
                  <p className="text-blue-800 text-sm">{tier.results}</p>
                </div>

                {/* CTA Button */}
                <Button
                  variant={tier.popular ? "primary" : "outline"}
                  size="lg"
                  className="w-full"
                >
                  {tier.id === 'enterprise' ? 'Contact Sales' : 'Get Started'}
                  {tier.id === 'enterprise' ? <Phone className="ml-2 h-5 w-5" /> : <Calendar className="ml-2 h-5 w-5" />}
                </Button>
              </GlassCard>
            </motion.div>
          ))}
        </div>

        {/* Add-ons Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-16"
        >
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-8">
            Optional Add-Ons
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {addOns.map((addon, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <GlassCard className="text-center h-full">
                  <h4 className="font-bold text-gray-900 mb-2">{addon.name}</h4>
                  <p className="text-gray-600 text-sm mb-4">{addon.description}</p>
                  <div className="text-2xl font-bold text-blue-600">
                    ${addon.price}
                    <span className="text-sm text-gray-500 font-normal">/{addon.period}</span>
                  </div>
                </GlassCard>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* FAQ and Guarantee */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <GradientCard gradient="blue" className="max-w-4xl mx-auto text-center">
            <h3 className="text-3xl font-bold text-gray-900 mb-6">
              30-Day Money-Back Guarantee
            </h3>
            <p className="text-xl text-gray-600 mb-8">
              We're so confident in our results that we offer a 30-day money-back guarantee. 
              If you're not completely satisfied with our service, we'll refund your first month's payment.
            </p>
            <div className="grid md:grid-cols-3 gap-6 text-left">
              <div className="flex items-start">
                <Shield className="h-8 w-8 text-green-500 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">No Long-Term Contracts</h4>
                  <p className="text-gray-600 text-sm">Cancel anytime with 30 days notice</p>
                </div>
              </div>
              <div className="flex items-start">
                <Users className="h-8 w-8 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">Dedicated Support</h4>
                  <p className="text-gray-600 text-sm">Direct access to your account manager</p>
                </div>
              </div>
              <div className="flex items-start">
                <TrendingUp className="h-8 w-8 text-purple-500 mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">Proven Results</h4>
                  <p className="text-gray-600 text-sm">98% client satisfaction rate</p>
                </div>
              </div>
            </div>
          </GradientCard>
        </motion.div>
      </div>
    </Section>
  );
};
