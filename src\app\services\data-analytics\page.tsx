import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Data Analytics & Reporting for Local Service Businesses | GroundUPDigital',
  description: 'Make data-driven decisions with comprehensive analytics and reporting for landscapers, roofers, and pest control companies. Track ROI and optimize performance.',
  keywords: 'data analytics, marketing reporting, ROI tracking, business intelligence, performance analytics',
  openGraph: {
    title: 'Data Analytics & Reporting for Local Service Businesses | GroundUPDigital',
    description: 'Make data-driven decisions with comprehensive analytics and reporting for landscapers, roofers, and pest control companies. Track ROI and optimize performance.',
    type: 'website',
  },
};

export default function DataAnalyticsPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-green-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Data Analytics That <span className="text-blue-600">Drive Results</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Stop guessing and start knowing. Our comprehensive analytics and reporting solutions provide 
              the insights you need to make data-driven decisions and maximize your marketing ROI.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Analytics Setup
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                View Sample Reports
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Key Metrics Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Track What Matters Most
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  metric: 'Lead Generation',
                  description: 'Track lead sources, quality, and conversion rates across all channels.',
                  icon: '🎯',
                  kpis: ['Lead volume', 'Cost per lead', 'Lead quality score', 'Source attribution']
                },
                {
                  metric: 'Revenue Tracking',
                  description: 'Monitor revenue attribution and ROI from all marketing activities.',
                  icon: '💰',
                  kpis: ['Revenue per channel', 'Customer lifetime value', 'ROI by campaign', 'Profit margins']
                },
                {
                  metric: 'Customer Behavior',
                  description: 'Understand how customers interact with your brand and services.',
                  icon: '👥',
                  kpis: ['Website behavior', 'Engagement rates', 'Customer journey', 'Retention rates']
                },
                {
                  metric: 'Operational Efficiency',
                  description: 'Measure business performance and identify optimization opportunities.',
                  icon: '⚡',
                  kpis: ['Response times', 'Conversion rates', 'Service delivery', 'Team productivity']
                }
              ].map((category, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg border border-gray-100">
                  <div className="text-4xl mb-4">{category.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{category.metric}</h3>
                  <p className="text-gray-600 mb-4">{category.description}</p>
                  <ul className="space-y-2">
                    {category.kpis.map((kpi, kpiIndex) => (
                      <li key={kpiIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {kpi}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Analytics Tools */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Comprehensive Analytics Platform
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  tool: 'Custom Dashboards',
                  description: 'Real-time dashboards with the metrics that matter most to your business.',
                  features: ['Real-time data', 'Custom KPIs', 'Visual charts', 'Mobile access'],
                  icon: '📊'
                },
                {
                  tool: 'Automated Reports',
                  description: 'Scheduled reports delivered to your inbox with actionable insights.',
                  features: ['Weekly summaries', 'Monthly deep dives', 'Performance alerts', 'Trend analysis'],
                  icon: '📈'
                },
                {
                  tool: 'Lead Attribution',
                  description: 'Track every lead back to its original source and campaign.',
                  features: ['Multi-touch attribution', 'Source tracking', 'Campaign ROI', 'Channel performance'],
                  icon: '🔍'
                },
                {
                  tool: 'Conversion Tracking',
                  description: 'Monitor conversions across your entire customer journey.',
                  features: ['Goal tracking', 'Funnel analysis', 'A/B test results', 'Optimization insights'],
                  icon: '🎯'
                },
                {
                  tool: 'Competitor Analysis',
                  description: 'Benchmark your performance against local competitors.',
                  features: ['Market share', 'Competitive positioning', 'Opportunity gaps', 'Industry trends'],
                  icon: '⚔️'
                },
                {
                  tool: 'Predictive Analytics',
                  description: 'Forecast trends and identify opportunities before your competitors.',
                  features: ['Demand forecasting', 'Seasonal trends', 'Growth projections', 'Risk assessment'],
                  icon: '🔮'
                }
              ].map((tool, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{tool.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{tool.tool}</h3>
                  <p className="text-gray-600 mb-4">{tool.description}</p>
                  <ul className="space-y-2">
                    {tool.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Industry-Specific Analytics */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Industry-Specific Analytics
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  industry: 'Landscaping',
                  metrics: [
                    'Seasonal demand patterns and forecasting',
                    'Service profitability by type (lawn care vs. design)',
                    'Weather impact on lead generation',
                    'Customer retention by service category',
                    'Geographic performance mapping'
                  ]
                },
                {
                  industry: 'Roofing',
                  metrics: [
                    'Storm damage correlation with lead spikes',
                    'Insurance claim conversion tracking',
                    'Emergency vs. planned service profitability',
                    'Material cost impact on margins',
                    'Warranty claim analysis and prevention'
                  ]
                },
                {
                  industry: 'Pest Control',
                  metrics: [
                    'Seasonal pest activity and service demand',
                    'Treatment effectiveness and re-service rates',
                    'Recurring vs. one-time service profitability',
                    'Chemical usage and cost optimization',
                    'Customer satisfaction by pest type'
                  ]
                }
              ].map((industry, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg border-l-4 border-blue-600">
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">{industry.industry} Analytics</h3>
                  <ul className="space-y-3">
                    {industry.metrics.map((metric, metricIndex) => (
                      <li key={metricIndex} className="flex items-start text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span className="text-sm">{metric}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Reporting Features */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Advanced Reporting Features
            </h2>
            <div className="grid md:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">What We Track:</h3>
                <ul className="space-y-4">
                  {[
                    'Website traffic and user behavior',
                    'Lead generation and conversion rates',
                    'Campaign performance across all channels',
                    'Customer acquisition costs and lifetime value',
                    'Revenue attribution and ROI calculations',
                    'Competitor performance and market share',
                    'Seasonal trends and demand forecasting',
                    'Customer satisfaction and retention metrics'
                  ].map((item, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">How We Report:</h3>
                <ul className="space-y-4">
                  {[
                    'Real-time dashboards with live data updates',
                    'Automated weekly and monthly reports',
                    'Custom reports tailored to your business goals',
                    'Visual charts and graphs for easy understanding',
                    'Actionable insights and recommendations',
                    'Performance alerts for significant changes',
                    'Comparative analysis with previous periods',
                    'Executive summaries for quick decision making'
                  ].map((item, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Data-Driven Results
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                { metric: '35%', label: 'Improvement in ROI', icon: '📈' },
                { metric: '50%', label: 'Better Decision Making', icon: '🧠' },
                { metric: '25%', label: 'Cost Reduction', icon: '💰' },
                { metric: '90%', label: 'Client Satisfaction', icon: '⭐' }
              ].map((result, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{result.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{result.metric}</div>
                  <div className="text-gray-600">{result.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Make Data-Driven Decisions?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Get comprehensive analytics and reporting that help you optimize performance and maximize ROI.
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Start Your Analytics Setup
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
