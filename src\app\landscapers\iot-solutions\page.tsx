import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'IoT Solutions for Landscaping Companies | Smart Irrigation & Monitoring | GroundUPDigital',
  description: 'Transform your landscaping business with IoT solutions. Smart irrigation systems, soil monitoring, equipment tracking, and automated landscape management.',
  keywords: 'landscaping IoT, smart irrigation, soil monitoring, landscape automation, IoT sensors',
  openGraph: {
    title: 'IoT Solutions for Landscaping Companies | Smart Irrigation & Monitoring | GroundUPDigital',
    description: 'Transform your landscaping business with IoT solutions. Smart irrigation systems, soil monitoring, equipment tracking, and automated landscape management.',
    type: 'website',
  },
};

export default function LandscapingIoTPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-blue-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              IoT Solutions for <span className="text-green-600">Landscaping Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Transform your landscaping business with smart IoT technology. Monitor soil conditions, 
              automate irrigation, track equipment, and provide data-driven landscape management services.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Explore IoT Solutions
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                Request Demo
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* IoT Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Smart Technology for Modern Landscaping
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '40%', label: 'Water Usage Reduction', icon: '💧' },
                { metric: '60%', label: 'Maintenance Efficiency Increase', icon: '⚡' },
                { metric: '85%', label: 'Plant Health Improvement', icon: '🌱' },
                { metric: '300%', label: 'ROI Within 18 Months', icon: '💰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-green-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* IoT Solutions */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Comprehensive IoT Solutions for Landscapers
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  solution: 'Smart Irrigation Systems',
                  description: 'Automated irrigation with soil moisture sensors and weather integration.',
                  features: ['Soil moisture monitoring', 'Weather-based scheduling', 'Remote control', 'Water usage analytics', 'Zone-specific watering'],
                  icon: '💧'
                },
                {
                  solution: 'Soil Health Monitoring',
                  description: 'Real-time soil condition monitoring for optimal plant health.',
                  features: ['pH level monitoring', 'Nutrient analysis', 'Temperature tracking', 'Moisture levels', 'Fertilizer recommendations'],
                  icon: '🌱'
                },
                {
                  solution: 'Equipment Tracking',
                  description: 'GPS tracking and monitoring for all landscaping equipment.',
                  features: ['Real-time GPS tracking', 'Usage monitoring', 'Maintenance alerts', 'Theft protection', 'Fuel consumption tracking'],
                  icon: '🚜'
                },
                {
                  solution: 'Weather Monitoring',
                  description: 'Hyperlocal weather stations for precise landscape management.',
                  features: ['Temperature monitoring', 'Humidity tracking', 'Rainfall measurement', 'Wind speed detection', 'Frost alerts'],
                  icon: '🌤️'
                },
                {
                  solution: 'Plant Health Sensors',
                  description: 'Monitor individual plant health and growth conditions.',
                  features: ['Growth rate tracking', 'Disease detection', 'Stress monitoring', 'Light level measurement', 'Health alerts'],
                  icon: '🌿'
                },
                {
                  solution: 'Security & Surveillance',
                  description: 'Protect landscapes and equipment with smart security systems.',
                  features: ['Motion detection', 'Camera surveillance', 'Intrusion alerts', 'Remote monitoring', 'Night vision capability'],
                  icon: '🔒'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.solution}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Smart Irrigation Focus */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Smart Irrigation: The Future of Water Management
              </h2>
              <p className="text-xl text-green-100">
                Reduce water waste, improve plant health, and automate irrigation with intelligent systems.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Automated Scheduling',
                  description: 'AI-powered irrigation scheduling based on soil conditions and weather forecasts.',
                  benefits: ['Weather integration', 'Soil moisture triggers', 'Seasonal adjustments', 'Plant-specific watering', 'Water conservation'],
                  icon: '🤖'
                },
                {
                  feature: 'Remote Monitoring',
                  description: 'Monitor and control irrigation systems from anywhere using mobile apps.',
                  benefits: ['Mobile app control', 'Real-time alerts', 'System diagnostics', 'Usage reports', 'Emergency shutoff'],
                  icon: '📱'
                },
                {
                  feature: 'Water Analytics',
                  description: 'Detailed analytics on water usage, savings, and system performance.',
                  benefits: ['Usage tracking', 'Cost analysis', 'Efficiency reports', 'Leak detection', 'ROI calculations'],
                  icon: '📊'
                }
              ].map((feature, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{feature.feature}</h3>
                  <p className="text-green-100 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="text-sm text-green-100">
                        • {benefit}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Implementation Process */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              IoT Implementation Process
            </h2>
            <div className="grid md:grid-cols-5 gap-8">
              {[
                { step: '1', title: 'Site Assessment', description: 'Evaluate landscape and identify IoT opportunities', duration: '1 week' },
                { step: '2', title: 'System Design', description: 'Create custom IoT solution architecture', duration: '2 weeks' },
                { step: '3', title: 'Installation', description: 'Deploy sensors and IoT devices', duration: '1-2 weeks' },
                { step: '4', title: 'Integration', description: 'Connect systems and configure software', duration: '1 week' },
                { step: '5', title: 'Training', description: 'Train team and provide ongoing support', duration: 'Ongoing' }
              ].map((phase, index) => (
                <div key={index} className="text-center">
                  <div className="bg-green-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                    {phase.step}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{phase.title}</h3>
                  <p className="text-gray-600 mb-2">{phase.description}</p>
                  <span className="text-sm text-green-600 font-semibold">{phase.duration}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Technology Partners */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Leading IoT Technology Partners
            </h2>
            <div className="grid lg:grid-cols-4 gap-8">
              {[
                {
                  category: 'Sensor Technology',
                  partners: ['Bosch', 'Honeywell', 'Siemens', 'Texas Instruments', 'STMicroelectronics'],
                  description: 'High-quality sensors for accurate environmental monitoring.',
                  icon: '📡'
                },
                {
                  category: 'Connectivity',
                  partners: ['LoRaWAN', 'Sigfox', 'NB-IoT', 'WiFi', 'Cellular'],
                  description: 'Reliable connectivity solutions for all environments.',
                  icon: '📶'
                },
                {
                  category: 'Cloud Platforms',
                  partners: ['AWS IoT', 'Azure IoT', 'Google Cloud IoT', 'ThingWorx', 'IBM Watson'],
                  description: 'Scalable cloud platforms for data processing and analytics.',
                  icon: '☁️'
                },
                {
                  category: 'Analytics & AI',
                  partners: ['TensorFlow', 'Azure ML', 'AWS SageMaker', 'IBM Watson', 'Google AI'],
                  description: 'Advanced analytics and machine learning capabilities.',
                  icon: '🧠'
                }
              ].map((category, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4 text-center">{category.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">{category.category}</h3>
                  <p className="text-gray-600 mb-4 text-sm">{category.description}</p>
                  <ul className="space-y-1">
                    {category.partners.map((partner, partnerIndex) => (
                      <li key={partnerIndex} className="text-sm text-gray-600 text-center">
                        {partner}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              IoT Success Story: Smart Golf Course Management
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Greenfield Golf Club: 50% Water Savings with Smart IoT
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Greenfield Golf Club implemented our comprehensive IoT solution to optimize irrigation, 
                    monitor turf health, and reduce operational costs across their 18-hole course.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">50%</div>
                      <div className="text-sm text-gray-600">Water Usage Reduction</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">$75K</div>
                      <div className="text-sm text-gray-600">Annual Savings</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">IoT Implementation Results:</h4>
                  <ul className="space-y-3">
                    {[
                      '50% reduction in water usage',
                      '30% improvement in turf quality',
                      '40% decrease in maintenance costs',
                      '90% reduction in manual monitoring',
                      '24/7 automated system monitoring',
                      'Real-time weather integration'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* ROI Calculator */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Calculate Your IoT ROI
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">Potential Savings with IoT:</h3>
                  <div className="space-y-6">
                    {[
                      { category: 'Water Cost Reduction', savings: '30-50%', annual: '$5,000-15,000' },
                      { category: 'Labor Cost Savings', savings: '25-40%', annual: '$8,000-20,000' },
                      { category: 'Equipment Efficiency', savings: '20-35%', annual: '$3,000-10,000' },
                      { category: 'Plant Health Improvement', savings: '15-25%', annual: '$2,000-8,000' }
                    ].map((item, index) => (
                      <div key={index} className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                        <div>
                          <h4 className="font-semibold text-gray-900">{item.category}</h4>
                          <p className="text-sm text-gray-600">Typical savings: {item.savings}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-green-600">{item.annual}</div>
                          <div className="text-sm text-gray-600">per year</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">Investment Breakdown:</h3>
                  <div className="space-y-4 mb-8">
                    {[
                      { component: 'Sensor Hardware', cost: '$2,000-5,000' },
                      { component: 'Installation & Setup', cost: '$1,500-3,000' },
                      { component: 'Software Platform', cost: '$100-300/month' },
                      { component: 'Ongoing Support', cost: '$200-500/month' }
                    ].map((item, index) => (
                      <div key={index} className="flex justify-between items-center">
                        <span className="text-gray-700">{item.component}</span>
                        <span className="font-semibold text-gray-900">{item.cost}</span>
                      </div>
                    ))}
                  </div>
                  <div className="bg-green-50 rounded-lg p-6">
                    <h4 className="font-semibold text-green-800 mb-2">Typical ROI Timeline</h4>
                    <p className="text-green-700 text-sm mb-4">
                      Most landscaping businesses see full ROI within 12-18 months, 
                      with ongoing savings of $18,000-53,000 annually.
                    </p>
                    <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                      Get Custom ROI Analysis
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Transform Your Landscaping Business with IoT?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Discover how IoT solutions can reduce costs, improve efficiency, and give you 
              a competitive advantage in the landscaping industry.
            </p>
            <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
              Schedule Your IoT Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
