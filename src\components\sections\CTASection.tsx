'use client';

import React from 'react';
import {
  ArrowRight,
  Phone,
  Calendar,
  CheckCircle,
  Clock,
  Users,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Container, Section } from '@/components/ui/Container';
import { MotionWrapper } from '@/components/ui/MotionWrapper';

const benefits = [
  'Free 60-minute business growth consultation',
  'Custom landscaping business growth roadmap',
  'Market opportunity analysis report',
  'No obligation or pressure'
];

const urgencyPoints = [
  { icon: Clock, text: 'Limited partnership spots this quarter' },
  { icon: Users, text: 'Exclusive to landscaping business owners' },
  { icon: TrendingUp, text: 'Spring season preparation starts now' }
];

export const CTASection: React.FC = () => {
  return (
    <Section background="gradient" padding="xl" className="relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-transparent to-green-600/10" />
      <div className="absolute top-0 left-0 w-72 h-72 bg-blue-500/20 rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-0 w-72 h-72 bg-green-500/20 rounded-full blur-3xl" />
      
      <Container className="relative">
        <div className="max-w-4xl mx-auto">
          {/* Main CTA Content */}
          <MotionWrapper
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            {/* Badge */}
            <MotionWrapper
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium mb-6"
            >
              🌱 Ready to Transform Your Landscaping Business?
            </MotionWrapper>

            {/* Headline */}
            <MotionWrapper
              as="h2"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight"
            >
              Get Your Free Business Growth{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600">
                Strategy Session
              </span>
            </MotionWrapper>

            {/* Value Proposition */}
            <MotionWrapper
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.35 }}
              className="text-2xl font-semibold text-blue-600 mb-4"
            >
              Worth $3,500 - Yours FREE
            </MotionWrapper>

            {/* Subheadline */}
            <MotionWrapper
              as="p"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto"
            >
              Discover exactly how to generate more leads, dominate local search, and scale your
              landscaping business with our proven digital growth strategies designed exclusively for landscaping professionals.
              <span className="font-semibold text-gray-800"> No fluff, just actionable business growth insights.</span>
            </MotionWrapper>

            {/* Benefits List */}
            <MotionWrapper
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.5 }}
              className="grid md:grid-cols-2 gap-4 max-w-2xl mx-auto mb-8"
            >
              {benefits.map((benefit, index) => (
                <MotionWrapper
                  key={benefit}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                  className="flex items-center space-x-3 text-left"
                >
                  <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                  <span className="text-gray-700 font-medium">{benefit}</span>
                </MotionWrapper>
              ))}
            </MotionWrapper>

            {/* CTA Buttons */}
            <MotionWrapper
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.7 }}
              className="flex flex-col sm:flex-row gap-4 justify-center mb-8"
            >
              <a href="/partnership" className="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-lg hover:shadow-xl px-8 py-4 text-lg touch-target-lg group">
                <Calendar className="mr-2 h-5 w-5" />
                Book Free Growth Strategy Session
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
              </a>

              <a href="tel:+15551234567" className="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white focus:ring-blue-500 px-8 py-4 text-lg touch-target-lg group">
                <Phone className="mr-2 h-5 w-5" />
                Call (*************
              </a>
            </MotionWrapper>

            {/* Urgency Points */}
            <MotionWrapper
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="flex flex-col sm:flex-row items-center justify-center gap-6 text-sm text-gray-600"
            >
              {urgencyPoints.map((point, index) => {
                const Icon = point.icon;
                return (
                  <MotionWrapper
                    key={point.text}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.9 + index * 0.1 }}
                    className="flex items-center space-x-2"
                  >
                    <Icon className="h-4 w-4 text-orange-500" />
                    <span>{point.text}</span>
                  </MotionWrapper>
                );
              })}
            </MotionWrapper>
          </MotionWrapper>

          {/* Bottom Section - Process Preview */}
          <MotionWrapper
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="bg-white rounded-2xl shadow-xl p-8"
          >
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                What Happens Next?
              </h3>
              <p className="text-gray-600">
                Here's exactly what you can expect from your free strategy session
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  step: '01',
                  title: 'Discovery Call',
                  description: 'We analyze your current digital presence and identify opportunities for growth.',
                  icon: Phone
                },
                {
                  step: '02',
                  title: 'Custom Strategy',
                  description: 'Receive a tailored digital marketing roadmap specific to your industry and goals.',
                  icon: TrendingUp
                },
                {
                  step: '03',
                  title: 'Implementation',
                  description: 'If we\'re a good fit, we\'ll help you execute the strategy and start generating results.',
                  icon: CheckCircle
                }
              ].map((item, index) => {
                const Icon = item.icon;
                return (
                  <MotionWrapper
                    key={item.step}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: 0.6 + index * 0.2 }}
                    className="text-center"
                  >
                    <div className="relative mb-4">
                      <div className="w-16 h-16 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-2">
                        <Icon className="h-8 w-8" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {item.step}
                      </div>
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">
                      {item.title}
                    </h4>
                    <p className="text-gray-600 text-sm">
                      {item.description}
                    </p>
                  </MotionWrapper>
                );
              })}
            </div>
          </MotionWrapper>
        </div>
      </Container>
    </Section>
  );
};
