import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'IoT Solutions for Pest Control Companies | Smart Monitoring | GroundUPDigital',
  description: 'Transform pest control with IoT solutions. Smart traps, monitoring systems, and automated alerts for proactive pest management and customer service.',
  keywords: 'pest control IoT, smart pest traps, pest monitoring systems, IoT pest management, smart exterminator technology',
  openGraph: {
    title: 'IoT Solutions for Pest Control Companies | Smart Monitoring | GroundUPDigital',
    description: 'Transform pest control with IoT solutions. Smart traps, monitoring systems, and automated alerts for proactive pest management and customer service.',
    type: 'website',
  },
};

export default function PestControlIoTPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-yellow-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              IoT Solutions for <span className="text-green-600">Pest Control Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Transform your pest control business with Internet of Things (IoT) technology. 
              Smart monitoring systems, automated traps, and real-time alerts for proactive pest management and superior customer service.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Explore IoT Solutions
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View Technology Demo
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* IoT Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              IoT Technology Transforms Pest Control Operations
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '75%', label: 'Reduction in Service Calls', icon: '📞' },
                { metric: '90%', label: 'Faster Pest Detection', icon: '🔍' },
                { metric: '85%', label: 'Improvement in Customer Satisfaction', icon: '😊' },
                { metric: '600%', label: 'ROI Within Two Years', icon: '💰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-green-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* IoT Solutions */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Smart IoT Solutions for Pest Control
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  solution: 'Smart Pest Traps',
                  description: 'Intelligent traps with sensors that detect pest activity and send real-time alerts.',
                  features: ['Motion detection', 'Real-time alerts', 'Battery monitoring', 'Trap status updates', 'Activity analytics'],
                  icon: '🪤'
                },
                {
                  solution: 'Environmental Monitoring',
                  description: 'Monitor temperature, humidity, and conditions that attract pests.',
                  features: ['Temperature sensors', 'Humidity monitoring', 'Air quality tracking', 'Weather integration', 'Predictive alerts'],
                  icon: '🌡️'
                },
                {
                  solution: 'Smart Bait Stations',
                  description: 'Connected bait stations that monitor consumption and effectiveness.',
                  features: ['Consumption tracking', 'Bait level monitoring', 'Effectiveness analytics', 'Refill alerts', 'Activity patterns'],
                  icon: '🎯'
                },
                {
                  solution: 'Perimeter Monitoring',
                  description: 'IoT sensors around property perimeters to detect pest entry points.',
                  features: ['Entry point detection', 'Perimeter alerts', 'Movement tracking', 'Breach notifications', 'Pattern analysis'],
                  icon: '🛡️'
                },
                {
                  solution: 'Indoor Air Quality Sensors',
                  description: 'Monitor indoor conditions that may indicate pest problems.',
                  features: ['Air quality monitoring', 'Chemical detection', 'Odor sensors', 'Contamination alerts', 'Health monitoring'],
                  icon: '💨'
                },
                {
                  solution: 'Automated Dispensers',
                  description: 'Smart dispensers for treatments and repellents with remote control.',
                  features: ['Remote activation', 'Scheduled dispensing', 'Chemical level monitoring', 'Usage tracking', 'Maintenance alerts'],
                  icon: '💉'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.solution}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Smart Monitoring Dashboard */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Centralized IoT Monitoring Dashboard
              </h2>
              <p className="text-xl text-green-100">
                Monitor all your IoT devices and pest activity from a single, comprehensive dashboard.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Real-Time Monitoring',
                  description: 'Live monitoring of all connected devices and pest activity across properties.',
                  capabilities: ['Live device status', 'Real-time alerts', 'Activity feeds', 'System health', 'Performance metrics'],
                  icon: '📊'
                },
                {
                  feature: 'Predictive Analytics',
                  description: 'AI-powered analytics to predict pest activity and optimize treatments.',
                  capabilities: ['Activity prediction', 'Trend analysis', 'Risk assessment', 'Treatment optimization', 'Seasonal forecasting'],
                  icon: '🔮'
                },
                {
                  feature: 'Automated Responses',
                  description: 'Automated actions based on IoT sensor data and predefined rules.',
                  capabilities: ['Trigger treatments', 'Send notifications', 'Schedule services', 'Alert customers', 'Generate reports'],
                  icon: '⚡'
                }
              ].map((feature, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{feature.feature}</h3>
                  <p className="text-green-100 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.capabilities.map((capability, capIndex) => (
                      <li key={capIndex} className="text-sm text-green-100">
                        • {capability}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Commercial IoT Applications */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Commercial IoT Pest Control Applications
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  application: 'Restaurants',
                  challenges: 'Food safety compliance and health inspections',
                  solutions: ['Kitchen monitoring', 'Storage area sensors', 'Grease trap monitoring', 'Compliance reporting'],
                  benefits: 'Prevent health violations and maintain reputation',
                  icon: '🍽️'
                },
                {
                  application: 'Warehouses',
                  challenges: 'Large spaces and inventory protection',
                  solutions: ['Perimeter monitoring', 'Storage area sensors', 'Loading dock alerts', 'Inventory protection'],
                  benefits: 'Protect valuable inventory and reduce losses',
                  icon: '🏭'
                },
                {
                  application: 'Hotels',
                  challenges: 'Guest satisfaction and reputation management',
                  solutions: ['Room monitoring', 'Common area sensors', 'Discreet alerts', 'Preventive measures'],
                  benefits: 'Maintain guest satisfaction and prevent reviews',
                  icon: '🏨'
                },
                {
                  application: 'Healthcare',
                  challenges: 'Sterile environments and patient safety',
                  solutions: ['Sterile area monitoring', 'Patient room sensors', 'Medical equipment protection', 'Compliance tracking'],
                  benefits: 'Ensure patient safety and regulatory compliance',
                  icon: '🏥'
                }
              ].map((app, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                  <div className="text-4xl mb-4">{app.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{app.application}</h3>
                  <p className="text-sm text-gray-600 mb-4 italic">"{app.challenges}"</p>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-700 mb-2">IoT Solutions:</h4>
                    <ul className="space-y-1">
                      {app.solutions.map((solution, solutionIndex) => (
                        <li key={solutionIndex} className="text-xs text-gray-600">
                          • {solution}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="bg-green-50 rounded-lg p-3">
                    <h5 className="font-semibold text-green-800 text-sm mb-1">Benefits:</h5>
                    <p className="text-green-700 text-xs">{app.benefits}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Technology Integration */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              IoT Technology Integration
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Hardware Components:</h3>
                <ul className="space-y-4">
                  {[
                    'Wireless sensors with long battery life (2-5 years)',
                    'Weather-resistant outdoor monitoring devices',
                    'Low-power wide-area network (LPWAN) connectivity',
                    'Edge computing devices for local processing',
                    'Smart cameras with AI-powered pest detection',
                    'Environmental sensors (temperature, humidity, air quality)',
                    'Motion detectors and vibration sensors',
                    'GPS tracking for mobile monitoring units'
                  ].map((component, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{component}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Software Integration:</h3>
                <div className="space-y-6">
                  {[
                    { system: 'CRM Integration', description: 'Sync IoT data with customer management systems', icon: '👥' },
                    { system: 'Mobile Apps', description: 'Real-time alerts and monitoring on mobile devices', icon: '📱' },
                    { system: 'Cloud Analytics', description: 'Advanced analytics and machine learning in the cloud', icon: '☁️' },
                    { system: 'API Connectivity', description: 'Connect with existing pest control software', icon: '🔗' }
                  ].map((integration, index) => (
                    <div key={index} className="flex items-center bg-white p-4 rounded-lg shadow-lg">
                      <div className="text-3xl mr-4">{integration.icon}</div>
                      <div>
                        <div className="text-lg font-semibold text-gray-900">{integration.system}</div>
                        <div className="text-gray-600">{integration.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Implementation Process */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              IoT Implementation Process
            </h2>
            <div className="grid md:grid-cols-5 gap-8">
              {[
                { step: '1', title: 'Site Assessment', description: 'Evaluate property and identify optimal sensor placement', duration: '1-2 weeks' },
                { step: '2', title: 'System Design', description: 'Design custom IoT solution for your needs', duration: '1 week' },
                { step: '3', title: 'Installation', description: 'Install sensors and configure monitoring systems', duration: '1-2 weeks' },
                { step: '4', title: 'Integration', description: 'Connect with existing systems and train staff', duration: '1 week' },
                { step: '5', title: 'Monitoring', description: 'Ongoing monitoring and system optimization', duration: 'Ongoing' }
              ].map((phase, index) => (
                <div key={index} className="text-center">
                  <div className="bg-green-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                    {phase.step}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{phase.title}</h3>
                  <p className="text-gray-600 mb-2">{phase.description}</p>
                  <span className="text-sm text-green-600 font-semibold">{phase.duration}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              IoT Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Metro Pest Solutions: 75% Reduction in Service Calls
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Metro Pest Solutions implemented our IoT monitoring system across 200+ commercial properties. 
                    The smart monitoring system transformed their reactive approach into proactive pest management.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">75%</div>
                      <div className="text-sm text-gray-600">Fewer Service Calls</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">90%</div>
                      <div className="text-sm text-gray-600">Customer Satisfaction</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">IoT Implementation Results:</h4>
                  <ul className="space-y-3">
                    {[
                      '75% reduction in emergency service calls',
                      '90% faster pest problem detection',
                      '85% improvement in customer satisfaction',
                      '60% reduction in treatment costs',
                      '95% accuracy in pest activity prediction',
                      '600% ROI within 24 months'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing & ROI */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              IoT Investment & ROI
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  package: 'Starter IoT Package',
                  description: 'Basic IoT monitoring for small properties and residential customers.',
                  features: ['10 smart sensors', 'Basic monitoring dashboard', 'Mobile alerts', 'Monthly reporting'],
                  pricing: '$2,500 setup + $150/month',
                  roi: '300% ROI in 18 months'
                },
                {
                  package: 'Professional IoT Suite',
                  description: 'Comprehensive IoT solution for commercial properties and pest control companies.',
                  features: ['50 smart sensors', 'Advanced analytics', 'Predictive alerts', 'Custom integrations'],
                  pricing: '$8,500 setup + $450/month',
                  roi: '500% ROI in 24 months'
                },
                {
                  package: 'Enterprise IoT Platform',
                  description: 'Full-scale IoT deployment for large commercial clients and multi-location businesses.',
                  features: ['Unlimited sensors', 'AI-powered analytics', 'Custom development', 'Dedicated support'],
                  pricing: 'Custom pricing',
                  roi: '600%+ ROI in 24 months'
                }
              ].map((package, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg border border-gray-100">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{package.package}</h3>
                  <p className="text-gray-600 mb-6">{package.description}</p>
                  <ul className="space-y-3 mb-6">
                    {package.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <div className="border-t pt-6">
                    <div className="text-lg font-bold text-green-600 mb-2">{package.pricing}</div>
                    <div className="text-sm text-gray-600 mb-4">{package.roi}</div>
                    <button className="w-full bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                      Get Quote
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Transform Your Pest Control with IoT?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Discover how IoT technology can revolutionize your pest control operations, 
              improve customer satisfaction, and dramatically increase your ROI.
            </p>
            <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
              Schedule Your IoT Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
