import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Custom Software Development for Roofing Companies | GroundUPDigital',
  description: 'Custom software solutions for roofing businesses. Build specialized tools for emergency dispatch, insurance claims, project management, and crew coordination.',
  keywords: 'roofing software development, custom roofing apps, emergency dispatch software, insurance claim software',
  openGraph: {
    title: 'Custom Software Development for Roofing Companies | GroundUPDigital',
    description: 'Custom software solutions for roofing businesses. Build specialized tools for emergency dispatch, insurance claims, project management, and crew coordination.',
    type: 'website',
  },
};

export default function RoofingCustomSoftwarePage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Custom Software for <span className="text-blue-600">Roofing Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Build specialized software solutions tailored to your roofing business needs. 
              From emergency dispatch systems to insurance claim management, we create tools that give you a competitive advantage.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Discuss Your Project
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                View Software Examples
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Custom Software Types */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Specialized Software Solutions for Roofing Companies
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  software: 'Emergency Dispatch System',
                  description: 'Intelligent emergency call routing and crew dispatch for urgent roofing repairs.',
                  features: ['Priority-based routing', 'GPS crew tracking', 'Automated notifications', 'Response time optimization', 'Customer updates'],
                  icon: '🚨'
                },
                {
                  software: 'Insurance Claim Platform',
                  description: 'Comprehensive platform for managing insurance claims from start to finish.',
                  features: ['Damage documentation', 'Adjuster coordination', 'Photo management', 'Progress tracking', 'Payment processing'],
                  icon: '📋'
                },
                {
                  software: 'Weather-Based Scheduling',
                  description: 'Smart scheduling system that adapts to weather conditions and forecasts.',
                  features: ['Weather integration', 'Automatic rescheduling', 'Storm alerts', 'Crew notifications', 'Safety protocols'],
                  icon: '🌤️'
                },
                {
                  software: 'Customer Portal System',
                  description: 'Self-service portal for customers to track projects and communicate.',
                  features: ['Project tracking', 'Photo galleries', 'Communication tools', 'Payment processing', 'Document access'],
                  icon: '💻'
                },
                {
                  software: 'Crew Management App',
                  description: 'Mobile app for crew coordination, time tracking, and job management.',
                  features: ['Job assignments', 'Time tracking', 'Photo documentation', 'Material tracking', 'Safety checklists'],
                  icon: '👥'
                },
                {
                  software: 'Estimating & Bidding Tool',
                  description: 'Advanced estimating software with material costs and labor calculations.',
                  features: ['Material databases', 'Labor calculations', 'Competitive analysis', 'Proposal generation', 'Win/loss tracking'],
                  icon: '📊'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg border border-gray-100">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.software}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <button className="w-full mt-4 bg-blue-600 text-white py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Learn More
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Emergency Response Software */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Emergency Response Software Suite
              </h2>
              <p className="text-xl text-blue-100">
                Specialized software for managing roofing emergencies and urgent repairs.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  component: 'Call Triage System',
                  description: 'Intelligent call routing based on urgency and crew availability.',
                  capabilities: ['Urgency assessment', 'Automated routing', 'Queue management', 'Callback scheduling', 'Priority escalation'],
                  icon: '📞'
                },
                {
                  component: 'Dispatch Optimization',
                  description: 'GPS-based crew dispatch with real-time tracking and updates.',
                  capabilities: ['GPS tracking', 'Route optimization', 'ETA calculations', 'Crew coordination', 'Customer notifications'],
                  icon: '🗺️'
                },
                {
                  component: 'Emergency Documentation',
                  description: 'Rapid documentation system for emergency repairs and damage assessment.',
                  capabilities: ['Photo capture', 'Damage assessment', 'Emergency reports', 'Insurance documentation', 'Safety protocols'],
                  icon: '📸'
                }
              ].map((component, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{component.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{component.component}</h3>
                  <p className="text-blue-100 mb-4">{component.description}</p>
                  <ul className="space-y-2">
                    {component.capabilities.map((capability, capIndex) => (
                      <li key={capIndex} className="text-sm text-blue-100">
                        • {capability}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Development Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Our Custom Software Development Process
            </h2>
            <div className="grid md:grid-cols-6 gap-8">
              {[
                { step: '1', title: 'Discovery', description: 'Understand your roofing business needs', duration: '1-2 weeks' },
                { step: '2', title: 'Planning', description: 'Create detailed project roadmap', duration: '1 week' },
                { step: '3', title: 'Design', description: 'UI/UX design and system architecture', duration: '2-3 weeks' },
                { step: '4', title: 'Development', description: 'Build your custom software solution', duration: '8-16 weeks' },
                { step: '5', title: 'Testing', description: 'Comprehensive testing and QA', duration: '2-3 weeks' },
                { step: '6', title: 'Launch', description: 'Deployment, training, and support', duration: 'Ongoing' }
              ].map((phase, index) => (
                <div key={index} className="text-center">
                  <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold mx-auto mb-4">
                    {phase.step}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{phase.title}</h3>
                  <p className="text-gray-600 mb-2 text-sm">{phase.description}</p>
                  <span className="text-xs text-blue-600 font-semibold">{phase.duration}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Technology Stack */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Modern Technology Stack for Roofing Software
            </h2>
            <div className="grid lg:grid-cols-4 gap-8">
              {[
                {
                  category: 'Frontend Development',
                  technologies: ['React/Next.js', 'Vue.js', 'Angular', 'React Native', 'Flutter'],
                  description: 'Modern, responsive interfaces for web and mobile applications.',
                  icon: '💻'
                },
                {
                  category: 'Backend Development',
                  technologies: ['Node.js', 'Python/Django', 'PHP/Laravel', 'Ruby on Rails', '.NET Core'],
                  description: 'Robust server-side solutions for data processing and API development.',
                  icon: '⚙️'
                },
                {
                  category: 'Database & Storage',
                  technologies: ['PostgreSQL', 'MySQL', 'MongoDB', 'Redis', 'AWS S3'],
                  description: 'Scalable data storage solutions for your business information.',
                  icon: '🗄️'
                },
                {
                  category: 'Cloud & DevOps',
                  technologies: ['AWS', 'Google Cloud', 'Azure', 'Docker', 'Kubernetes'],
                  description: 'Reliable cloud infrastructure and deployment automation.',
                  icon: '☁️'
                }
              ].map((stack, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4 text-center">{stack.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">{stack.category}</h3>
                  <p className="text-gray-600 mb-4 text-sm">{stack.description}</p>
                  <ul className="space-y-2">
                    {stack.technologies.map((tech, techIndex) => (
                      <li key={techIndex} className="text-sm text-gray-600 text-center">
                        {tech}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Integration Capabilities */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seamless Integration Capabilities
            </h2>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  category: 'Business Systems',
                  integrations: ['QuickBooks/Xero', 'Salesforce', 'HubSpot', 'Microsoft Office', 'Google Workspace'],
                  description: 'Connect with your existing business and productivity tools.',
                  icon: '🔗'
                },
                {
                  category: 'Insurance & Claims',
                  integrations: ['Insurance Portals', 'Xactimate', 'EagleView', 'Claim Management', 'Adjuster Tools'],
                  description: 'Integrate with insurance systems and claim management platforms.',
                  icon: '📋'
                },
                {
                  category: 'Weather & Maps',
                  integrations: ['Weather APIs', 'Google Maps', 'GPS Tracking', 'Storm Tracking', 'Location Services'],
                  description: 'Leverage weather and location data for better service delivery.',
                  icon: '🌍'
                }
              ].map((category, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4 text-center">{category.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">{category.category}</h3>
                  <p className="text-gray-600 mb-4">{category.description}</p>
                  <ul className="space-y-2">
                    {category.integrations.map((integration, intIndex) => (
                      <li key={intIndex} className="text-sm text-gray-600 text-center">
                        • {integration}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Custom Software Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Apex Roofing: Custom Emergency Dispatch System
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Apex Roofing needed a specialized emergency dispatch system for storm damage response. 
                    Our custom solution reduced response times and improved customer satisfaction dramatically.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">70%</div>
                      <div className="text-sm text-gray-600">Faster Emergency Response</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">95%</div>
                      <div className="text-sm text-gray-600">Customer Satisfaction</div>
                    </div>
                  </div>
                  <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Custom Software Features:</h4>
                  <ul className="space-y-3">
                    {[
                      'AI-powered call triage and prioritization',
                      'Real-time GPS crew tracking and dispatch',
                      'Automated customer notifications and updates',
                      'Weather-based scheduling optimization',
                      'Integrated insurance claim documentation',
                      'Mobile crew app with offline capabilities'
                    ].map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Models */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Flexible Development Pricing Models
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  model: 'Fixed Price Project',
                  description: 'Best for well-defined roofing software projects with clear requirements.',
                  benefits: ['Predictable costs', 'Clear deliverables', 'Fixed timeline', 'Comprehensive documentation'],
                  pricing: 'Starting at $35,000',
                  bestFor: 'Emergency dispatch systems'
                },
                {
                  model: 'Time & Materials',
                  description: 'Flexible approach for complex roofing software with evolving requirements.',
                  benefits: ['Flexible scope', 'Iterative development', 'Regular feedback', 'Adaptable timeline'],
                  pricing: '$150-200/hour',
                  bestFor: 'Complex insurance platforms'
                },
                {
                  model: 'Dedicated Team',
                  description: 'Long-term partnership with dedicated developers for ongoing projects.',
                  benefits: ['Dedicated resources', 'Deep business knowledge', 'Ongoing support', 'Scalable team'],
                  pricing: '$10,000-18,000/month',
                  bestFor: 'Large-scale roofing platforms'
                }
              ].map((model, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg border border-gray-100">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{model.model}</h3>
                  <p className="text-gray-600 mb-6">{model.description}</p>
                  <ul className="space-y-3 mb-6">
                    {model.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                  <div className="border-t pt-6">
                    <div className="text-2xl font-bold text-blue-600 mb-2">{model.pricing}</div>
                    <div className="text-sm text-gray-600 mb-4">Best for: {model.bestFor}</div>
                    <button className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                      Get Quote
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Build Your Custom Roofing Software?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Let's discuss your unique business needs and create a custom software solution 
              that gives you a competitive advantage in the roofing industry.
            </p>
            <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Schedule Your Custom Software Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
