import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, Globe, Smartphone, TrendingUp, Users, Star, CheckCircle, Phone, Eye, MousePointer } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Professional Landscaping Business Websites | GroundUP Digital',
  description: 'Custom websites designed specifically for landscaping businesses. Convert more visitors into customers with our proven web design strategies.',
  keywords: 'landscaping website design, landscaping business websites, lawn care website, landscape contractor website design',
};

const webFeatures = [
  {
    icon: Eye,
    title: 'Lead-Focused Design',
    description: 'Every element designed to convert visitors into paying customers for your landscaping business.',
    benefits: ['Strategic call-to-action placement', 'Lead capture forms', 'Phone number prominence', 'Service showcases']
  },
  {
    icon: Smartphone,
    title: 'Mobile-First Approach',
    description: 'Your customers search on mobile. Your website will look perfect on every device.',
    benefits: ['Responsive design', 'Fast mobile loading', 'Touch-friendly navigation', 'Mobile-optimized forms']
  },
  {
    icon: TrendingUp,
    title: 'Business Growth Tools',
    description: 'Built-in features that help your landscaping business attract and retain customers.',
    benefits: ['Online quote requests', 'Service area maps', 'Customer testimonials', 'Before/after galleries']
  }
];

const websiteFeatures = [
  'Professional project galleries',
  'Before/after photo sliders',
  'Service area mapping',
  'Online quote request forms',
  'Customer testimonial sections',
  'Mobile-responsive design',
  'Fast loading speeds',
  'SEO-optimized structure',
  'Social media integration',
  'Google Analytics setup',
  'Contact form integration',
  'Business information display'
];

const packages = [
  {
    name: "Business Starter",
    price: "$2,997",
    period: "one-time",
    description: "Perfect for new landscaping businesses establishing their online presence",
    features: [
      "5-page professional website",
      "Mobile-responsive design",
      "Contact forms & quote requests",
      "Basic SEO optimization",
      "Google Analytics setup",
      "30 days of support"
    ],
    popular: false
  },
  {
    name: "Growth Pro",
    price: "$4,997",
    period: "one-time", 
    description: "Ideal for established landscaping companies ready to scale their business",
    features: [
      "10-page custom website",
      "Advanced lead capture system",
      "Project gallery & testimonials",
      "Service area mapping",
      "Advanced SEO optimization",
      "90 days of support",
      "Monthly performance reports"
    ],
    popular: true
  },
  {
    name: "Market Leader",
    price: "$7,997",
    period: "one-time",
    description: "For landscaping businesses ready to dominate their market online",
    features: [
      "Unlimited pages & features",
      "Custom functionality development",
      "Advanced analytics & tracking",
      "A/B testing setup",
      "Conversion optimization",
      "6 months of support",
      "Quarterly strategy sessions"
    ],
    popular: false
  }
];

const caseStudy = {
  company: "Premier Landscapes LLC",
  location: "Denver, CO",
  results: [
    { metric: "Website Conversions", improvement: "+340%" },
    { metric: "Online Leads", improvement: "+280%" },
    { metric: "Quote Requests", improvement: "+195%" },
    { metric: "Revenue Growth", improvement: "+156%" }
  ]
};

export default function WebDesignPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Globe className="w-4 h-4" />
              <span className="text-sm font-semibold">Professional Landscaping Business Websites</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Websites That Convert Visitors Into{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscaping Customers
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Professional websites designed specifically for landscaping business owners. 
              Showcase your services, attract more customers, and grow your landscaping business online.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Eye className="w-5 h-5 mr-2" />
                View Website Examples
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                Get Free Quote
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '340%', label: 'Avg Conversion Increase' },
                { number: '150+', label: 'Websites Built' },
                { number: '94%', label: 'Client Satisfaction' },
                { number: '30-Day', label: 'Launch Guarantee' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Why Landscaping Businesses Choose Our Web Design
            </h2>
            <p className="text-lg text-gray-600">
              We understand what landscaping business owners need from their website. Every design element is strategically 
              crafted to help you attract more customers and grow your business.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {webFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Website Features Grid */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Everything Your Landscaping Business Website Needs
              </h2>
              <p className="text-lg text-gray-600">
                Our websites include all the features landscaping business owners need to attract customers and grow their business online.
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-4">
              {websiteFeatures.map((feature, index) => (
                <div key={index} className="flex items-center p-4 bg-white rounded-lg shadow-sm">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Real Results for Landscaping Business Owners
              </h2>
              <p className="text-lg text-gray-600">
                See how our website design helped {caseStudy.company} transform their online presence and grow their business
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A growing landscaping business in {caseStudy.location} needed a professional website to compete with larger companies. 
                      Our custom design helped them establish credibility and attract high-value customers.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      View Their Website
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Website Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Professional website design packages specifically created for landscaping business owners. 
              All packages include everything you need to establish a strong online presence.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600"> {pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* FAQ Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Frequently Asked Questions About Landscaping Website Design
              </h2>
              <p className="text-lg text-gray-600">
                Common questions landscaping business owners have about professional website design and development.
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "How long does it take to build a landscaping website?",
                  answer: "Most landscaping websites take 4-8 weeks to complete, depending on complexity and features. We work efficiently while ensuring every detail meets your business needs and showcases your landscaping services effectively."
                },
                {
                  question: "Will my landscaping website work on mobile devices?",
                  answer: "Absolutely! All our landscaping websites are fully responsive and optimized for mobile devices. With 70%+ of customers browsing on mobile, we ensure your landscaping business looks professional on every screen size."
                },
                {
                  question: "Can I update my landscaping website content myself?",
                  answer: "Yes! We build user-friendly content management systems that allow you to easily update project photos, service descriptions, pricing, and blog posts without technical knowledge. We also provide training and ongoing support."
                },
                {
                  question: "Do you provide landscaping-specific features?",
                  answer: "Yes! We include features like project galleries, service area maps, seasonal service scheduling, quote request forms, before/after photo showcases, and integration with landscaping business tools."
                }
              ].map((faq, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{faq.question}</h3>
                  <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Launch Your Professional Landscaping Website?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a custom website designed specifically for your landscaping business. Start attracting more customers online today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Eye className="w-5 h-5 mr-2" />
                View Portfolio
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Get Free Quote
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
