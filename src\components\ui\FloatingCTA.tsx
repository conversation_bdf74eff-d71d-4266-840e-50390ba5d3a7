'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Phone, Calendar, X, ArrowRight } from 'lucide-react';
import Link from 'next/link';

export const FloatingCTA: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      // Show after scrolling 500px
      setIsVisible(window.scrollY > 500);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 100, scale: 0.8 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 100, scale: 0.8 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="fixed top-1/2 right-4 xs:right-6 transform -translate-y-1/2 z-50 safe-right"
        >
          {!isExpanded ? (
            // Collapsed State - Floating Button
            <motion.button
              onClick={() => setIsExpanded(true)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-blue-600 to-green-600 text-white rounded-full px-4 py-3 xs:px-6 xs:py-4 shadow-2xl hover:shadow-3xl transition-all duration-300 group touch-target-lg"
            >
              <div className="flex items-center gap-2 xs:gap-3">
                <Calendar className="w-5 h-5 xs:w-6 xs:h-6 flex-shrink-0" />
                <span className="text-sm xs:text-base font-semibold whitespace-nowrap">Free Strategy</span>
              </div>

              {/* Pulse Animation */}
              <div className="absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-20"></div>
            </motion.button>
          ) : (
            // Expanded State - CTA Card
            <motion.div
              initial={{ opacity: 0, scale: 0.8, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              transition={{ duration: 0.2 }}
              className="bg-white rounded-xl xs:rounded-2xl shadow-2xl p-4 xs:p-6 w-64 xs:w-72 sm:w-80 max-w-[calc(100vw-2rem)] border border-gray-100 relative"
            >
              {/* Close Button */}
              <button
                onClick={() => setIsExpanded(false)}
                className="absolute top-2 xs:top-3 right-2 xs:right-3 text-gray-400 hover:text-gray-600 transition-colors touch-target"
              >
                <X className="w-4 h-4 xs:w-5 xs:h-5" />
              </button>

              {/* Content */}
              <div className="pr-6 xs:pr-8">
                <h3 className="text-base xs:text-lg font-bold text-gray-900 mb-2 leading-tight">
                  Free Growth Strategy
                </h3>

                <p className="text-xs xs:text-sm text-gray-600 mb-3 xs:mb-4 leading-relaxed">
                  Discover how to generate 247%+ more leads for your landscaping business with our premium digital ecosystem.
                </p>

                <div className="text-xs text-gray-500 mb-3 xs:mb-4 leading-relaxed">
                  ✓ Custom growth strategy worth $3,500<br/>
                  ✓ Landscaping-specific solutions<br/>
                  ✓ No obligation - 100% FREE
                </div>

                {/* CTA Buttons */}
                <div className="space-y-2">
                  <Link href="/partnership">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="w-full bg-gradient-to-r from-blue-600 to-green-600 text-white py-2.5 xs:py-3 rounded-lg font-semibold text-xs xs:text-sm flex items-center justify-center gap-1 xs:gap-2 shadow-lg hover:shadow-xl transition-all touch-target"
                    >
                      <Calendar className="w-3 h-3 xs:w-4 xs:h-4 flex-shrink-0" />
                      <span className="truncate">
                        <span className="hidden xs:inline">Get My Free Strategy Now</span>
                        <span className="xs:hidden">Get Free Strategy</span>
                      </span>
                    </motion.button>
                  </Link>

                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full bg-gray-100 text-gray-700 py-2.5 xs:py-3 rounded-lg font-semibold text-xs xs:text-sm flex items-center justify-center gap-1 xs:gap-2 hover:bg-gray-200 transition-colors touch-target"
                  >
                    <Phone className="w-3 h-3 xs:w-4 xs:h-4 flex-shrink-0" />
                    <span className="truncate">Call (*************</span>
                  </motion.button>
                </div>

                {/* Urgency */}
                <div className="text-center mt-2 xs:mt-3">
                  <span className="text-xs text-orange-600 font-medium">
                    🔥 Only 3 spots left this month
                  </span>
                </div>
              </div>
            </motion.div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};
