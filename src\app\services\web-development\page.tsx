'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Globe, 
  Smartphone, 
  Zap, 
  Search, 
  Shield, 
  CheckCircle,
  ArrowRight,
  Code,
  Palette,
  BarChart3
} from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

const features = [
  {
    icon: Smartphone,
    title: 'Mobile-First Design',
    description: 'Responsive websites that look perfect on all devices, ensuring customers can reach you anywhere.'
  },
  {
    icon: Zap,
    title: 'Lightning Fast',
    description: 'Optimized for speed with loading times under 3 seconds to keep visitors engaged.'
  },
  {
    icon: Search,
    title: 'SEO Optimized',
    description: 'Built with search engine optimization in mind to help you rank higher in local searches.'
  },
  {
    icon: Shield,
    title: 'Secure & Reliable',
    description: 'SSL certificates, regular backups, and security monitoring to protect your business.'
  }
];

const industryExamples = [
  {
    industry: 'Landscaping',
    features: ['Project gallery showcase', 'Seasonal service highlights', 'Before/after photo sliders', 'Service area maps'],
    color: 'green'
  },
  {
    industry: 'Roofing',
    features: ['Insurance claim assistance', 'Material showcase', 'Emergency contact forms', 'Warranty information'],
    color: 'blue'
  },
  {
    industry: 'Pest Control',
    features: ['Emergency booking system', 'Treatment schedules', 'Pest identification guides', 'Service guarantees'],
    color: 'orange'
  }
];

const process = [
  {
    step: '01',
    title: 'Discovery & Planning',
    description: 'We analyze your business goals, target audience, and competition to create a strategic plan.',
    icon: BarChart3
  },
  {
    step: '02',
    title: 'Design & Development',
    description: 'Our team creates a custom design and develops your website with the latest technologies.',
    icon: Palette
  },
  {
    step: '03',
    title: 'Testing & Launch',
    description: 'Thorough testing across all devices and browsers before launching your new website.',
    icon: Code
  },
  {
    step: '04',
    title: 'Ongoing Support',
    description: 'Continuous monitoring, updates, and support to keep your website performing optimally.',
    icon: Shield
  }
];

export default function WebDevelopmentPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <Badge variant="primary" size="lg">
                Web Development
              </Badge>
              
              <h1 className="heading-1 text-gray-900">
                Professional Websites That{' '}
                <span className="gradient-text">Convert Visitors</span>{' '}
                Into Customers
              </h1>
              
              <p className="text-large text-gray-600">
                Get a stunning, mobile-responsive website designed specifically for your local service business. 
                Our websites don't just look great—they're built to generate leads and grow your business.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Button variant="primary" size="lg">
                  Get Free Website Audit
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button variant="outline" size="lg">
                  View Portfolio
                </Button>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="bg-white rounded-2xl shadow-2xl p-6 border border-gray-100">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-gray-900">Website Performance</h3>
                    <Globe className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-green-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">98%</div>
                      <div className="text-sm text-gray-600">Speed Score</div>
                    </div>
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">2.1s</div>
                      <div className="text-sm text-gray-600">Load Time</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="heading-2 text-gray-900 mb-6">
              Why Choose Our Web Development Services?
            </h2>
            <p className="text-large text-gray-600">
              We build websites that work as hard as you do, with features specifically designed 
              for local service businesses.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Card className="h-full text-center">
                    <CardContent className="space-y-4">
                      <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center mx-auto">
                        <Icon className="h-6 w-6" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600">
                        {feature.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </Container>
      </Section>

      {/* Industry Examples */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="heading-2 text-gray-900 mb-6">
              Tailored for Your Industry
            </h2>
            <p className="text-large text-gray-600">
              Every website we build includes industry-specific features that help your business stand out.
            </p>
          </div>
          
          <div className="grid lg:grid-cols-3 gap-8">
            {industryExamples.map((example, index) => (
              <motion.div
                key={example.industry}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
              >
                <Card>
                  <CardContent className="space-y-6">
                    <div className="text-center">
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">
                        {example.industry}
                      </h3>
                      <p className="text-gray-600">Specialized Features</p>
                    </div>
                    
                    <ul className="space-y-3">
                      {example.features.map((feature) => (
                        <li key={feature} className="flex items-center">
                          <CheckCircle className="h-5 w-5 text-green-600 mr-3 flex-shrink-0" />
                          <span className="text-gray-700">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </Container>
      </Section>

      {/* Process Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="heading-2 text-gray-900 mb-6">
              Our Development Process
            </h2>
            <p className="text-large text-gray-600">
              From concept to launch, we follow a proven process to deliver exceptional results.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {process.map((step, index) => {
              const Icon = step.icon;
              return (
                <motion.div
                  key={step.step}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="relative mb-6">
                    <div className="w-16 h-16 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Icon className="h-8 w-8" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      {step.step}
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {step.description}
                  </p>
                </motion.div>
              );
            })}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="heading-2 text-gray-900 mb-6">
              Ready to Get a Website That Converts?
            </h2>
            <p className="text-large text-gray-600 mb-8">
              Let's discuss your project and create a website that drives real results for your business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="primary" size="lg">
                Start Your Project
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button variant="outline" size="lg">
                Schedule Consultation
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
