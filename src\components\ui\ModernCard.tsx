import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface ModernCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'gradient' | 'glass' | 'elevated' | 'bordered';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  glow?: boolean;
  onClick?: () => void;
}

const cardVariants = {
  default: 'bg-white border border-gray-200 shadow-sm',
  gradient: 'bg-gradient-to-br from-white to-gray-50 border border-gray-200 shadow-lg',
  glass: 'bg-white/80 backdrop-blur-sm border border-white/20 shadow-xl',
  elevated: 'bg-white shadow-2xl border-0',
  bordered: 'bg-white border-2 border-blue-100 shadow-md'
};

const sizeVariants = {
  sm: 'p-3 xs:p-4 rounded-lg xs:rounded-xl',
  md: 'p-4 xs:p-6 rounded-lg xs:rounded-xl',
  lg: 'p-4 xs:p-6 sm:p-8 rounded-lg xs:rounded-xl sm:rounded-2xl',
  xl: 'p-6 xs:p-8 sm:p-10 rounded-xl xs:rounded-2xl sm:rounded-3xl'
};

export const ModernCard: React.FC<ModernCardProps> = ({
  children,
  className,
  variant = 'default',
  size = 'md',
  hover = true,
  glow = false,
  onClick
}) => {
  const baseClasses = cn(
    'transition-all duration-300 ease-out',
    cardVariants[variant],
    sizeVariants[size],
    {
      'hover:shadow-xl hover:scale-[1.02] cursor-pointer': hover && onClick,
      'hover:shadow-lg hover:-translate-y-1': hover && !onClick,
      'ring-2 ring-blue-500/20 shadow-blue-500/25': glow,
    },
    className
  );

  if (onClick) {
    return (
      <motion.div
        className={baseClasses}
        onClick={onClick}
        whileHover={{ scale: hover ? 1.02 : 1 }}
        whileTap={{ scale: 0.98 }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {children}
      </motion.div>
    );
  }

  return (
    <motion.div
      className={baseClasses}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {children}
    </motion.div>
  );
};

// Enhanced Service Card Component
interface ServiceCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  features: string[];
  price?: string;
  popular?: boolean;
  onClick?: () => void;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
  icon,
  title,
  description,
  features,
  price,
  popular = false,
  onClick
}) => {
  return (
    <ModernCard
      variant={popular ? 'gradient' : 'default'}
      size="lg"
      glow={popular}
      onClick={onClick}
      className={cn(
        'relative overflow-hidden group',
        popular && 'border-blue-500 ring-2 ring-blue-500/20'
      )}
    >
      {popular && (
        <div className="absolute -top-1 -right-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-bl-lg text-xs font-semibold">
          Most Popular
        </div>
      )}
      
      <div className="flex flex-col h-full">
        <div className="flex items-center gap-4 mb-4">
          <div className="p-3 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl group-hover:from-blue-100 group-hover:to-indigo-200 transition-colors">
            {icon}
          </div>
          <div>
            <h3 className="text-lg xs:text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
              {title}
            </h3>
            {price && (
              <p className="text-xl xs:text-2xl font-bold text-blue-600 mt-1">
                {price}
                <span className="text-sm text-gray-500 font-normal">/month</span>
              </p>
            )}
          </div>
        </div>
        
        <p className="text-sm xs:text-base text-gray-600 mb-4 xs:mb-6 leading-relaxed flex-grow">
          {description}
        </p>
        
        <div className="space-y-3">
          {features.map((feature, index) => (
            <div key={index} className="flex items-center gap-3">
              <div className="w-2 h-2 bg-gradient-to-r from-green-400 to-green-600 rounded-full"></div>
              <span className="text-gray-700 text-xs xs:text-sm">{feature}</span>
            </div>
          ))}
        </div>
        
        <div className="mt-6 pt-6 border-t border-gray-100">
          <motion.button
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-6 rounded-xl font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            Get Started
          </motion.button>
        </div>
      </div>
    </ModernCard>
  );
};

// Enhanced Stats Card
interface StatsCardProps {
  number: string;
  label: string;
  icon: React.ReactNode;
  trend?: 'up' | 'down';
  trendValue?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  number,
  label,
  icon,
  trend,
  trendValue
}) => {
  return (
    <ModernCard variant="glass" size="lg" className="text-center group">
      <div className="flex flex-col items-center">
        <div className="p-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
          <div className="text-white text-2xl">
            {icon}
          </div>
        </div>
        
        <div className="text-4xl font-bold text-gray-900 mb-2 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          {number}
        </div>
        
        <div className="text-gray-600 font-medium mb-2">
          {label}
        </div>
        
        {trend && trendValue && (
          <div className={cn(
            'flex items-center gap-1 text-sm font-semibold',
            trend === 'up' ? 'text-green-600' : 'text-red-600'
          )}>
            <span>{trend === 'up' ? '↗' : '↘'}</span>
            <span>{trendValue}</span>
          </div>
        )}
      </div>
    </ModernCard>
  );
};

// Enhanced Feature Card
interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  benefits: string[];
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
  icon,
  title,
  description,
  benefits
}) => {
  return (
    <ModernCard variant="elevated" size="lg" className="group h-full">
      <div className="flex flex-col h-full">
        <div className="flex items-start gap-4 mb-6">
          <div className="p-3 bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl group-hover:from-blue-100 group-hover:to-indigo-200 transition-colors flex-shrink-0">
            <div className="text-blue-600 text-xl">
              {icon}
            </div>
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">
              {title}
            </h3>
            <p className="text-gray-600 leading-relaxed">
              {description}
            </p>
          </div>
        </div>
        
        <div className="space-y-3 flex-grow">
          {benefits.map((benefit, index) => (
            <div key={index} className="flex items-center gap-3">
              <div className="w-1.5 h-1.5 bg-gradient-to-r from-green-400 to-green-600 rounded-full"></div>
              <span className="text-gray-700 text-sm">{benefit}</span>
            </div>
          ))}
        </div>
      </div>
    </ModernCard>
  );
};

// Enhanced CTA Card
interface CTACardProps {
  title: string;
  description: string;
  buttonText: string;
  buttonHref?: string;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
}

export const CTACard: React.FC<CTACardProps> = ({
  title,
  description,
  buttonText,
  buttonHref,
  onClick,
  variant = 'primary'
}) => {
  const gradientClass = variant === 'primary' 
    ? 'from-blue-600 via-purple-600 to-indigo-600' 
    : 'from-green-600 via-teal-600 to-blue-600';

  return (
    <ModernCard 
      variant="glass" 
      size="xl" 
      className={cn(
        'relative overflow-hidden',
        `bg-gradient-to-br ${gradientClass} text-white border-0`
      )}
    >
      <div className="absolute inset-0 bg-black/10"></div>
      <div className="relative z-10 text-center">
        <h3 className="text-3xl font-bold mb-4">
          {title}
        </h3>
        <p className="text-xl opacity-90 mb-8 leading-relaxed">
          {description}
        </p>
        <motion.button
          className="bg-white text-gray-900 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-colors shadow-lg hover:shadow-xl"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={onClick}
        >
          {buttonText}
        </motion.button>
      </div>
    </ModernCard>
  );
};
