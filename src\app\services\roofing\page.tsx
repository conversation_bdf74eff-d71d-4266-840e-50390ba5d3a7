import React from 'react';
import { Metadata } from 'next';
import { RoofingServicesContent } from '@/components/services/RoofingServicesContent';

export const metadata: Metadata = {
  title: 'Roofing Digital Marketing Services | GroundUP Digital - #1 Roofing Marketing Agency',
  description: 'Dominate your local roofing market with our proven digital marketing strategies. 400% more storm leads, enterprise-grade solutions at affordable prices. Get your free audit today!',
  keywords: 'roofing marketing, roof repair marketing, roofing digital marketing, roofing SEO, roofing Google Ads, roofing lead generation, storm damage marketing, roof replacement marketing',
  openGraph: {
    title: 'Roofing Digital Marketing Services | GroundUP Digital',
    description: 'The #1 digital marketing agency for roofing contractors. Proven strategies that generate 400% more qualified storm and repair leads.',
    type: 'website',
    images: [
      {
        url: '/og-roofing-services.jpg',
        width: 1200,
        height: 630,
        alt: 'Roofing Digital Marketing Services'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Roofing Digital Marketing Services | GroundUP Digital',
    description: 'The #1 digital marketing agency for roofing contractors. Proven strategies that generate 400% more qualified storm and repair leads.',
    images: ['/og-roofing-services.jpg']
  },
  alternates: {
    canonical: 'https://groundupdigital.com/services/roofing'
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

// Structured Data for SEO
const structuredData = {
  "@context": "https://schema.org",
  "@type": "Service",
  "name": "Roofing Digital Marketing Services",
  "description": "Comprehensive digital marketing services specifically designed for roofing contractors and storm damage restoration companies",
  "provider": {
    "@type": "Organization",
    "name": "GroundUP Digital",
    "url": "https://groundupdigital.com",
    "logo": "https://groundupdigital.com/logo.png",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-123-4567",
      "contactType": "customer service",
      "availableLanguage": "English"
    }
  },
  "serviceType": "Digital Marketing",
  "areaServed": {
    "@type": "Country",
    "name": "United States"
  },
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Roofing Marketing Services",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Roofing SEO",
          "description": "Search engine optimization specifically for roofing contractors"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Storm Damage Marketing",
          "description": "Emergency marketing campaigns for storm damage restoration"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Roofing Google Ads",
          "description": "Pay-per-click advertising campaigns for roofing companies"
        }
      }
    ]
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.9",
    "reviewCount": "89",
    "bestRating": "5"
  }
};

export default function RoofingServicesPage() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <RoofingServicesContent />
    </>
  );
}
