import React from 'react';
import { Metadata } from 'next';
import { 
  Users, 
  Target, 
  Award, 
  TrendingUp,
  CheckCircle,
  ArrowRight,
  Heart,
  Lightbulb,
  Shield
} from 'lucide-react';

export const metadata: Metadata = {
  title: 'About GroundUPDigital | Digital Marketing Experts for Local Service Businesses',
  description: 'Learn about GroundUPDigital\'s mission to help landscaping, roofing, and pest control businesses grow through specialized digital marketing strategies and proven results.',
  keywords: 'about GroundUPDigital, digital marketing team, local service marketing experts, landscaping marketing specialists',
  openGraph: {
    title: 'About GroundUPDigital | Digital Marketing Experts for Local Service Businesses',
    description: 'Learn about GroundUPDigital\'s mission to help landscaping, roofing, and pest control businesses grow through specialized digital marketing strategies and proven results.',
    type: 'website',
  },
};

const values = [
  {
    icon: Heart,
    title: 'Client-Focused',
    description: 'Your success is our success. We\'re committed to delivering results that matter to your business.'
  },
  {
    icon: Lightbulb,
    title: 'Innovation',
    description: 'We stay ahead of digital marketing trends to give our clients a competitive advantage.'
  },
  {
    icon: Shield,
    title: 'Transparency',
    description: 'Clear communication, honest reporting, and no hidden fees. You always know what we\'re doing and why.'
  }
];

const stats = [
  { number: '500+', label: 'Happy Clients', icon: Users },
  { number: '300%', label: 'Avg Lead Increase', icon: TrendingUp },
  { number: '95%', label: 'Client Retention', icon: Target },
  { number: '5★', label: 'Average Rating', icon: Award }
];

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-green-50 py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
                We Help Local Service Businesses{' '}
                <span className="text-blue-600">Dominate Online</span>
              </h1>
              
              <p className="text-xl text-gray-600">
                Founded by local business owners who understand the challenges you face, 
                GroundUPDigital specializes in digital marketing solutions that actually work 
                for landscapers, roofers, and pest control companies.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-gray-700">Founded by local business owners</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-gray-700">Specialized in service industries</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-gray-700">Proven track record of success</span>
                </div>
              </div>
              
              <button className="bg-blue-600 text-white px-6 xs:px-8 py-3 xs:py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-flex items-center text-sm xs:text-base touch-target">
                Learn About Our Process
                <ArrowRight className="ml-2 h-5 w-5" />
              </button>
            </div>
            
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-100">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Our Mission</h3>
                <p className="text-gray-600 leading-relaxed">
                  "To empower local service businesses with digital marketing strategies that 
                  generate real results. We believe every hardworking business owner deserves 
                  to succeed online, and we're here to make that happen."
                </p>
                <div className="mt-6 pt-6 border-t border-gray-100">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold">SJ</span>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">Sarah Johnson</div>
                      <div className="text-sm text-gray-600">Founder & CEO</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-xl xs:text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4 xs:mb-6">
              Our Core Values
            </h2>
            <p className="text-base xs:text-lg sm:text-xl text-gray-600">
              These principles guide everything we do and ensure we deliver exceptional results for our clients.
            </p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 xs:gap-6 sm:gap-8">
            {values.map((value, index) => {
              const Icon = value.icon;
              return (
                <div key={value.title} className="bg-white p-8 rounded-lg shadow-lg text-center">
                  <div className="w-16 h-16 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Icon className="h-8 w-8" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    {value.title}
                  </h3>
                  <p className="text-gray-600">
                    {value.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-xl xs:text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4 xs:mb-6">
              Results That Speak for Themselves
            </h2>
            <p className="text-base xs:text-lg sm:text-xl text-gray-600">
              Our track record demonstrates our commitment to delivering real results for local service businesses.
            </p>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 xs:gap-6 sm:gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={stat.label} className="text-center bg-white p-4 xs:p-6 sm:p-8 rounded-lg shadow-lg">
                  <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Icon className="h-6 w-6" />
                  </div>
                  <div className="text-2xl xs:text-3xl sm:text-4xl font-bold text-gray-900 mb-2">
                    {stat.number}
                  </div>
                  <div className="text-gray-600">
                    {stat.label}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Why Local Service Businesses Choose Us
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Industry Expertise</h3>
                <p className="text-gray-600 mb-6">
                  We don't just do generic digital marketing. We specialize exclusively in landscaping, 
                  roofing, and pest control businesses, which means we understand your unique challenges, 
                  seasonal patterns, and customer behavior.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                    <span className="text-gray-700">Deep understanding of service industry challenges</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                    <span className="text-gray-700">Seasonal marketing strategies that work</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                    <span className="text-gray-700">Local market expertise and insights</span>
                  </li>
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Proven Results</h3>
                <p className="text-gray-600 mb-6">
                  Our clients see real, measurable results. We focus on metrics that matter to your business: 
                  more leads, higher conversion rates, and increased revenue. Every strategy is designed 
                  with your bottom line in mind.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                    <span className="text-gray-700">Average 300% increase in qualified leads</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                    <span className="text-gray-700">95% client retention rate</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                    <span className="text-gray-700">Transparent reporting and communication</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Partner with Digital Marketing Experts?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Let's discuss how we can help your local service business dominate online and attract more customers.
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Schedule Your Free Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
