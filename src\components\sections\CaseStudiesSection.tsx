'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TrendingUp, Users, DollarSign, Clock, ArrowRight, Star, MapPin, Calendar } from 'lucide-react';
import { Section } from '@/components/ui/Section';
import { GlassCard, GradientCard } from '@/components/ui/GlassCard';
import { Button } from '@/components/ui/Button';
import { Counter, CircularProgress } from '@/components/ui/InteractiveElements';

interface CaseStudy {
  id: string;
  industry: 'landscaping' | 'roofing' | 'pest-control';
  companyName: string;
  location: string;
  challenge: string;
  solution: string[];
  results: {
    metric: string;
    before: number;
    after: number;
    improvement: string;
    icon: React.ComponentType<any>;
  }[];
  testimonial: {
    quote: string;
    author: string;
    position: string;
    rating: number;
  };
  timeline: string;
  services: string[];
  image: string;
}

const caseStudies: CaseStudy[] = [
  {
    id: 'green-thumb-landscaping',
    industry: 'landscaping',
    companyName: 'Green Thumb Landscaping',
    location: 'Austin, TX',
    challenge: 'Local landscaping company struggling with seasonal lead generation and competing against larger franchises. Only 15% of leads were coming from digital channels.',
    solution: [
      'Complete website redesign with mobile optimization',
      'Local SEO campaign targeting "landscaping Austin" keywords',
      'Google Ads campaigns for seasonal services',
      'Social media content showcasing before/after projects',
      'Google My Business optimization with review management'
    ],
    results: [
      {
        metric: 'Monthly Leads',
        before: 12,
        after: 47,
        improvement: '+292%',
        icon: Users
      },
      {
        metric: 'Revenue Growth',
        before: 8500,
        after: 28000,
        improvement: '+229%',
        icon: DollarSign
      },
      {
        metric: 'Local Rankings',
        before: 45,
        after: 3,
        improvement: 'Top 3',
        icon: TrendingUp
      },
      {
        metric: 'Cost Per Lead',
        before: 85,
        after: 32,
        improvement: '-62%',
        icon: Clock
      }
    ],
    testimonial: {
      quote: "GroundUPDigital transformed our business. We went from struggling to find customers to having a 3-month booking waitlist. Their understanding of the landscaping industry made all the difference.",
      author: "Mike Rodriguez",
      position: "Owner, Green Thumb Landscaping",
      rating: 5
    },
    timeline: '6 months',
    services: ['Web Development', 'Local SEO', 'Google Ads', 'Social Media'],
    image: '/case-studies/green-thumb-before-after.jpg'
  },
  {
    id: 'apex-roofing',
    industry: 'roofing',
    companyName: 'Apex Roofing Solutions',
    location: 'Dallas, TX',
    challenge: 'Roofing contractor needed to establish online presence and compete for storm damage restoration work. Limited visibility during peak storm seasons.',
    solution: [
      'Emergency-focused website with storm damage forms',
      'Local SEO for "roof repair Dallas" and storm-related keywords',
      'Google Ads campaigns targeting weather events',
      'Insurance claim assistance landing pages',
      'Reputation management and review generation'
    ],
    results: [
      {
        metric: 'Emergency Calls',
        before: 8,
        after: 34,
        improvement: '+325%',
        icon: Users
      },
      {
        metric: 'Monthly Revenue',
        before: 45000,
        after: 125000,
        improvement: '+178%',
        icon: DollarSign
      },
      {
        metric: 'Google Rankings',
        before: 67,
        after: 2,
        improvement: 'Top 3',
        icon: TrendingUp
      },
      {
        metric: 'Response Time',
        before: 4,
        after: 1,
        improvement: '75% faster',
        icon: Clock
      }
    ],
    testimonial: {
      quote: "During the last hail storm, we received 34 qualified leads in one week compared to 8 for the entire previous storm season. The ROI has been incredible.",
      author: "Sarah Johnson",
      position: "Operations Manager, Apex Roofing",
      rating: 5
    },
    timeline: '4 months',
    services: ['Web Development', 'Local SEO', 'Google Ads', 'Reputation Management'],
    image: '/case-studies/apex-roofing-results.jpg'
  },
  {
    id: 'pest-pro-solutions',
    industry: 'pest-control',
    companyName: 'PestPro Solutions',
    location: 'Houston, TX',
    challenge: 'Family-owned pest control business losing customers to national chains. Needed year-round lead generation beyond seasonal pest issues.',
    solution: [
      'Educational content website with pest identification guides',
      'Local SEO for "pest control Houston" and specific pest keywords',
      'Seasonal Google Ads campaigns for different pest types',
      'Email marketing for maintenance reminders',
      'Social media education and prevention tips'
    ],
    results: [
      {
        metric: 'Monthly Customers',
        before: 89,
        after: 267,
        improvement: '+200%',
        icon: Users
      },
      {
        metric: 'Annual Revenue',
        before: 180000,
        after: 485000,
        improvement: '+169%',
        icon: DollarSign
      },
      {
        metric: 'Customer Retention',
        before: 65,
        after: 87,
        improvement: '+34%',
        icon: TrendingUp
      },
      {
        metric: 'Avg. Customer Value',
        before: 125,
        after: 285,
        improvement: '+128%',
        icon: Clock
      }
    ],
    testimonial: {
      quote: "We've gone from a seasonal business to year-round growth. The educational content strategy has positioned us as the local pest control experts.",
      author: "David Chen",
      position: "Owner, PestPro Solutions",
      rating: 5
    },
    timeline: '8 months',
    services: ['Web Development', 'Content Marketing', 'Local SEO', 'Email Marketing'],
    image: '/case-studies/pestpro-growth-chart.jpg'
  }
];

const industryFilters = [
  { id: 'all', label: 'All Industries', color: 'blue' },
  { id: 'landscaping', label: 'Landscaping', color: 'green' },
  { id: 'roofing', label: 'Roofing', color: 'orange' },
  { id: 'pest-control', label: 'Pest Control', color: 'purple' }
];

export const CaseStudiesSection: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [selectedCase, setSelectedCase] = useState<CaseStudy | null>(null);

  const filteredCases = activeFilter === 'all' 
    ? caseStudies 
    : caseStudies.filter(study => study.industry === activeFilter);

  return (
    <Section id="case-studies" className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Success <span className="gradient-text-blue">Stories</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Real results from real local service providers. See how we've helped landscapers, 
            roofers, and pest control companies dominate their local markets and grow their revenue.
          </p>
        </motion.div>

        {/* Industry Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {industryFilters.map((filter) => (
            <button
              key={filter.id}
              onClick={() => setActiveFilter(filter.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeFilter === filter.id
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'bg-white text-gray-600 hover:bg-blue-50 hover:text-blue-600 shadow-md'
              }`}
            >
              {filter.label}
            </button>
          ))}
        </motion.div>

        {/* Case Studies Grid */}
        <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-16">
          <AnimatePresence mode="wait">
            {filteredCases.map((caseStudy, index) => (
              <motion.div
                key={caseStudy.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <GlassCard className="h-full hover:scale-105 transition-transform duration-300 cursor-pointer">
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-4">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        caseStudy.industry === 'landscaping' ? 'bg-green-100 text-green-800' :
                        caseStudy.industry === 'roofing' ? 'bg-orange-100 text-orange-800' :
                        'bg-purple-100 text-purple-800'
                      }`}>
                        {caseStudy.industry.charAt(0).toUpperCase() + caseStudy.industry.slice(1).replace('-', ' ')}
                      </span>
                      <div className="flex items-center text-gray-500 text-sm">
                        <MapPin className="h-4 w-4 mr-1" />
                        {caseStudy.location}
                      </div>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{caseStudy.companyName}</h3>
                    <p className="text-gray-600 text-sm mb-4">{caseStudy.challenge}</p>
                  </div>

                  {/* Key Results */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    {caseStudy.results.slice(0, 2).map((result, idx) => (
                      <div key={idx} className="text-center">
                        <result.icon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                        <div className="text-2xl font-bold text-gray-900">
                          {result.improvement}
                        </div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>

                  {/* Testimonial Preview */}
                  <div className="mb-6">
                    <div className="flex items-center mb-2">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <p className="text-gray-700 text-sm italic line-clamp-3">
                      "{caseStudy.testimonial.quote.substring(0, 120)}..."
                    </p>
                    <p className="text-sm text-gray-600 mt-2">
                      - {caseStudy.testimonial.author}
                    </p>
                  </div>

                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full"
                    onClick={() => setSelectedCase(caseStudy)}
                  >
                    View Full Case Study
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </GlassCard>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <GradientCard gradient="blue" className="max-w-4xl mx-auto">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to Be Our Next Success Story?
            </h3>
            <p className="text-xl text-gray-600 mb-8">
              Join hundreds of local service providers who have transformed their businesses 
              with our proven digital marketing strategies.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="primary"
                size="lg"
              >
                <Calendar className="mr-2 h-5 w-5" />
                Schedule Free Strategy Session
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                onClick={() => {
                  const contactSection = document.getElementById('contact');
                  if (contactSection) {
                    contactSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
              >
                Get Custom Proposal
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </GradientCard>
        </motion.div>
      </div>

      {/* Detailed Case Study Modal would go here */}
      {/* This would be implemented as a separate modal component */}
    </Section>
  );
};
