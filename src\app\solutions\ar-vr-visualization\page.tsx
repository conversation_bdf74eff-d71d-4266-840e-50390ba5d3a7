import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowRight, Eye, Smartphone, Monitor, Zap, Star, CheckCircle, Phone, Camera } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'AR/VR Design Visualization for Landscaping Businesses | GroundUP Digital',
  description: 'Immersive AR and VR technology that helps landscaping businesses showcase designs, close more deals, and wow clients with interactive presentations.',
  keywords: 'AR landscaping design, VR landscape visualization, augmented reality landscaping, virtual reality landscape design, immersive landscaping presentations',
};

const arvrFeatures = [
  {
    icon: Eye,
    title: 'Immersive Client Presentations',
    description: 'Show clients exactly how their landscaping project will look before breaking ground.',
    benefits: ['3D landscape visualization', 'Virtual property walkthroughs', 'Real-time design modifications', 'Interactive client experiences']
  },
  {
    icon: Smartphone,
    title: 'Mobile AR Solutions',
    description: 'Use smartphones and tablets to overlay landscape designs directly onto client properties.',
    benefits: ['On-site design visualization', 'Instant client feedback', 'Mobile-friendly presentations', 'Easy sharing capabilities']
  },
  {
    icon: Monitor,
    title: 'VR Design Studios',
    description: 'Create fully immersive virtual environments where clients can experience their future landscape.',
    benefits: ['360-degree landscape views', 'Seasonal visualization', 'Material and plant selection', 'Virtual design collaboration']
  }
];

const visualizationTypes = [
  {
    name: 'Augmented Reality (AR)',
    description: 'Overlay digital landscape designs onto real-world property views',
    applications: ['On-site client presentations', 'Property assessment tools', 'Design modification previews', 'Marketing demonstrations']
  },
  {
    name: 'Virtual Reality (VR)',
    description: 'Fully immersive landscape experiences in virtual environments',
    applications: ['Detailed design walkthroughs', 'Seasonal change previews', 'Material selection experiences', 'Client decision-making tools']
  },
  {
    name: '3D Modeling & Rendering',
    description: 'Photorealistic 3D models of landscape designs and installations',
    applications: ['Proposal presentations', 'Marketing materials', 'Design documentation', 'Client approval processes']
  },
  {
    name: 'Interactive Presentations',
    description: 'Dynamic presentations that engage clients and close more deals',
    applications: ['Sales presentations', 'Design consultations', 'Project planning', 'Client education']
  }
];

const packages = [
  {
    name: "AR Starter",
    price: "$2,497",
    period: "/month",
    description: "Essential AR tools for landscaping businesses ready to enhance client presentations",
    features: [
      "Mobile AR app development",
      "Basic 3D design library",
      "Client presentation training",
      "Monthly design updates",
      "Technical support"
    ],
    popular: false
  },
  {
    name: "VR Pro Suite",
    price: "$4,997",
    period: "/month", 
    description: "Comprehensive AR/VR solution for landscaping companies ready to revolutionize client experiences",
    features: [
      "Full AR/VR platform access",
      "Custom 3D asset creation",
      "VR headset integration",
      "Advanced visualization tools",
      "Dedicated design specialist",
      "Quarterly content updates"
    ],
    popular: true
  },
  {
    name: "Immersive Enterprise",
    price: "$9,997",
    period: "/month",
    description: "Enterprise AR/VR solution for large landscaping businesses seeking market leadership",
    features: [
      "Custom AR/VR development",
      "White-label solutions",
      "Advanced analytics platform",
      "Multi-location deployment",
      "Dedicated development team",
      "Ongoing innovation support"
    ],
    popular: false
  }
];

const caseStudy = {
  company: "Visionary Landscapes",
  location: "Los Angeles, CA",
  results: [
    { metric: "Client Approval Rate", improvement: "+85%" },
    { metric: "Project Value", improvement: "+120%" },
    { metric: "Sales Cycle Time", improvement: "-40%" },
    { metric: "Client Satisfaction", improvement: "+95%" }
  ]
};

const benefits = [
  { icon: Zap, title: "Faster Sales Cycles", description: "Close deals 40% faster with immersive presentations" },
  { icon: Eye, title: "Higher Project Values", description: "Increase average project size by 120%" },
  { icon: Star, title: "Improved Client Satisfaction", description: "95% client satisfaction with AR/VR presentations" },
  { icon: Camera, title: "Competitive Advantage", description: "Stand out from competitors with cutting-edge technology" }
];

export default function ARVRVisualizationPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Eye className="w-4 h-4" />
              <span className="text-sm font-semibold">AR/VR Visualization for Landscaping</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Immersive Design Visualization for{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Modern Landscaping Businesses
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Revolutionary AR and VR technology that helps landscaping business owners showcase designs, 
              close more deals, and provide unforgettable client experiences.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Eye className="w-5 h-5 mr-2" />
                Experience AR/VR Demo
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                View Portfolio
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '85%', label: 'Higher Approval Rate' },
                { number: '120%', label: 'Project Value Increase' },
                { number: '40%', label: 'Faster Sales Cycles' },
                { number: '30+', label: 'AR/VR Projects' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Benefits Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Why Landscaping Businesses Choose AR/VR Visualization
            </h2>
            <p className="text-lg text-gray-600">
              Transform how you present landscape designs to clients. Our AR/VR solutions help landscaping business owners 
              close more deals, increase project values, and deliver exceptional client experiences.
            </p>
          </div>
          
          <div className="grid md:grid-cols-4 gap-6">
            {benefits.map((benefit, index) => (
              <Card key={index} className="text-center h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <benefit.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">{benefit.title}</h3>
                  <p className="text-sm text-gray-600">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Advanced AR/VR Solutions for Landscaping Professionals
            </h2>
            <p className="text-lg text-gray-600">
              Comprehensive visualization technology designed specifically for landscaping business owners. 
              Every tool is crafted to enhance your client presentations and grow your business.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {arvrFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Visualization Types Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Complete Visualization Solutions for Every Landscaping Need
            </h2>
            <p className="text-lg text-gray-600">
              From mobile AR apps to immersive VR experiences, we provide the full spectrum of visualization technology 
              for landscaping business owners.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {visualizationTypes.map((type, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{type.name}</h3>
                  <p className="text-gray-600 mb-4">{type.description}</p>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900 text-sm">Applications:</h4>
                    <ul className="space-y-1">
                      {type.applications.map((application, idx) => (
                        <li key={idx} className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="w-3 h-3 text-green-500 mr-2 flex-shrink-0" />
                          {application}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                AR/VR Success Story: Transforming Client Experiences
              </h2>
              <p className="text-lg text-gray-600">
                See how {caseStudy.company} used our AR/VR technology to revolutionize their client presentations and grow their business
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A premium landscaping business in {caseStudy.location} wanted to differentiate themselves with cutting-edge technology. 
                      Our AR/VR solutions helped them close bigger deals and provide unforgettable client experiences.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Watch AR/VR Demo
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              AR/VR Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Revolutionary visualization technology designed specifically for landscaping business owners. 
              Choose the AR/VR solution that transforms your client presentations.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Transform Your Landscaping Presentations with AR/VR?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Experience the future of landscape design presentation. See how AR/VR technology can help your landscaping business 
              close more deals and wow clients.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Eye className="w-5 h-5 mr-2" />
                Experience AR/VR Demo
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule Consultation
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
