import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Digital Marketing for Roofing Companies | GroundUPDigital',
  description: 'Specialized digital marketing services for roofing businesses. Generate more leads, dominate local search, and grow your roofing company with our proven strategies.',
  keywords: 'roofing marketing, roofing SEO, roofing leads, roof repair marketing, roofing contractor marketing',
  openGraph: {
    title: 'Digital Marketing for Roofing Companies | GroundUPDigital',
    description: 'Specialized digital marketing services for roofing businesses. Generate more leads, dominate local search, and grow your roofing company with our proven strategies.',
    type: 'website',
  },
};

export default function RoofersPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Digital Marketing for <span className="text-blue-600">Roofing Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Grow your roofing business with our specialized digital marketing services. From emergency repairs 
              to new installations, we help you attract more customers and increase revenue year-round.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Free Marketing Audit
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                View Success Stories
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Proven Results for Roofing Businesses
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '400%', label: 'Average Lead Increase', icon: '📈' },
                { metric: '$150K', label: 'Additional Annual Revenue', icon: '💰' },
                { metric: '#1', label: 'Local Search Rankings', icon: '🏆' },
                { metric: '4.9★', label: 'Average Review Rating', icon: '⭐' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Specialized Services for Roofers
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: 'Roofing SEO',
                  description: 'Dominate local search results for roofing services in your area.',
                  features: ['Local keyword optimization', 'Google My Business optimization', 'Emergency service SEO', 'Competitor analysis'],
                  link: '/roofers/seo'
                },
                {
                  title: 'Roofing Web Design',
                  description: 'Professional websites that convert visitors into roofing customers.',
                  features: ['Emergency contact forms', 'Insurance claim assistance', 'Photo galleries', 'Mobile optimization'],
                  link: '/roofers/web-design'
                },
                {
                  title: 'Roofing PPC',
                  description: 'Targeted advertising campaigns that generate immediate roofing leads.',
                  features: ['Google Ads management', 'Storm response campaigns', 'Local targeting', 'Emergency service ads'],
                  link: '/roofers/ppc'
                },
                {
                  title: 'Reputation Management',
                  description: 'Build trust and credibility with comprehensive reputation management.',
                  features: ['Review generation', 'Response management', 'Crisis management', 'Trust building'],
                  link: '/roofers/reputation-management'
                },
                {
                  title: 'Roofing CRM',
                  description: 'Custom CRM solutions designed specifically for roofing businesses.',
                  features: ['Lead management', 'Project tracking', 'Insurance integration', 'Customer communication'],
                  link: '/roofers/crm-solution'
                },
                {
                  title: 'Custom Roofing Software',
                  description: 'Tailored software solutions to streamline your roofing operations.',
                  features: ['Estimate generation', 'Project management', 'Material tracking', 'Client portals'],
                  link: '/roofers/custom-software'
                }
              ].map((service, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-2 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <a 
                    href={service.link}
                    className="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700"
                  >
                    Learn More →
                  </a>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Roofing Challenges */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Common Roofing Business Challenges We Solve
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Challenges:</h3>
                <ul className="space-y-4">
                  {[
                    'Seasonal demand fluctuations and weather dependency',
                    'High competition from national roofing chains',
                    'Complex insurance claim processes',
                    'Emergency service response expectations',
                    'Trust and credibility concerns from homeowners',
                    'Difficulty showcasing quality workmanship online'
                  ].map((challenge, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{challenge}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Our Solutions:</h3>
                <ul className="space-y-4">
                  {[
                    'Year-round marketing with storm response optimization',
                    'Local SEO dominance and competitive differentiation',
                    'Insurance claim assistance and partnership marketing',
                    'Emergency service SEO and rapid response campaigns',
                    'Reputation management and trust-building strategies',
                    'Visual portfolio websites with before/after galleries'
                  ].map((solution, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{solution}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Types */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Roofing Services We Market
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { service: 'Roof Repairs', icon: '🔨' },
                { service: 'New Roof Installation', icon: '🏠' },
                { service: 'Storm Damage Restoration', icon: '⛈️' },
                { service: 'Roof Inspections', icon: '🔍' },
                { service: 'Gutter Services', icon: '🌊' },
                { service: 'Emergency Repairs', icon: '🚨' },
                { service: 'Commercial Roofing', icon: '🏢' },
                { service: 'Insurance Claims', icon: '📋' }
              ].map((type, index) => (
                <div key={index} className="text-center bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{type.icon}</div>
                  <h3 className="font-semibold text-gray-900">{type.service}</h3>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study Preview */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Success Story: Elite Roofing Solutions
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    From 5 Leads to 75+ Leads Per Month
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Elite Roofing Solutions was struggling to compete with larger roofing companies and 
                    generate consistent leads. Our comprehensive digital marketing strategy transformed their business.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">400%</div>
                      <div className="text-sm text-gray-600">Lead Increase</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">$200K</div>
                      <div className="text-sm text-gray-600">Additional Revenue</div>
                    </div>
                  </div>
                  <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8 text-center">
                  <div className="text-6xl mb-4">🏆</div>
                  <p className="text-gray-600 italic">
                    "GroundUPDigital completely transformed our roofing business. We went from struggling to find customers 
                    to having more work than we can handle. Their roofing industry expertise really shows."
                  </p>
                  <div className="mt-4 font-semibold text-gray-900">
                    - Tom Wilson, Owner
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Emergency Response Marketing */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Storm Response Marketing
              </h2>
              <p className="text-xl text-blue-100">
                Be the first roofing company customers call when storms hit your area.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Weather Monitoring',
                  description: 'Automated campaigns triggered by severe weather in your service area.',
                  icon: '🌩️'
                },
                {
                  feature: 'Emergency SEO',
                  description: 'Rapid optimization for emergency roofing keywords during storm events.',
                  icon: '🚨'
                },
                {
                  feature: 'Crisis Communication',
                  description: 'Pre-planned messaging and rapid response communication strategies.',
                  icon: '📢'
                }
              ].map((feature, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{feature.feature}</h3>
                  <p className="text-blue-100">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Grow Your Roofing Business?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Get a free marketing audit and discover how we can help you attract more customers and increase revenue.
            </p>
            <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Get Your Free Roofing Marketing Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
