import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Digital Marketing for Pest Control Companies | GroundUPDigital',
  description: 'Specialized digital marketing services for pest control businesses. Generate more leads, dominate local search, and grow your pest control company with our proven strategies.',
  keywords: 'pest control marketing, pest control SEO, pest control leads, exterminator marketing, pest management marketing',
  openGraph: {
    title: 'Digital Marketing for Pest Control Companies | GroundUPDigital',
    description: 'Specialized digital marketing services for pest control businesses. Generate more leads, dominate local search, and grow your pest control company with our proven strategies.',
    type: 'website',
  },
};

export default function PestControlPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-yellow-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Digital Marketing for <span className="text-green-600">Pest Control Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Grow your pest control business with our specialized digital marketing services. From emergency 
              extermination to preventive treatments, we help you attract more customers and increase revenue year-round.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Get Free Marketing Audit
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View Success Stories
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Proven Results for Pest Control Businesses
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '450%', label: 'Average Lead Increase', icon: '📈' },
                { metric: '$120K', label: 'Additional Annual Revenue', icon: '💰' },
                { metric: '#1', label: 'Local Search Rankings', icon: '🏆' },
                { metric: '4.8★', label: 'Average Review Rating', icon: '⭐' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-green-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Specialized Services for Pest Control
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: 'Pest Control SEO',
                  description: 'Dominate local search results for pest control services in your area.',
                  features: ['Local keyword optimization', 'Google My Business optimization', 'Emergency service SEO', 'Seasonal optimization'],
                  link: '/pest-control/seo'
                },
                {
                  title: 'Pest Control App Development',
                  description: 'Custom mobile apps for pest control businesses and customer management.',
                  features: ['Service scheduling', 'Treatment tracking', 'Customer portals', 'Inspection reports'],
                  link: '/pest-control/app-development'
                },
                {
                  title: 'Pest Control Web Design',
                  description: 'Professional websites that convert visitors into pest control customers.',
                  features: ['Emergency contact forms', 'Service area mapping', 'Treatment galleries', 'Mobile optimization'],
                  link: '/pest-control/web-design'
                },
                {
                  title: 'Pest Control PPC',
                  description: 'Targeted advertising campaigns that generate immediate pest control leads.',
                  features: ['Google Ads management', 'Seasonal campaigns', 'Emergency service ads', 'Local targeting'],
                  link: '/pest-control/ppc'
                },
                {
                  title: 'Reputation Management',
                  description: 'Build trust and credibility with comprehensive reputation management.',
                  features: ['Review generation', 'Response management', 'Crisis management', 'Trust building'],
                  link: '/pest-control/reputation-management'
                },
                {
                  title: 'Pest Control CRM',
                  description: 'Custom CRM solutions designed specifically for pest control businesses.',
                  features: ['Customer management', 'Treatment tracking', 'Recurring services', 'Chemical inventory'],
                  link: '/pest-control/crm-solution'
                }
              ].map((service, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-2 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <a 
                    href={service.link}
                    className="inline-flex items-center text-green-600 font-semibold hover:text-green-700"
                  >
                    Learn More →
                  </a>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Pest Control Challenges */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Common Pest Control Business Challenges We Solve
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Challenges:</h3>
                <ul className="space-y-4">
                  {[
                    'Seasonal pest activity and demand fluctuations',
                    'Emergency service response expectations',
                    'Trust and safety concerns from customers',
                    'Complex treatment tracking and compliance',
                    'Recurring service customer retention',
                    'Competing with national pest control chains'
                  ].map((challenge, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{challenge}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Our Solutions:</h3>
                <ul className="space-y-4">
                  {[
                    'Year-round marketing with seasonal pest optimization',
                    'Emergency service SEO and rapid response campaigns',
                    'Trust-building strategies and safety certifications',
                    'Custom CRM with treatment tracking and compliance',
                    'Automated retention campaigns and service reminders',
                    'Local SEO dominance and competitive differentiation'
                  ].map((solution, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{solution}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Types */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Pest Control Services We Market
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { service: 'General Pest Control', icon: '🐛' },
                { service: 'Termite Treatment', icon: '🐜' },
                { service: 'Rodent Control', icon: '🐭' },
                { service: 'Bed Bug Treatment', icon: '🛏️' },
                { service: 'Wildlife Removal', icon: '🦝' },
                { service: 'Mosquito Control', icon: '🦟' },
                { service: 'Commercial Services', icon: '🏢' },
                { service: 'Preventive Treatments', icon: '🛡️' }
              ].map((type, index) => (
                <div key={index} className="text-center bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{type.icon}</div>
                  <h3 className="font-semibold text-gray-900">{type.service}</h3>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Seasonal Marketing */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seasonal Pest Control Marketing
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  season: 'Spring',
                  pests: ['Ants', 'Termites', 'Wasps'],
                  strategy: 'Prevention campaigns and early treatment promotions',
                  icon: '🌸'
                },
                {
                  season: 'Summer',
                  pests: ['Mosquitoes', 'Flies', 'Spiders'],
                  strategy: 'Outdoor pest control and family safety messaging',
                  icon: '☀️'
                },
                {
                  season: 'Fall',
                  pests: ['Rodents', 'Stink Bugs', 'Spiders'],
                  strategy: 'Winter preparation and home sealing services',
                  icon: '🍂'
                },
                {
                  season: 'Winter',
                  pests: ['Mice', 'Rats', 'Cockroaches'],
                  strategy: 'Indoor pest control and maintenance contracts',
                  icon: '❄️'
                }
              ].map((season, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                  <div className="text-4xl mb-4">{season.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{season.season}</h3>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-700 mb-2">Target Pests:</h4>
                    <ul className="text-sm text-gray-600">
                      {season.pests.map((pest, pestIndex) => (
                        <li key={pestIndex}>{pest}</li>
                      ))}
                    </ul>
                  </div>
                  <p className="text-sm text-gray-600">{season.strategy}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study Preview */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Success Story: Guardian Pest Control
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    From 8 Leads to 90+ Leads Per Month
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Guardian Pest Control was struggling to compete with larger pest control companies and 
                    generate consistent leads. Our comprehensive digital marketing strategy transformed their business.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">450%</div>
                      <div className="text-sm text-gray-600">Lead Increase</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">$180K</div>
                      <div className="text-sm text-gray-600">Additional Revenue</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8 text-center">
                  <div className="text-6xl mb-4">🏆</div>
                  <p className="text-gray-600 italic">
                    "GroundUPDigital completely transformed our pest control business. We went from struggling to find customers 
                    to having a waiting list. Their pest control industry expertise really shows."
                  </p>
                  <div className="mt-4 font-semibold text-gray-900">
                    - Maria Garcia, Owner
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Grow Your Pest Control Business?
            </h2>
            <p className="text-xl text-green-100 mb-8">
              Get a free marketing audit and discover how we can help you attract more customers and increase revenue.
            </p>
            <button className="bg-white text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Get Your Free Pest Control Marketing Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
