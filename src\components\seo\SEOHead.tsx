import React from 'react';
import Head from 'next/head';

interface SEOHeadProps {
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  ogImage?: string;
  ogType?: string;
  noIndex?: boolean;
  structuredData?: any;
  additionalMeta?: Array<{
    name?: string;
    property?: string;
    content: string;
  }>;
}

export function SEOHead({
  title,
  description,
  keywords,
  canonical,
  ogImage = 'https://www.groundupdigital.com/og-image.jpg',
  ogType = 'website',
  noIndex = false,
  structuredData,
  additionalMeta = []
}: SEOHeadProps) {
  const fullTitle = title.includes('GroundUPDigital') ? title : `${title} | GroundUPDigital`;
  const url = canonical || 'https://www.groundupdigital.com';

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}
      <meta name="author" content="GroundUPDigital" />
      <meta name="robots" content={noIndex ? 'noindex,nofollow' : 'index,follow'} />
      <link rel="canonical" href={url} />

      {/* Open Graph Meta Tags */}
      <meta property="og:type" content={ogType} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:url" content={url} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="GroundUPDigital" />
      <meta property="og:locale" content="en_US" />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      <meta name="twitter:creator" content="@groundupdigital" />

      {/* Additional Meta Tags */}
      {additionalMeta.map((meta, index) => (
        <meta
          key={index}
          {...(meta.name ? { name: meta.name } : { property: meta.property })}
          content={meta.content}
        />
      ))}

      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      )}

      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />

      {/* Preconnect to External Domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://www.google-analytics.com" />
      <link rel="preconnect" href="https://www.googletagmanager.com" />

      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />

      {/* Viewport and Mobile Optimization */}
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="theme-color" content="#2563eb" />

      {/* Security Headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />

      {/* Language and Locale */}
      <meta httpEquiv="content-language" content="en-US" />
      <html lang="en" />
    </Head>
  );
}

// Predefined SEO configurations for common page types
export const seoConfigs = {
  homepage: {
    title: 'Digital Marketing for Local Service Businesses | GroundUPDigital',
    description: 'Grow your landscaping, roofing, or pest control business with our specialized digital marketing services. Increase leads by 300%+ with our proven strategies.',
    keywords: 'digital marketing, local service marketing, landscaping marketing, roofing marketing, pest control marketing, SEO, PPC, web design'
  },
  
  about: {
    title: 'About GroundUPDigital | Digital Marketing Experts for Local Service Businesses',
    description: 'Learn about GroundUPDigital\'s mission to help landscaping, roofing, and pest control businesses grow through specialized digital marketing strategies.',
    keywords: 'about GroundUPDigital, digital marketing team, local service marketing experts'
  },

  services: {
    title: 'Digital Marketing Services for Local Service Businesses | GroundUPDigital',
    description: 'Comprehensive digital marketing services including web development, SEO, PPC, social media, and lead generation for landscaping, roofing, and pest control companies.',
    keywords: 'digital marketing services, web development, SEO services, PPC advertising, social media marketing, lead generation'
  },

  landscaping: {
    title: 'Digital Marketing for Landscaping Companies | GroundUPDigital',
    description: 'Specialized digital marketing services for landscaping businesses. Increase leads, dominate local search, and grow your landscaping company.',
    keywords: 'landscaping marketing, landscaping SEO, landscaping leads, lawn care marketing, landscape design marketing'
  },

  roofing: {
    title: 'Digital Marketing for Roofing Companies | GroundUPDigital',
    description: 'Specialized digital marketing services for roofing businesses. Generate more leads, dominate local search, and grow your roofing company.',
    keywords: 'roofing marketing, roofing SEO, roofing leads, roof repair marketing, roofing contractor marketing'
  },

  pestControl: {
    title: 'Digital Marketing for Pest Control Companies | GroundUPDigital',
    description: 'Specialized digital marketing services for pest control businesses. Generate more leads, dominate local search, and grow your pest control company.',
    keywords: 'pest control marketing, pest control SEO, pest control leads, exterminator marketing, pest management marketing'
  }
};

// Helper function to generate industry-specific SEO config
export function getIndustrySEOConfig(industry: 'landscaping' | 'roofing' | 'pestControl', service?: string) {
  const baseConfig = seoConfigs[industry];
  
  if (service) {
    return {
      ...baseConfig,
      title: `${service} for ${industry === 'pestControl' ? 'Pest Control' : industry.charAt(0).toUpperCase() + industry.slice(1)} Companies | GroundUPDigital`,
      description: `Professional ${service.toLowerCase()} services for ${industry === 'pestControl' ? 'pest control' : industry} businesses. ${baseConfig.description}`,
      keywords: `${industry} ${service.toLowerCase()}, ${baseConfig.keywords}`
    };
  }
  
  return baseConfig;
}
