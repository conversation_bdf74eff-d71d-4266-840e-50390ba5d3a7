'use client';

import React from 'react';
import { Container } from '@/components/ui/Container';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  Tv, 
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';

const breakpoints = [
  { name: 'Mobile (xs)', width: '320px', icon: Smartphone, color: 'text-red-600' },
  { name: 'Mobile (sm)', width: '640px', icon: Smartphone, color: 'text-orange-600' },
  { name: 'Tablet (md)', width: '768px', icon: Tablet, color: 'text-yellow-600' },
  { name: 'Laptop (lg)', width: '1024px', icon: Monitor, color: 'text-green-600' },
  { name: 'Desktop (xl)', width: '1280px', icon: Monitor, color: 'text-blue-600' },
  { name: 'Large (2xl)', width: '1536px', icon: Tv, color: 'text-purple-600' },
];

const testComponents = [
  'Typography scaling',
  'Button touch targets',
  'Navigation menu',
  'Grid layouts',
  'Card components',
  'Form elements',
  'Image responsiveness',
  'Spacing consistency'
];

export default function ResponsiveTestPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <Container className="py-6">
          <div className="text-center">
            <h1 className="heading-1 text-gray-900 mb-4">
              Responsive Design Test Page
            </h1>
            <p className="text-body text-gray-600 max-w-2xl mx-auto">
              This page tests all responsive design elements across different screen sizes.
              Resize your browser or test on different devices to validate responsiveness.
            </p>
          </div>
        </Container>
      </div>

      {/* Current Breakpoint Indicator */}
      <div className="bg-blue-600 text-white py-2">
        <Container>
          <div className="flex items-center justify-center gap-2 text-sm">
            <Info className="w-4 h-4" />
            <span className="xs:hidden">Current: XS (320px+)</span>
            <span className="hidden xs:block sm:hidden">Current: SM (640px+)</span>
            <span className="hidden sm:block md:hidden">Current: MD (768px+)</span>
            <span className="hidden md:block lg:hidden">Current: LG (1024px+)</span>
            <span className="hidden lg:block xl:hidden">Current: XL (1280px+)</span>
            <span className="hidden xl:block">Current: 2XL (1536px+)</span>
          </div>
        </Container>
      </div>

      <Container className="section-padding">
        {/* Breakpoints Overview */}
        <section className="mb-12">
          <h2 className="heading-3 text-gray-900 mb-6">Breakpoints Overview</h2>
          <div className="grid-responsive-3 gap-4">
            {breakpoints.map((bp, index) => {
              const Icon = bp.icon;
              return (
                <Card key={index} className="text-center">
                  <CardContent>
                    <Icon className={`w-8 h-8 mx-auto mb-3 ${bp.color}`} />
                    <h3 className="font-semibold text-gray-900 mb-1">{bp.name}</h3>
                    <p className="text-sm text-gray-600">{bp.width}+</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </section>

        {/* Typography Test */}
        <section className="mb-12">
          <h2 className="heading-3 text-gray-900 mb-6">Typography Scaling</h2>
          <Card>
            <CardContent className="space-y-4">
              <h1 className="heading-1 text-gray-900">Heading 1 - Main Title</h1>
              <h2 className="heading-2 text-gray-900">Heading 2 - Section Title</h2>
              <h3 className="heading-3 text-gray-900">Heading 3 - Subsection</h3>
              <h4 className="heading-4 text-gray-900">Heading 4 - Component Title</h4>
              <p className="text-large text-gray-700">Large text for important content</p>
              <p className="text-body text-gray-600">Body text for regular content</p>
              <p className="text-small text-gray-500">Small text for captions and notes</p>
            </CardContent>
          </Card>
        </section>

        {/* Button Test */}
        <section className="mb-12">
          <h2 className="heading-3 text-gray-900 mb-6">Button Touch Targets</h2>
          <Card>
            <CardContent>
              <div className="space-y-4">
                <div className="flex flex-col xs:flex-row gap-3">
                  <Button variant="primary" size="sm">Small Button</Button>
                  <Button variant="primary" size="md">Medium Button</Button>
                  <Button variant="primary" size="lg">Large Button</Button>
                </div>
                <div className="flex flex-col xs:flex-row gap-3">
                  <Button variant="secondary" size="md">Secondary</Button>
                  <Button variant="outline" size="md">Outline</Button>
                  <Button variant="ghost" size="md">Ghost</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Grid Layout Test */}
        <section className="mb-12">
          <h2 className="heading-3 text-gray-900 mb-6">Grid Layouts</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-3">Responsive 2-Column Grid</h3>
              <div className="grid-responsive-2 gap-4">
                {[1, 2, 3, 4].map(i => (
                  <Card key={i}>
                    <CardContent className="text-center py-8">
                      <p className="text-gray-600">Grid Item {i}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Responsive 3-Column Grid</h3>
              <div className="grid-responsive-3 gap-4">
                {[1, 2, 3, 4, 5, 6].map(i => (
                  <Card key={i}>
                    <CardContent className="text-center py-6">
                      <p className="text-gray-600">Item {i}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Responsive 4-Column Grid</h3>
              <div className="grid-responsive-4 gap-4">
                {[1, 2, 3, 4, 5, 6, 7, 8].map(i => (
                  <Card key={i}>
                    <CardContent className="text-center py-4">
                      <p className="text-gray-600">{i}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Component Test Checklist */}
        <section className="mb-12">
          <h2 className="heading-3 text-gray-900 mb-6">Responsive Component Checklist</h2>
          <Card>
            <CardContent>
              <div className="grid-responsive-2 gap-4">
                {testComponents.map((component, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                    <span className="text-gray-700">{component}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Spacing Test */}
        <section className="mb-12">
          <h2 className="heading-3 text-gray-900 mb-6">Responsive Spacing</h2>
          <div className="space-y-4">
            <Card className="spacing-responsive-sm bg-blue-50">
              <p className="text-center text-blue-700 font-medium">Small Responsive Spacing</p>
            </Card>
            <Card className="spacing-responsive-md bg-green-50">
              <p className="text-center text-green-700 font-medium">Medium Responsive Spacing</p>
            </Card>
            <Card className="spacing-responsive-lg bg-purple-50">
              <p className="text-center text-purple-700 font-medium">Large Responsive Spacing</p>
            </Card>
          </div>
        </section>

        {/* Test Instructions */}
        <section>
          <Card className="bg-yellow-50 border-yellow-200">
            <CardHeader>
              <div className="flex items-center gap-2">
                <AlertCircle className="w-5 h-5 text-yellow-600" />
                <h3 className="text-lg font-semibold text-yellow-800">Testing Instructions</h3>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-yellow-700 space-y-2">
                <p>• Resize your browser window to test different breakpoints</p>
                <p>• Check that all text remains readable at all sizes</p>
                <p>• Verify that buttons maintain proper touch targets (44px minimum)</p>
                <p>• Ensure grids reflow appropriately on smaller screens</p>
                <p>• Test on actual mobile devices for real-world validation</p>
                <p>• Check that no horizontal scrolling occurs on any screen size</p>
              </div>
            </CardContent>
          </Card>
        </section>
      </Container>
    </div>
  );
}
