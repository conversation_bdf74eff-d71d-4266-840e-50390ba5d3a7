import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, Phone, Mail, MapPin, Clock, CheckCircle, Users, Award } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Partner with GroundUP Digital | Exclusive Landscaping Business Growth Partnership',
  description: 'Start your partnership with GroundUP Digital. Exclusive digital growth solutions for landscaping business owners. Schedule your consultation today.',
  keywords: 'GroundUP Digital partnership, landscaping marketing partnership, landscaping business growth consultation, digital marketing for landscapers',
};

const partnershipBenefits = [
  {
    icon: Users,
    title: 'Dedicated Partnership Team',
    description: 'Your own team of landscaping marketing specialists, account managers, and growth strategists.',
    benefits: ['Dedicated account manager', 'Landscaping industry experts', 'Strategic growth planning', 'Regular performance reviews']
  },
  {
    icon: Award,
    title: 'Proven Success Framework',
    description: 'Our battle-tested methodology that has helped 200+ landscaping businesses achieve remarkable growth.',
    benefits: ['247% average lead increase', '156% revenue growth', '89% client retention rate', 'Industry-specific strategies']
  },
  {
    icon: CheckCircle,
    title: 'Comprehensive Growth Ecosystem',
    description: 'Complete digital transformation covering every aspect of your landscaping business growth.',
    benefits: ['All-in-one platform', 'Integrated solutions', 'Seamless workflows', 'Ongoing optimization']
  }
];

const partnershipProcess = [
  {
    step: 1,
    title: 'Discovery Consultation',
    description: 'We analyze your current situation, goals, and growth opportunities.',
    duration: '60 minutes',
    deliverable: 'Custom growth strategy outline'
  },
  {
    step: 2,
    title: 'Strategic Planning',
    description: 'We develop a comprehensive digital growth plan tailored to your landscaping business.',
    duration: '1-2 weeks',
    deliverable: 'Detailed implementation roadmap'
  },
  {
    step: 3,
    title: 'Implementation & Launch',
    description: 'Our team implements all systems and strategies while training your team.',
    duration: '2-4 weeks',
    deliverable: 'Fully operational digital ecosystem'
  },
  {
    step: 4,
    title: 'Growth & Optimization',
    description: 'Continuous monitoring, optimization, and scaling of your digital presence.',
    duration: 'Ongoing',
    deliverable: 'Monthly performance reports & strategy updates'
  }
];

const contactInfo = {
  phone: '(*************',
  email: '<EMAIL>',
  address: '123 Digital Growth Blvd, Suite 100, Austin, TX 78701',
  hours: 'Monday - Friday: 8:00 AM - 6:00 PM CST'
};

const faqs = [
  {
    question: 'What makes GroundUP Digital different from other marketing agencies?',
    answer: 'We exclusively serve landscaping businesses. Every strategy, tool, and innovation is designed specifically for the landscaping industry. Our team understands your unique challenges and opportunities.'
  },
  {
    question: 'How quickly can I expect to see results?',
    answer: 'Most landscaping business owners see initial improvements within 30-60 days, with significant growth typically occurring within 3-6 months. Our average client sees a 247% increase in leads within the first year.'
  },
  {
    question: 'Do you work with landscaping businesses of all sizes?',
    answer: 'Yes, we work with landscaping businesses from solo operators to large commercial companies. Our solutions scale to match your business size and growth ambitions.'
  },
  {
    question: 'What is included in the partnership?',
    answer: 'Our partnership includes comprehensive digital marketing services, dedicated account management, ongoing strategy development, and access to our full suite of landscaping-specific tools and technologies.'
  }
];

export default function PartnershipPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Partner with the{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscaping Growth Experts
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Join 200+ landscaping business owners who have chosen GroundUP Digital as their exclusive digital growth partner. 
              Let's discuss how we can transform your landscaping business.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Phone className="w-5 h-5 mr-2" />
                Schedule Partnership Call
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                Download Partnership Guide
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '200+', label: 'Partner Businesses' },
                { number: '247%', label: 'Avg Lead Increase' },
                { number: '89%', label: 'Partnership Retention' },
                { number: '24/7', label: 'Support Available' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Partnership Benefits */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              What You Get as a GroundUP Digital Partner
            </h2>
            <p className="text-lg text-gray-600">
              Our partnership goes beyond traditional agency relationships. We become your dedicated digital growth team, 
              committed to your landscaping business success.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {partnershipBenefits.map((benefit, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <benefit.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{benefit.title}</h3>
                  <p className="text-gray-600 mb-4">{benefit.description}</p>
                  <ul className="space-y-2">
                    {benefit.benefits.map((item, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Partnership Process */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Our Partnership Process
            </h2>
            <p className="text-lg text-gray-600">
              A proven 4-step process that ensures your landscaping business gets the maximum value from our partnership.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {partnershipProcess.map((process, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold mr-3">
                      {process.step}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">{process.title}</h3>
                  </div>
                  <p className="text-gray-600 mb-4">{process.description}</p>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-500">Duration: {process.duration}</span>
                    <span className="text-blue-600 font-medium">{process.deliverable}</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Contact Information */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Ready to Start Your Partnership?
              </h2>
              <p className="text-lg text-gray-600">
                Contact our partnership team to schedule your consultation and begin your landscaping business transformation.
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 gap-8">
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Get in Touch</h3>
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <Phone className="w-5 h-5 text-blue-600 mr-3" />
                      <div>
                        <p className="font-medium text-gray-900">{contactInfo.phone}</p>
                        <p className="text-sm text-gray-600">Call for immediate assistance</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <Mail className="w-5 h-5 text-blue-600 mr-3" />
                      <div>
                        <p className="font-medium text-gray-900">{contactInfo.email}</p>
                        <p className="text-sm text-gray-600">Email for detailed inquiries</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <MapPin className="w-5 h-5 text-blue-600 mr-3 mt-1" />
                      <div>
                        <p className="font-medium text-gray-900">Office Location</p>
                        <p className="text-sm text-gray-600">{contactInfo.address}</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-5 h-5 text-blue-600 mr-3" />
                      <div>
                        <p className="font-medium text-gray-900">Business Hours</p>
                        <p className="text-sm text-gray-600">{contactInfo.hours}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Schedule Your Consultation</h3>
                  <div className="space-y-4">
                    <Button className="w-full bg-blue-600 text-white hover:bg-blue-700">
                      <Phone className="w-5 h-5 mr-2" />
                      Schedule Partnership Call
                    </Button>
                    <Button variant="outline" className="w-full">
                      <Mail className="w-5 h-5 mr-2" />
                      Send Partnership Inquiry
                    </Button>
                    <div className="text-center pt-4">
                      <p className="text-sm text-gray-600">
                        Prefer to talk first? Call us at{' '}
                        <a href={`tel:${contactInfo.phone}`} className="text-blue-600 font-medium">
                          {contactInfo.phone}
                        </a>
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </Container>
      </Section>

      {/* FAQ Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600">
              Common questions from landscaping business owners considering our partnership program.
            </p>
          </div>
          
          <div className="max-w-3xl mx-auto space-y-6">
            {faqs.map((faq, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{faq.question}</h3>
                  <p className="text-gray-600">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Final CTA */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Transform Your Landscaping Business Today
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Join the 200+ landscaping business owners who have chosen GroundUP Digital as their growth partner. 
              Your transformation starts with a single conversation.
            </p>
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
              <Phone className="w-5 h-5 mr-2" />
              Start Your Partnership Journey
            </Button>
          </div>
        </Container>
      </Section>
    </>
  );
}
