import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Marketing Automation for Landscaping Companies | Automate Lead Nurturing | GroundUPDigital',
  description: 'Automate your landscaping marketing with smart workflows. Nurture leads, retain customers, and grow revenue with automated email campaigns and customer journeys.',
  keywords: 'landscaping marketing automation, lawn care email marketing, landscape design automation, customer retention',
  openGraph: {
    title: 'Marketing Automation for Landscaping Companies | Automate Lead Nurturing | GroundUPDigital',
    description: 'Automate your landscaping marketing with smart workflows. Nurture leads, retain customers, and grow revenue with automated email campaigns and customer journeys.',
    type: 'website',
  },
};

export default function LandscapingMarketingAutomationPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-blue-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Marketing Automation for <span className="text-green-600">Landscaping Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Automate your landscaping marketing to nurture leads, retain customers, and grow revenue. 
              Smart workflows that work 24/7 to build relationships and drive business growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Get Automation Demo
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View Automation Examples
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Automation Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Transform Your Landscaping Marketing with Automation
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '300%', label: 'Increase in Lead Conversion', icon: '🎯' },
                { metric: '75%', label: 'Time Saved on Marketing Tasks', icon: '⏰' },
                { metric: '85%', label: 'Customer Retention Improvement', icon: '🤝' },
                { metric: '450%', label: 'ROI on Marketing Spend', icon: '💰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-green-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Automation Workflows */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Automated Workflows for Landscaping Businesses
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  workflow: 'Lead Nurturing Sequence',
                  description: 'Automatically nurture new leads with educational content and service information.',
                  triggers: ['Website form submission', 'Quote request', 'Phone inquiry', 'Social media engagement'],
                  actions: ['Welcome email series', 'Service information', 'Customer testimonials', 'Follow-up calls'],
                  icon: '🌱'
                },
                {
                  workflow: 'Seasonal Service Reminders',
                  description: 'Automated reminders for seasonal landscaping services based on customer history.',
                  triggers: ['Calendar dates', 'Weather conditions', 'Service history', 'Customer preferences'],
                  actions: ['Service reminders', 'Seasonal tips', 'Booking links', 'Special offers'],
                  icon: '🍂'
                },
                {
                  workflow: 'Customer Retention Campaign',
                  description: 'Keep existing customers engaged with regular communication and value-added content.',
                  triggers: ['Service completion', 'Anniversary dates', 'Inactivity periods', 'Satisfaction surveys'],
                  actions: ['Thank you messages', 'Maintenance tips', 'Loyalty rewards', 'Referral requests'],
                  icon: '🤝'
                },
                {
                  workflow: 'Project Follow-up Sequence',
                  description: 'Automated follow-up after project completion to ensure satisfaction and gather reviews.',
                  triggers: ['Project completion', 'Invoice payment', 'Time delays', 'Customer feedback'],
                  actions: ['Satisfaction surveys', 'Review requests', 'Maintenance offers', 'Photo requests'],
                  icon: '📋'
                },
                {
                  workflow: 'Abandoned Quote Recovery',
                  description: 'Re-engage prospects who requested quotes but haven\'t responded.',
                  triggers: ['Quote sent', 'No response', 'Time delays', 'Website revisits'],
                  actions: ['Follow-up emails', 'Value propositions', 'Limited-time offers', 'Personal calls'],
                  icon: '💰'
                },
                {
                  workflow: 'Referral Generation System',
                  description: 'Automatically request and reward customer referrals at optimal times.',
                  triggers: ['Project completion', 'Positive reviews', 'High satisfaction', 'Loyalty milestones'],
                  actions: ['Referral requests', 'Reward offers', 'Thank you messages', 'Social sharing'],
                  icon: '🎁'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.workflow}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-700 mb-2">Triggers:</h4>
                    <ul className="space-y-1">
                      {item.triggers.map((trigger, triggerIndex) => (
                        <li key={triggerIndex} className="text-sm text-gray-600">
                          • {trigger}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">Actions:</h4>
                    <ul className="space-y-1">
                      {item.actions.map((action, actionIndex) => (
                        <li key={actionIndex} className="text-sm text-gray-600">
                          • {action}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Seasonal Automation */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seasonal Marketing Automation
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  season: 'Spring',
                  campaigns: ['Spring cleanup reminders', 'Planting season promotions', 'Irrigation system startups', 'Mulching services'],
                  automation: 'Weather-triggered campaigns and service reminders',
                  timing: 'March - May',
                  icon: '🌸'
                },
                {
                  season: 'Summer',
                  campaigns: ['Watering tips and reminders', 'Pest control treatments', 'Lawn maintenance schedules', 'Drought management'],
                  automation: 'Temperature-based messaging and water conservation tips',
                  timing: 'June - August',
                  icon: '☀️'
                },
                {
                  season: 'Fall',
                  campaigns: ['Leaf removal services', 'Winterization reminders', 'Tree and shrub care', 'End-of-season cleanups'],
                  automation: 'Leaf-fall alerts and winter preparation sequences',
                  timing: 'September - November',
                  icon: '🍂'
                },
                {
                  season: 'Winter',
                  campaigns: ['Snow removal services', 'Holiday decorations', 'Planning next season', 'Equipment maintenance'],
                  automation: 'Weather alerts and planning consultation offers',
                  timing: 'December - February',
                  icon: '❄️'
                }
              ].map((season, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4 text-center">{season.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 text-center">{season.season}</h3>
                  <p className="text-sm text-green-600 font-semibold mb-4 text-center">{season.timing}</p>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-700 mb-2">Campaigns:</h4>
                    <ul className="space-y-1">
                      {season.campaigns.map((campaign, campaignIndex) => (
                        <li key={campaignIndex} className="text-sm text-gray-600">
                          • {campaign}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="bg-green-50 rounded-lg p-3">
                    <h5 className="font-semibold text-green-800 text-sm mb-1">Smart Automation:</h5>
                    <p className="text-green-700 text-xs">{season.automation}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Email Marketing Templates */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Professional Email Templates for Landscapers
              </h2>
              <p className="text-xl text-green-100">
                Ready-to-use email templates designed specifically for landscaping businesses.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  category: 'Lead Nurturing',
                  templates: ['Welcome series', 'Service education', 'Case studies', 'Testimonials', 'Consultation offers'],
                  description: 'Convert prospects into customers with educational and trust-building content.',
                  icon: '📧'
                },
                {
                  category: 'Customer Retention',
                  templates: ['Maintenance reminders', 'Seasonal tips', 'Loyalty rewards', 'Referral requests', 'Anniversary messages'],
                  description: 'Keep customers engaged and encourage repeat business.',
                  icon: '🔄'
                },
                {
                  category: 'Seasonal Campaigns',
                  templates: ['Spring cleanup', 'Summer care', 'Fall preparation', 'Winter planning', 'Holiday decorations'],
                  description: 'Timely campaigns that align with seasonal landscaping needs.',
                  icon: '🌿'
                }
              ].map((category, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{category.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{category.category}</h3>
                  <p className="text-green-100 mb-4">{category.description}</p>
                  <ul className="space-y-2">
                    {category.templates.map((template, templateIndex) => (
                      <li key={templateIndex} className="text-sm text-green-100">
                        • {template}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Integration & Analytics */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seamless Integration & Advanced Analytics
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Platform Integrations:</h3>
                <div className="grid grid-cols-2 gap-4">
                  {[
                    { platform: 'CRM Systems', tools: ['Salesforce', 'HubSpot', 'Pipedrive', 'Custom CRM'], icon: '👥' },
                    { platform: 'Email Platforms', tools: ['Mailchimp', 'Constant Contact', 'ActiveCampaign', 'ConvertKit'], icon: '📧' },
                    { platform: 'Website Tools', tools: ['WordPress', 'Squarespace', 'Wix', 'Custom Sites'], icon: '🌐' },
                    { platform: 'Social Media', tools: ['Facebook', 'Instagram', 'LinkedIn', 'Google My Business'], icon: '📱' }
                  ].map((integration, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg shadow-lg">
                      <div className="text-2xl mb-2 text-center">{integration.icon}</div>
                      <h4 className="font-semibold text-gray-900 mb-2">{integration.platform}</h4>
                      <ul className="space-y-1">
                        {integration.tools.map((tool, toolIndex) => (
                          <li key={toolIndex} className="text-xs text-gray-600">• {tool}</li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Analytics & Reporting:</h3>
                <ul className="space-y-4">
                  {[
                    'Email open and click-through rates',
                    'Lead conversion tracking and attribution',
                    'Customer lifetime value analysis',
                    'Campaign ROI and performance metrics',
                    'Seasonal trend analysis and forecasting',
                    'Customer segmentation and behavior insights'
                  ].map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-8 bg-green-50 rounded-lg p-6">
                  <h4 className="font-semibold text-green-800 mb-2">Real-Time Dashboard</h4>
                  <p className="text-green-700 text-sm">
                    Monitor all your marketing automation campaigns from a single dashboard 
                    with real-time metrics and actionable insights.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Marketing Automation Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    GreenThumb Landscapes: 400% Lead Conversion Increase
                  </h3>
                  <p className="text-gray-600 mb-6">
                    GreenThumb Landscapes was struggling with lead follow-up and customer retention. 
                    Our marketing automation system transformed their customer relationships and revenue.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">400%</div>
                      <div className="text-sm text-gray-600">Lead Conversion Increase</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">85%</div>
                      <div className="text-sm text-gray-600">Customer Retention Rate</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Automation Results:</h4>
                  <ul className="space-y-3">
                    {[
                      '400% increase in lead conversion rates',
                      '75% reduction in manual marketing tasks',
                      '85% improvement in customer retention',
                      '300% growth in recurring revenue',
                      '60% increase in average project value',
                      '90% customer satisfaction rating'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Implementation Timeline */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Marketing Automation Implementation Timeline
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                { week: 'Week 1-2', title: 'Strategy & Setup', tasks: ['Audit current marketing', 'Define automation goals', 'Platform selection', 'Initial configuration'], icon: '📋' },
                { week: 'Week 3-4', title: 'Content Creation', tasks: ['Email template design', 'Workflow development', 'Content writing', 'Asset preparation'], icon: '✍️' },
                { week: 'Week 5-6', title: 'Integration & Testing', tasks: ['System integrations', 'Workflow testing', 'Quality assurance', 'Team training'], icon: '🔧' },
                { week: 'Week 7+', title: 'Launch & Optimize', tasks: ['Campaign launch', 'Performance monitoring', 'Optimization', 'Ongoing support'], icon: '🚀' }
              ].map((phase, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                  <div className="text-4xl mb-4">{phase.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{phase.week}</h3>
                  <h4 className="text-lg font-semibold text-green-600 mb-4">{phase.title}</h4>
                  <ul className="space-y-2">
                    {phase.tasks.map((task, taskIndex) => (
                      <li key={taskIndex} className="text-sm text-gray-600">
                        • {task}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Automate Your Landscaping Marketing?
            </h2>
            <p className="text-xl text-green-100 mb-8">
              Stop losing leads and start nurturing customers automatically. Get a free marketing automation 
              consultation and see how we can transform your landscaping business.
            </p>
            <button className="bg-white text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Get Your Free Marketing Automation Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
