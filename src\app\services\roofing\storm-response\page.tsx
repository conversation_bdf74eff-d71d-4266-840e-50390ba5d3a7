import React from 'react';
import { Metadata } from 'next';
import { StormResponseMarketingContent } from '@/components/services/roofing/StormResponseMarketingContent';

export const metadata: Metadata = {
  title: 'Emergency Storm Response Marketing | GroundUP Digital - 24-Hour Storm Damage Lead Generation',
  description: 'Dominate storm seasons with our emergency response marketing. 500% more storm damage leads within 24 hours. Weather-triggered campaigns and instant lead capture systems.',
  keywords: 'storm damage marketing, emergency roofing marketing, hail damage marketing, storm response campaigns, weather triggered marketing, emergency roof repair leads',
  openGraph: {
    title: 'Emergency Storm Response Marketing | GroundUP Digital',
    description: 'Dominate storm seasons with our emergency response marketing. 500% more storm damage leads within 24 hours.',
    type: 'website',
    images: [
      {
        url: '/og-storm-response.jpg',
        width: 1200,
        height: 630,
        alt: 'Emergency Storm Response Marketing'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Emergency Storm Response Marketing | GroundUP Digital',
    description: 'Dominate storm seasons with our emergency response marketing. 500% more storm damage leads within 24 hours.',
    images: ['/og-storm-response.jpg']
  },
  alternates: {
    canonical: 'https://groundupdigital.com/services/roofing/storm-response'
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

// Structured Data for Storm Response Marketing
const stormResponseStructuredData = {
  "@context": "https://schema.org",
  "@type": "Service",
  "name": "Emergency Storm Response Marketing",
  "description": "Rapid-response digital marketing campaigns for roofing contractors during storm events and severe weather",
  "provider": {
    "@type": "Organization",
    "name": "GroundUP Digital",
    "url": "https://groundupdigital.com",
    "logo": "https://groundupdigital.com/logo.png"
  },
  "serviceType": "Emergency Digital Marketing",
  "areaServed": {
    "@type": "Country",
    "name": "United States"
  },
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Storm Response Marketing Services",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "24-Hour Emergency Campaign Deployment",
          "description": "Rapid deployment of storm damage marketing campaigns within 24 hours of severe weather events"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Weather-Triggered Automation",
          "description": "Automated marketing campaigns triggered by weather alerts and storm warnings"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Emergency Landing Pages",
          "description": "High-converting landing pages specifically designed for storm damage leads"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Real-Time Lead Routing",
          "description": "Instant lead notification and routing systems for emergency response"
        }
      }
    ]
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.9",
    "reviewCount": "78",
    "bestRating": "5"
  },
  "availableChannel": {
    "@type": "ServiceChannel",
    "availableLanguage": "English",
    "serviceType": "Emergency Response",
    "serviceLocation": {
      "@type": "Country",
      "name": "United States"
    }
  }
};

export default function StormResponseMarketingPage() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(stormResponseStructuredData) }}
      />
      <StormResponseMarketingContent />
    </>
  );
}
