import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'PPC Advertising for Local Service Businesses | GroundUPDigital',
  description: 'Drive immediate leads with targeted PPC campaigns for landscapers, roofers, and pest control companies. Get more customers with our proven Google Ads strategies.',
  keywords: 'PPC advertising, Google Ads, local service ads, landscaping PPC, roofing ads, pest control advertising',
  openGraph: {
    title: 'PPC Advertising for Local Service Businesses | GroundUPDigital',
    description: 'Drive immediate leads with targeted PPC campaigns for landscapers, roofers, and pest control companies. Get more customers with our proven Google Ads strategies.',
    type: 'website',
  },
};

export default function PPCAdvertisingPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-green-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              PPC Advertising That <span className="text-blue-600">Generates Leads</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Get immediate visibility and qualified leads with our targeted PPC campaigns designed specifically 
              for landscapers, roofers, and pest control businesses.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Free PPC Audit
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                View Campaign Results
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Proven PPC Results for Local Service Businesses
            </h2>
            <div className="grid md:grid-cols-3 gap-8 mb-16">
              {[
                { metric: '300%', label: 'Average Lead Increase', icon: '📈' },
                { metric: '$4.50', label: 'Average Cost Per Lead', icon: '💰' },
                { metric: '15%', label: 'Average Conversion Rate', icon: '🎯' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Comprehensive PPC Services
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: 'Google Ads Management',
                  description: 'Expert management of Google Search and Display campaigns with continuous optimization.',
                  features: ['Keyword research', 'Ad copywriting', 'Bid management', 'Performance tracking']
                },
                {
                  title: 'Local Service Ads',
                  description: 'Specialized Google Local Service Ads to appear at the top of local search results.',
                  features: ['Google Guaranteed badge', 'Lead verification', 'Background checks', 'Customer reviews']
                },
                {
                  title: 'Facebook & Instagram Ads',
                  description: 'Social media advertising to reach customers where they spend their time.',
                  features: ['Audience targeting', 'Visual campaigns', 'Lead generation', 'Retargeting']
                },
                {
                  title: 'Landing Page Optimization',
                  description: 'High-converting landing pages designed to turn clicks into customers.',
                  features: ['A/B testing', 'Mobile optimization', 'Fast loading', 'Clear CTAs']
                },
                {
                  title: 'Conversion Tracking',
                  description: 'Detailed tracking and analytics to measure campaign performance and ROI.',
                  features: ['Call tracking', 'Form submissions', 'Revenue attribution', 'Custom reporting']
                },
                {
                  title: 'Competitor Analysis',
                  description: 'Stay ahead of competitors with detailed market analysis and strategy adjustments.',
                  features: ['Keyword gaps', 'Ad copy analysis', 'Bid strategies', 'Market positioning']
                }
              ].map((service, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Industry-Specific Strategies */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Industry-Specific PPC Strategies
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  industry: 'Landscaping',
                  strategies: ['Seasonal campaign optimization', 'Service-specific ad groups', 'Geographic targeting', 'Weather-based bidding'],
                  color: 'green'
                },
                {
                  industry: 'Roofing',
                  strategies: ['Emergency service ads', 'Insurance claim targeting', 'Storm response campaigns', 'Material-specific keywords'],
                  color: 'blue'
                },
                {
                  industry: 'Pest Control',
                  strategies: ['Pest-specific campaigns', 'Seasonal pest targeting', 'Emergency service ads', 'Recurring service promotion'],
                  color: 'red'
                }
              ].map((strategy, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg border-l-4 border-blue-600">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">{strategy.industry} PPC</h3>
                  <ul className="space-y-3">
                    {strategy.strategies.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-center text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Start Generating Leads Today
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Get a free PPC audit and discover how we can drive more qualified leads to your business.
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Get Your Free PPC Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
