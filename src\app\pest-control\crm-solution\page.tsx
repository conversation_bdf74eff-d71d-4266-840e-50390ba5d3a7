import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'CRM Solutions for Pest Control Companies | Streamline Operations | GroundUPDigital',
  description: 'Streamline your pest control business with custom CRM solutions. Manage customers, schedule treatments, track pest activity, and automate operations.',
  keywords: 'pest control CRM, exterminator software, pest management CRM, pest control customer management',
  openGraph: {
    title: 'CRM Solutions for Pest Control Companies | Streamline Operations | GroundUPDigital',
    description: 'Streamline your pest control business with custom CRM solutions. Manage customers, schedule treatments, track pest activity, and automate operations.',
    type: 'website',
  },
};

export default function PestControlCRMPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-yellow-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              CRM Solutions for <span className="text-green-600">Pest Control Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Streamline your pest control business operations with custom CRM solutions designed for pest management, 
              customer tracking, treatment scheduling, and automated workflows that grow your business efficiently.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Get Free CRM Demo
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View CRM Features
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* CRM Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Transform Your Pest Control Business Operations
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '55%', label: 'Faster Service Documentation', icon: '📝' },
                { metric: '80%', label: 'Improvement in Customer Retention', icon: '🤝' },
                { metric: '90%', label: 'Better Treatment Tracking', icon: '📊' },
                { metric: '450%', label: 'ROI Within First Year', icon: '💰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-green-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Core CRM Features */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Comprehensive CRM Features for Pest Control
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Customer Management',
                  description: 'Complete customer profiles with pest history, treatment records, and preferences.',
                  capabilities: ['Customer profiles', 'Pest history tracking', 'Treatment records', 'Property details', 'Communication logs'],
                  icon: '👥'
                },
                {
                  feature: 'Treatment Scheduling',
                  description: 'Intelligent scheduling system with recurring treatments and route optimization.',
                  capabilities: ['Recurring schedules', 'Route optimization', 'Technician assignments', 'Customer notifications', 'Weather integration'],
                  icon: '📅'
                },
                {
                  feature: 'Pest Activity Tracking',
                  description: 'Track pest activity, treatment effectiveness, and infestation patterns.',
                  capabilities: ['Pest identification', 'Activity monitoring', 'Treatment effectiveness', 'Infestation mapping', 'Trend analysis'],
                  icon: '🐛'
                },
                {
                  feature: 'Service Documentation',
                  description: 'Comprehensive service documentation with photos, notes, and chemical usage.',
                  capabilities: ['Digital service reports', 'Photo documentation', 'Chemical tracking', 'Treatment notes', 'Customer signatures'],
                  icon: '📋'
                },
                {
                  feature: 'Billing & Invoicing',
                  description: 'Automated billing for recurring services with payment processing.',
                  capabilities: ['Recurring billing', 'Payment processing', 'Invoice automation', 'Payment tracking', 'Financial reporting'],
                  icon: '💳'
                },
                {
                  feature: 'Compliance Management',
                  description: 'Ensure regulatory compliance with automated documentation and reporting.',
                  capabilities: ['EPA compliance', 'Chemical documentation', 'Safety protocols', 'Regulatory reporting', 'Audit trails'],
                  icon: '📜'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.feature}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.capabilities.map((capability, capIndex) => (
                      <li key={capIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {capability}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Seasonal Pest Management */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Seasonal Pest Management Workflows
              </h2>
              <p className="text-xl text-green-100">
                Automated workflows that adapt to seasonal pest activity and treatment schedules.
              </p>
            </div>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  season: 'Spring',
                  pests: ['Ants', 'Termites', 'Wasps', 'Spiders'],
                  workflows: ['Spring activation campaigns', 'Termite inspection scheduling', 'Preventive treatment reminders', 'Outdoor pest preparation'],
                  automation: 'Weather-triggered scheduling and prevention campaigns',
                  icon: '🌸'
                },
                {
                  season: 'Summer',
                  pests: ['Mosquitoes', 'Flies', 'Wasps', 'Ticks'],
                  workflows: ['Peak activity monitoring', 'Emergency response protocols', 'Outdoor treatment scheduling', 'Customer education campaigns'],
                  automation: 'Temperature-based treatment adjustments and emergency alerts',
                  icon: '☀️'
                },
                {
                  season: 'Fall',
                  pests: ['Rodents', 'Stink Bugs', 'Spiders', 'Cluster Flies'],
                  workflows: ['Winter preparation treatments', 'Exclusion service scheduling', 'Indoor pest prevention', 'Rodent control campaigns'],
                  automation: 'Cooling weather triggers and indoor pest alerts',
                  icon: '🍂'
                },
                {
                  season: 'Winter',
                  pests: ['Mice', 'Rats', 'Cockroaches', 'Silverfish'],
                  workflows: ['Indoor pest monitoring', 'Heating system pest checks', 'Rodent control intensification', 'Planning next season'],
                  automation: 'Indoor activity monitoring and treatment scheduling',
                  icon: '❄️'
                }
              ].map((season, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{season.icon}</div>
                  <h3 className="text-xl font-semibold mb-2">{season.season}</h3>
                  <div className="mb-4">
                    <h4 className="font-semibold text-green-100 mb-2">Common Pests:</h4>
                    <ul className="text-sm text-green-100">
                      {season.pests.map((pest, pestIndex) => (
                        <li key={pestIndex}>{pest}</li>
                      ))}
                    </ul>
                  </div>
                  <div className="mb-4">
                    <h4 className="font-semibold text-green-100 mb-2">Workflows:</h4>
                    <ul className="text-xs text-green-100 space-y-1">
                      {season.workflows.map((workflow, workflowIndex) => (
                        <li key={workflowIndex}>• {workflow}</li>
                      ))}
                    </ul>
                  </div>
                  <div className="bg-green-700 rounded-lg p-3">
                    <h5 className="font-semibold text-green-100 text-sm mb-1">Automation:</h5>
                    <p className="text-green-200 text-xs">{season.automation}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Treatment Tracking System */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Advanced Treatment Tracking System
            </h2>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  component: 'Pest Identification',
                  description: 'AI-powered pest identification with treatment recommendations.',
                  features: ['Photo recognition', 'Pest database', 'Treatment protocols', 'Severity assessment', 'Prevention strategies'],
                  icon: '🔍'
                },
                {
                  component: 'Treatment Monitoring',
                  description: 'Track treatment effectiveness and pest activity over time.',
                  features: ['Effectiveness tracking', 'Activity monitoring', 'Follow-up scheduling', 'Success metrics', 'Trend analysis'],
                  icon: '📊'
                },
                {
                  component: 'Chemical Management',
                  description: 'Comprehensive chemical usage tracking and compliance management.',
                  features: ['Chemical inventory', 'Usage tracking', 'EPA compliance', 'Safety protocols', 'Application records'],
                  icon: '🧪'
                }
              ].map((component, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4 text-center">{component.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">{component.component}</h3>
                  <p className="text-gray-600 mb-6">{component.description}</p>
                  <ul className="space-y-3">
                    {component.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Mobile CRM Features */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Mobile CRM for Pest Control Technicians
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Field Technician Capabilities:</h3>
                <ul className="space-y-4">
                  {[
                    'Real-time service documentation with photos',
                    'Pest identification tools and treatment guides',
                    'Chemical usage tracking and safety protocols',
                    'Customer communication and service confirmations',
                    'GPS tracking and route optimization',
                    'Offline access for areas with poor connectivity',
                    'Digital signatures and service completion',
                    'Emergency protocol access and escalation'
                  ].map((capability, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{capability}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Mobile App Benefits:</h3>
                <div className="space-y-6">
                  {[
                    { metric: '65%', description: 'Faster service documentation', icon: '📱' },
                    { metric: '90%', description: 'Improvement in data accuracy', icon: '✅' },
                    { metric: '50%', description: 'Reduction in paperwork', icon: '📄' },
                    { metric: '45%', description: 'Better customer communication', icon: '💬' }
                  ].map((benefit, index) => (
                    <div key={index} className="flex items-center bg-white p-4 rounded-lg shadow-lg">
                      <div className="text-3xl mr-4">{benefit.icon}</div>
                      <div>
                        <div className="text-2xl font-bold text-green-600">{benefit.metric}</div>
                        <div className="text-gray-600">{benefit.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Integration Capabilities */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seamless Integrations for Pest Control Businesses
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Essential Integrations:</h3>
                <div className="grid grid-cols-2 gap-4">
                  {[
                    { name: 'QuickBooks', category: 'Accounting', icon: '💼' },
                    { name: 'Weather APIs', category: 'Weather Data', icon: '🌤️' },
                    { name: 'Google Calendar', category: 'Scheduling', icon: '📅' },
                    { name: 'Stripe/PayPal', category: 'Payments', icon: '💳' },
                    { name: 'EPA Databases', category: 'Compliance', icon: '📜' },
                    { name: 'Chemical Suppliers', category: 'Inventory', icon: '🧪' }
                  ].map((integration, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg shadow-lg text-center">
                      <div className="text-2xl mb-2">{integration.icon}</div>
                      <h4 className="font-semibold text-gray-900">{integration.name}</h4>
                      <p className="text-sm text-gray-600">{integration.category}</p>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Integration Benefits:</h3>
                <ul className="space-y-4">
                  {[
                    'Eliminate double data entry across systems',
                    'Sync financial data with accounting software',
                    'Automate weather-based treatment scheduling',
                    'Process payments directly within CRM',
                    'Maintain EPA compliance automatically',
                    'Real-time chemical inventory management'
                  ].map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{benefit}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-8 bg-green-50 rounded-lg p-6">
                  <h4 className="font-semibold text-green-800 mb-2">Custom Integrations Available</h4>
                  <p className="text-green-700 text-sm">
                    Need integration with specific pest control software or compliance systems? 
                    We can build custom integrations to connect your CRM with any business system.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Pest Control CRM Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    BugShield Pro: 500% Growth with Custom CRM
                  </h3>
                  <p className="text-gray-600 mb-6">
                    BugShield Pro was struggling with manual processes, missed treatments, and poor customer communication. 
                    Our custom CRM solution streamlined their operations and accelerated business growth.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">500%</div>
                      <div className="text-sm text-gray-600">Business Growth</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">98%</div>
                      <div className="text-sm text-gray-600">Customer Retention</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">CRM Implementation Results:</h4>
                  <ul className="space-y-3">
                    {[
                      '55% faster service documentation',
                      '90% improvement in treatment tracking',
                      '80% increase in customer retention',
                      '500% growth in annual revenue',
                      '98% customer satisfaction rating',
                      '70% improvement in technician productivity'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Implementation Timeline */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              CRM Implementation Timeline
            </h2>
            <div className="grid md:grid-cols-5 gap-8">
              {[
                { step: '1', title: 'Discovery', description: 'Analyze current processes and pest control needs', duration: '1-2 weeks' },
                { step: '2', title: 'Design', description: 'Create custom CRM solution for pest control', duration: '2 weeks' },
                { step: '3', title: 'Development', description: 'Build and configure pest control CRM', duration: '4-6 weeks' },
                { step: '4', title: 'Training', description: 'Comprehensive team training and onboarding', duration: '1 week' },
                { step: '5', title: 'Launch', description: 'Go live with ongoing support', duration: 'Ongoing' }
              ].map((phase, index) => (
                <div key={index} className="text-center">
                  <div className="bg-green-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                    {phase.step}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{phase.title}</h3>
                  <p className="text-gray-600 mb-2">{phase.description}</p>
                  <span className="text-sm text-green-600 font-semibold">{phase.duration}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Streamline Your Pest Control Operations?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Get a free CRM consultation and discover how we can help you manage customers, 
              track treatments, and grow your pest control business efficiently.
            </p>
            <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
              Get Your Free Pest Control CRM Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
