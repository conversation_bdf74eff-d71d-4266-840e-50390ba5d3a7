import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, ShoppingCart, Package, Calendar, DollarSign, Star, CheckCircle, Phone, Truck } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'E-commerce Integration for Landscaping Businesses | GroundUP Digital',
  description: 'Online sales platforms for landscaping products and services. Plant sales, hardscape materials, maintenance packages, and online booking systems.',
  keywords: 'landscaping ecommerce, online plant sales, landscaping products online, maintenance packages online, landscaping business ecommerce',
};

const ecommerceFeatures = [
  {
    icon: Package,
    title: 'Product Catalog Management',
    description: 'Comprehensive online catalog for landscaping products, plants, and materials.',
    benefits: ['Plant & material catalogs', 'Inventory management', 'Seasonal availability', 'Bulk pricing options']
  },
  {
    icon: Calendar,
    title: 'Service Booking System',
    description: 'Online booking and scheduling for landscaping services and maintenance packages.',
    benefits: ['Service package sales', 'Appointment scheduling', 'Recurring services', 'Seasonal programs']
  },
  {
    icon: Truck,
    title: 'Delivery & Fulfillment',
    description: 'Complete order fulfillment system with delivery scheduling and tracking.',
    benefits: ['Delivery zone management', 'Installation scheduling', 'Order tracking', 'Customer notifications']
  }
];

const ecommerceTypes = [
  {
    name: 'Plant & Nursery Store',
    description: 'Online plant sales with care guides and planting services',
    features: ['Plant catalog with photos', 'Care instructions', 'Planting service add-ons', 'Seasonal collections', 'Bulk order discounts', 'Delivery scheduling']
  },
  {
    name: 'Hardscape Materials Shop',
    description: 'Online sales of pavers, stones, mulch, and landscaping materials',
    features: ['Material calculators', 'Project estimators', 'Bulk pricing tiers', 'Delivery options', 'Installation services', 'Sample ordering']
  },
  {
    name: 'Maintenance Package Store',
    description: 'Subscription-based maintenance and lawn care service packages',
    features: ['Service package builder', 'Recurring billing', 'Seasonal adjustments', 'Add-on services', 'Customer portal access', 'Auto-renewal options']
  },
  {
    name: 'Design Consultation Booking',
    description: 'Online booking system for design consultations and project estimates',
    features: ['Consultation scheduling', 'Project questionnaires', 'Photo uploads', 'Estimate requests', 'Design package sales', 'Follow-up automation']
  }
];

const packages = [
  {
    name: "E-commerce Starter",
    price: "$1,497",
    period: "/month",
    description: "Basic online store functionality for landscaping businesses starting with e-commerce",
    features: [
      "Product catalog (up to 100 items)",
      "Basic shopping cart",
      "Payment processing",
      "Order management",
      "Customer accounts",
      "Mobile-responsive design"
    ],
    popular: false
  },
  {
    name: "Commerce Professional",
    price: "$2,997",
    period: "/month", 
    description: "Advanced e-commerce platform for growing landscaping companies",
    features: [
      "Unlimited product catalog",
      "Advanced inventory management",
      "Service booking system",
      "Subscription management",
      "Delivery scheduling",
      "Analytics & reporting"
    ],
    popular: true
  },
  {
    name: "Enterprise Commerce Hub",
    price: "$4,997",
    period: "/month",
    description: "Complete e-commerce ecosystem for large landscaping operations",
    features: [
      "Multi-location management",
      "B2B wholesale portal",
      "Advanced integrations",
      "Custom development",
      "White-label solutions",
      "Dedicated commerce manager"
    ],
    popular: false
  }
];

const caseStudy = {
  company: "Garden Center Plus",
  location: "Nashville, TN",
  results: [
    { metric: "Online Revenue", improvement: "+320%" },
    { metric: "Order Volume", improvement: "+280%" },
    { metric: "Customer Acquisition", improvement: "+190%" },
    { metric: "Average Order Value", improvement: "+85%" }
  ]
};

const revenueStreams = [
  { stream: "Plant & Tree Sales", description: "Direct-to-consumer plant sales with delivery and planting services" },
  { stream: "Hardscape Materials", description: "Online sales of pavers, stones, mulch, and landscaping supplies" },
  { stream: "Maintenance Packages", description: "Recurring service subscriptions for lawn care and maintenance" },
  { stream: "Design Consultations", description: "Online booking for design services and project consultations" },
  { stream: "Seasonal Programs", description: "Holiday decorating, spring cleanup, and seasonal service packages" },
  { stream: "Educational Content", description: "Premium guides, courses, and landscaping education materials" }
];

export default function EcommerceIntegrationPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <ShoppingCart className="w-4 h-4" />
              <span className="text-sm font-semibold">E-commerce for Landscaping Businesses</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Online Sales Platforms That Create{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                New Revenue Streams
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Comprehensive e-commerce solutions that help landscaping business owners sell products and services online, 
              creating additional revenue streams and reaching more customers.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <ShoppingCart className="w-5 h-5 mr-2" />
                See Store Demo
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                View Store Examples
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '320%', label: 'Revenue Increase' },
                { number: '280%', label: 'Order Growth' },
                { number: '85%', label: 'Higher Order Value' },
                { number: '50+', label: 'Stores Built' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              E-commerce Solutions Built for Landscaping Business Success
            </h2>
            <p className="text-lg text-gray-600">
              Our e-commerce platforms are designed specifically for landscaping business owners who want to expand their 
              revenue streams and reach customers through online sales.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {ecommerceFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Revenue Streams */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Multiple Revenue Streams for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Diversify your landscaping business income with multiple online revenue opportunities.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-6">
            {revenueStreams.map((item, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="flex items-center mb-3">
                    <DollarSign className="w-5 h-5 text-green-600 mr-2" />
                    <h3 className="text-lg font-bold text-gray-900">{item.stream}</h3>
                  </div>
                  <p className="text-gray-600 text-sm">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* E-commerce Types Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Specialized E-commerce Solutions for Every Landscaping Need
            </h2>
            <p className="text-lg text-gray-600">
              Custom online stores designed for different types of landscaping products and services.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {ecommerceTypes.map((store, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{store.name}</h3>
                  <p className="text-gray-600 mb-4">{store.description}</p>
                  <ul className="space-y-2">
                    {store.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                E-commerce Success Story: Revenue Transformation
              </h2>
              <p className="text-lg text-gray-600">
                See how {caseStudy.company} used our e-commerce platform to create new revenue streams and grow their business
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A traditional landscaping business in {caseStudy.location} wanted to expand beyond service work. 
                      Our e-commerce platform helped them create multiple online revenue streams and reach new customers.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Read Full Case Study
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              E-commerce Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Professional e-commerce solutions designed specifically for landscaping business owners. 
              Choose the package that creates new revenue opportunities for your business.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* FAQ Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Frequently Asked Questions About E-commerce for Landscaping Businesses
              </h2>
              <p className="text-lg text-gray-600">
                Common questions landscaping business owners have about online sales and e-commerce integration.
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "What can landscaping businesses sell online?",
                  answer: "Plants, garden supplies, maintenance packages, seasonal services, design consultations, soil and mulch, irrigation supplies, outdoor lighting, and gift certificates. Many landscaping businesses generate 20-30% additional revenue through e-commerce."
                },
                {
                  question: "How do you handle delivery for landscaping e-commerce?",
                  answer: "We integrate delivery zones, calculate shipping costs based on weight/distance, offer local delivery options, coordinate with existing service routes, and provide pickup options at your location for larger items like plants and materials."
                },
                {
                  question: "Can customers book landscaping services through e-commerce?",
                  answer: "Yes! We create service booking systems with package selection, scheduling integration, deposit collection, and automated confirmation. Customers can book consultations, maintenance packages, and seasonal services 24/7."
                },
                {
                  question: "How does e-commerce integrate with my existing landscaping business?",
                  answer: "E-commerce platforms integrate with your CRM, inventory management, scheduling software, and accounting systems. Orders automatically flow into your existing workflows, eliminating double data entry and streamlining operations."
                },
                {
                  question: "Is e-commerce profitable for small landscaping businesses?",
                  answer: "Absolutely! Even small landscaping businesses see 15-25% revenue increases through e-commerce. Low-overhead digital products like consultations and maintenance packages have excellent profit margins and help fill scheduling gaps."
                }
              ].map((faq, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{faq.question}</h3>
                  <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Create New Revenue Streams for Your Landscaping Business?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a free e-commerce consultation and discover how online sales can diversify your revenue 
              and grow your landscaping business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <ShoppingCart className="w-5 h-5 mr-2" />
                Get E-commerce Consultation
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule Demo
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
