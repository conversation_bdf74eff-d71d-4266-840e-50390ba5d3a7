import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Lead Generation for Local Service Businesses | GroundUPDigital',
  description: 'Generate high-quality leads for your landscaping, roofing, or pest control business. Our proven lead generation strategies deliver qualified customers ready to buy.',
  keywords: 'lead generation, local service leads, landscaping leads, roofing leads, pest control leads, digital marketing',
  openGraph: {
    title: 'Lead Generation for Local Service Businesses | GroundUPDigital',
    description: 'Generate high-quality leads for your landscaping, roofing, or pest control business. Our proven lead generation strategies deliver qualified customers ready to buy.',
    type: 'website',
  },
};

export default function LeadGenerationPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-green-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Lead Generation That <span className="text-blue-600">Fills Your Pipeline</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Stop chasing leads and start attracting them. Our proven lead generation strategies deliver 
              qualified customers ready to hire your landscaping, roofing, or pest control services.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Lead Generation Audit
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                See Lead Results
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Proven Lead Generation Results
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '300%', label: 'More Qualified Leads', icon: '📈' },
                { metric: '$25', label: 'Average Cost Per Lead', icon: '💰' },
                { metric: '24/7', label: 'Lead Generation', icon: '🕐' },
                { metric: '85%', label: 'Lead Quality Score', icon: '⭐' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Lead Generation Methods */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Multi-Channel Lead Generation
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  method: 'Search Engine Optimization',
                  description: 'Rank higher in Google for local service searches and attract organic leads.',
                  features: ['Local SEO optimization', 'Google My Business', 'Review management', 'Content marketing']
                },
                {
                  method: 'Pay-Per-Click Advertising',
                  description: 'Immediate visibility with targeted Google Ads and social media campaigns.',
                  features: ['Google Ads', 'Facebook Ads', 'Local Service Ads', 'Retargeting campaigns']
                },
                {
                  method: 'Landing Page Optimization',
                  description: 'High-converting landing pages designed to capture and convert visitors.',
                  features: ['A/B testing', 'Mobile optimization', 'Fast loading', 'Clear call-to-actions']
                },
                {
                  method: 'Email Marketing',
                  description: 'Nurture leads and stay top-of-mind with targeted email campaigns.',
                  features: ['Lead nurturing', 'Automated sequences', 'Seasonal campaigns', 'Customer retention']
                },
                {
                  method: 'Social Media Marketing',
                  description: 'Build brand awareness and generate leads through social platforms.',
                  features: ['Content creation', 'Community engagement', 'Social advertising', 'Influencer partnerships']
                },
                {
                  method: 'Referral Programs',
                  description: 'Turn satisfied customers into your best lead generation source.',
                  features: ['Referral tracking', 'Incentive programs', 'Customer advocacy', 'Word-of-mouth marketing']
                }
              ].map((method, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{method.method}</h3>
                  <p className="text-gray-600 mb-4">{method.description}</p>
                  <ul className="space-y-2">
                    {method.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Lead Qualification Process */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Our Lead Qualification Process
            </h2>
            <div className="grid lg:grid-cols-4 gap-8">
              {[
                {
                  step: '1',
                  title: 'Lead Capture',
                  description: 'Multiple touchpoints capture leads from various sources and channels.',
                  icon: '🎯'
                },
                {
                  step: '2',
                  title: 'Initial Screening',
                  description: 'Automated screening filters out unqualified leads and spam.',
                  icon: '🔍'
                },
                {
                  step: '3',
                  title: 'Lead Scoring',
                  description: 'Advanced scoring system ranks leads based on conversion probability.',
                  icon: '📊'
                },
                {
                  step: '4',
                  title: 'Delivery & Follow-up',
                  description: 'Qualified leads delivered instantly with automated follow-up sequences.',
                  icon: '🚀'
                }
              ].map((process, index) => (
                <div key={index} className="text-center">
                  <div className="bg-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    {process.step}
                  </div>
                  <div className="text-4xl mb-4">{process.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{process.title}</h3>
                  <p className="text-gray-600">{process.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Industry-Specific Lead Generation */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Industry-Specific Lead Strategies
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  industry: 'Landscaping',
                  strategies: [
                    'Seasonal campaign optimization',
                    'Before/after photo showcases',
                    'Property value enhancement messaging',
                    'Maintenance service upsells'
                  ],
                  leadTypes: ['Lawn care', 'Landscape design', 'Tree services', 'Irrigation']
                },
                {
                  industry: 'Roofing',
                  strategies: [
                    'Storm damage response campaigns',
                    'Insurance claim assistance',
                    'Emergency service promotion',
                    'Preventive maintenance education'
                  ],
                  leadTypes: ['Roof repairs', 'New installations', 'Inspections', 'Emergency services']
                },
                {
                  industry: 'Pest Control',
                  strategies: [
                    'Seasonal pest targeting',
                    'Prevention-focused messaging',
                    'Emergency response campaigns',
                    'Recurring service promotion'
                  ],
                  leadTypes: ['General pest control', 'Termite treatment', 'Wildlife removal', 'Preventive services']
                }
              ].map((industry, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg">
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">{industry.industry} Leads</h3>
                  
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Strategies:</h4>
                    <ul className="space-y-2">
                      {industry.strategies.map((strategy, strategyIndex) => (
                        <li key={strategyIndex} className="flex items-center text-gray-600 text-sm">
                          <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                          {strategy}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Lead Types:</h4>
                    <ul className="space-y-2">
                      {industry.leadTypes.map((type, typeIndex) => (
                        <li key={typeIndex} className="flex items-center text-gray-600 text-sm">
                          <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                          {type}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Fill Your Pipeline with Quality Leads?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Get a free lead generation audit and discover how we can 3x your qualified leads in 90 days.
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Get Your Free Lead Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
