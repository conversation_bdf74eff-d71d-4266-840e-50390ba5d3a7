import React from 'react';

interface SchemaProps {
  data: any;
}

export function Schema({ data }: SchemaProps) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  );
}

// Specific schema components for different page types
export function LocalBusinessSchema({ data }: { data: any }) {
  return <Schema data={data} />;
}

export function ServiceSchema({ data }: { data: any }) {
  return <Schema data={data} />;
}

export function FAQSchema({ data }: { data: any }) {
  return <Schema data={data} />;
}

export function BreadcrumbSchema({ data }: { data: any }) {
  return <Schema data={data} />;
}

export function ReviewSchema({ data }: { data: any }) {
  return <Schema data={data} />;
}

export function ArticleSchema({ data }: { data: any }) {
  return <Schema data={data} />;
}
