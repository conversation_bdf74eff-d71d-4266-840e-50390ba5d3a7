import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowRight, Search, Globe, MousePointer, PenTool, Shield, Brain, Eye, Wifi, Users, BarChart3, Smartphone, Palette, Route, Monitor, ShoppingCart, Code } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';


export const metadata: Metadata = {
  title: 'Digital Solutions for Landscaping Businesses | GroundUP Digital',
  description: 'Comprehensive digital solutions designed exclusively for landscaping business owners. From foundational growth to advanced technology integration.',
  keywords: 'landscaping digital solutions, landscaping business services, landscaping marketing solutions, digital transformation landscaping',
};

const foundationalSolutions = [
  {
    icon: Search,
    title: 'Hyper-Local SEO',
    description: 'Dominate local search results and attract customers in your service area.',
    href: '/solutions/seo',
    features: ['Local search optimization', 'Google Business management', 'Industry keywords', 'Competitor analysis']
  },
  {
    icon: Globe,
    title: 'Business-Focused Websites',
    description: 'Professional websites that showcase your services and convert visitors into customers.',
    href: '/solutions/web-design',
    features: ['Lead generation forms', 'Project galleries', 'Mobile optimization', 'Fast loading speeds']
  },
  {
    icon: MousePointer,
    title: 'Precision Paid Advertising',
    description: 'High-intent PPC campaigns that connect you with customers ready to hire.',
    href: '/solutions/ppc',
    features: ['Google Local Service Ads', 'Targeted campaigns', 'ROI optimization', 'Lead tracking']
  },
  {
    icon: PenTool,
    title: 'Strategic Content Marketing',
    description: 'Authority-building content that educates customers and showcases expertise.',
    href: '/solutions/content-marketing',
    features: ['Educational content', 'Project showcases', 'SEO optimization', 'Social amplification']
  },
  {
    icon: Shield,
    title: 'Reputation Management',
    description: 'Protect and enhance your online reputation across all platforms.',
    href: '/solutions/reputation-management',
    features: ['Review generation', 'Response management', 'Crisis protection', 'Trust building']
  }
];

const transformationSolutions = [
  {
    icon: Brain,
    title: 'AI-Powered Lead Generation',
    description: 'Intelligent systems that identify, qualify, and nurture high-value prospects.',
    href: '/solutions/ai-lead-generation',
    features: ['Predictive analytics', 'Lead scoring', 'Automated nurturing', 'Smart targeting']
  },
  {
    icon: Eye,
    title: 'AR/VR Design Visualization',
    description: 'Immersive presentations that help clients visualize their future landscape.',
    href: '/solutions/ar-vr-visualization',
    features: ['3D visualizations', 'Virtual walkthroughs', 'Interactive presentations', 'Client engagement']
  },
  {
    icon: Wifi,
    title: 'Smart Landscaping & IoT',
    description: 'Connected systems that create recurring revenue and ongoing client relationships.',
    href: '/solutions/iot-integration',
    features: ['Smart irrigation', 'Environmental monitoring', 'Client dashboards', 'Predictive maintenance']
  },
  {
    icon: Users,
    title: 'CRM & Sales Enablement',
    description: 'Complete business management platform designed for landscaping operations.',
    href: '/solutions/crm-sales',
    features: ['Customer management', 'Sales pipeline', 'Project tracking', 'Team coordination']
  },
  {
    icon: BarChart3,
    title: 'Market Intelligence',
    description: 'Advanced analytics and competitive intelligence for strategic decision making.',
    href: '/solutions/market-intelligence',
    features: ['Market analysis', 'Competitor tracking', 'Customer insights', 'Growth opportunities']
  }
];

const operationalSolutions = [
  {
    icon: Smartphone,
    title: 'Mobile App Development',
    description: 'Custom mobile apps that streamline operations and enhance client experiences.',
    href: '/solutions/mobile-app-development',
    features: ['Crew management', 'Project tracking', 'Client communication', 'Mobile invoicing']
  },
  {
    icon: Palette,
    title: 'Design Software & Studios',
    description: 'Professional design tools and virtual studios for landscape professionals.',
    href: '/solutions/design-software-studio',
    features: ['3D design tools', 'Virtual reality', 'Client presentations', 'Collaboration platforms']
  },
  {
    icon: Route,
    title: 'Field Service Management',
    description: 'Comprehensive field operations management for landscaping crews.',
    href: '/solutions/field-service-management',
    features: ['GPS tracking', 'Route optimization', 'Equipment management', 'Work order systems']
  },
  {
    icon: Monitor,
    title: 'Customer Portal Development',
    description: 'Branded client portals that enhance customer relationships.',
    href: '/solutions/customer-portals',
    features: ['Project updates', 'Service scheduling', 'Payment processing', 'Communication tools']
  },
  {
    icon: ShoppingCart,
    title: 'E-commerce Integration',
    description: 'Online sales platforms for landscaping products and services.',
    href: '/solutions/ecommerce-integration',
    features: ['Product catalogs', 'Service booking', 'Subscription management', 'Delivery systems']
  },
  {
    icon: Code,
    title: 'Custom Software Development',
    description: 'Tailored software solutions designed specifically for your business needs.',
    href: '/solutions/custom-software',
    features: ['Custom applications', 'Business automation', 'System integrations', 'Scalable architecture']
  }
];

export default function SolutionsPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Complete Digital Solutions for{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscaping Businesses
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              From foundational growth strategies to cutting-edge technology integration, 
              we provide everything landscaping business owners need to dominate their markets.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Link href="/partnership" className="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-white text-blue-600 hover:bg-gray-50 shadow-lg hover:shadow-xl px-6 xs:px-8 py-3 xs:py-4 text-base xs:text-lg touch-target-lg">
                Schedule Consultation
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
              <Link href="/why-groundup" className="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 border-2 border-white/30 text-white hover:bg-white/10 px-6 xs:px-8 py-3 xs:py-4 text-base xs:text-lg touch-target-lg">
                Why Choose GroundUP
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '16', label: 'Solution Categories' },
                { number: '200+', label: 'Businesses Served' },
                { number: '247%', label: 'Avg Lead Increase' },
                { number: '100%', label: 'Landscaping Focus' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Foundational Solutions */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-green-100 rounded-full px-4 py-2 mb-6">
              <span className="text-2xl">🌱</span>
              <span className="text-sm font-semibold text-green-700">Foundational Growth Pillars</span>
            </div>
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Essential Solutions Every Landscaping Business Needs
            </h2>
            <p className="text-lg text-gray-600">
              Build a strong foundation for your landscaping business with these proven digital marketing solutions.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {foundationalSolutions.map((solution, index) => (
              <Card key={index} className="h-full hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <solution.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{solution.title}</h3>
                  <p className="text-gray-600 mb-4">{solution.description}</p>
                  <ul className="space-y-2 mb-6">
                    {solution.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2 flex-shrink-0"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Link href={solution.href} className="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 w-full bg-gray-100 text-gray-900 hover:bg-gray-200 px-4 xs:px-6 py-2 xs:py-3 text-sm xs:text-base touch-target">
                    Learn More
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Digital Transformation Solutions */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-blue-100 rounded-full px-4 py-2 mb-6">
              <span className="text-2xl">🚀</span>
              <span className="text-sm font-semibold text-blue-700">Digital Transformation Solutions</span>
            </div>
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Advanced Technology for Market Leadership
            </h2>
            <p className="text-lg text-gray-600">
              Cutting-edge solutions that position your landscaping business as a technology leader and create competitive advantages.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {transformationSolutions.map((solution, index) => (
              <Card key={index} className="h-full hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <solution.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{solution.title}</h3>
                  <p className="text-gray-600 mb-4">{solution.description}</p>
                  <ul className="space-y-2 mb-6">
                    {solution.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2 flex-shrink-0"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Link href={solution.href} className="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 w-full bg-blue-600 text-white hover:bg-blue-700 px-4 xs:px-6 py-2 xs:py-3 text-sm xs:text-base touch-target">
                    Explore Solution
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Operational Excellence Solutions */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <div className="inline-flex items-center gap-2 bg-purple-100 rounded-full px-4 py-2 mb-6">
              <span className="text-2xl">⚡</span>
              <span className="text-sm font-semibold text-purple-700">Operational Excellence Solutions</span>
            </div>
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Streamline Operations & Enhance Customer Experience
            </h2>
            <p className="text-lg text-gray-600">
              Specialized tools and platforms that optimize your landscaping business operations and create exceptional customer experiences.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {operationalSolutions.map((solution, index) => (
              <Card key={index} className="h-full hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                    <solution.icon className="w-6 h-6 text-purple-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{solution.title}</h3>
                  <p className="text-gray-600 mb-4">{solution.description}</p>
                  <ul className="space-y-2 mb-6">
                    {solution.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-2 flex-shrink-0"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Link href={solution.href} className="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 w-full bg-purple-600 text-white hover:bg-purple-700 px-4 xs:px-6 py-2 xs:py-3 text-sm xs:text-base touch-target">
                    Explore Solution
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Transform Your Landscaping Business?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Let's discuss which solutions are right for your landscaping business. 
              Our experts will create a custom growth strategy tailored to your goals.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/partnership" className="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 bg-white text-blue-600 hover:bg-gray-50 shadow-lg hover:shadow-xl px-6 xs:px-8 py-3 xs:py-4 text-base xs:text-lg touch-target-lg">
                Schedule Strategy Session
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
              <Link href="/why-groundup" className="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 border-2 border-white/30 text-white hover:bg-white/10 px-6 xs:px-8 py-3 xs:py-4 text-base xs:text-lg touch-target-lg">
                Why Choose GroundUP
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
