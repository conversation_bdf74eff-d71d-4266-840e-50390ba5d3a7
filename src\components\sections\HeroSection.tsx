'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowRight, Play, CheckCircle, Star, TrendingUp, Shield, Clock } from 'lucide-react';

// Typed animation hook
const useTypedAnimation = (strings: string[], typeSpeed = 100, backSpeed = 50, backDelay = 2000) => {
  const [currentStringIndex, setCurrentStringIndex] = useState(0);
  const [currentText, setCurrentText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => {
      const currentString = strings[currentStringIndex];

      if (!isDeleting) {
        // Typing
        if (currentText.length < currentString.length) {
          setCurrentText(currentString.substring(0, currentText.length + 1));
        } else {
          // Start deleting after delay
          setTimeout(() => setIsDeleting(true), backDelay);
        }
      } else {
        // Deleting
        if (currentText.length > 0) {
          setCurrentText(currentString.substring(0, currentText.length - 1));
        } else {
          setIsDeleting(false);
          setCurrentStringIndex((prev) => (prev + 1) % strings.length);
        }
      }
    }, isDeleting ? backSpeed : typeSpeed);

    return () => clearTimeout(timeout);
  }, [currentText, isDeleting, currentStringIndex, strings, typeSpeed, backSpeed, backDelay]);

  return currentText;
};

export const HeroSection: React.FC = () => {
  const typedText = useTypedAnimation([
    'Landscaping Business Owners',
    'Landscape Contractors',
    'Lawn Care Companies',
    'Hardscaping Businesses',
    'Commercial Landscapers',
    'Tree Service Companies'
  ], 80, 40, 1500);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-50 via-white to-green-50 safe-top">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-transparent to-green-600/5"></div>

      {/* Animated Background Shapes - Responsive */}
      <motion.div
        className="absolute top-10 xs:top-20 left-5 xs:left-10 w-48 h-48 xs:w-72 xs:h-72 bg-blue-400/10 rounded-full blur-3xl"
        animate={{
          x: [0, 50, 0],
          y: [0, -25, 0],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <motion.div
        className="absolute bottom-10 xs:bottom-20 right-5 xs:right-10 w-64 h-64 xs:w-96 xs:h-96 bg-green-400/10 rounded-full blur-3xl"
        animate={{
          x: [0, -40, 0],
          y: [0, 30, 0],
        }}
        transition={{
          duration: 25,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <div className="container relative z-10">
        <div className="max-w-6xl mx-auto">
          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-6 xs:mb-8"
          >
            <div className="inline-flex items-center gap-1 xs:gap-2 bg-white/80 backdrop-blur-sm rounded-full px-3 xs:px-4 sm:px-6 py-2 xs:py-3 shadow-lg border border-white/20 max-w-full">
              <Star className="w-4 h-4 xs:w-5 xs:h-5 text-yellow-500 fill-current flex-shrink-0" />
              <span className="text-xs xs:text-sm font-semibold text-gray-700 truncate">
                <span className="hidden sm:inline">Premium Landscaping Partner • 200+ Clients • $45M+ Revenue Generated</span>
                <span className="sm:hidden">Premium Partner • 200+ Clients</span>
              </span>
              <div className="flex gap-0.5 xs:gap-1 flex-shrink-0">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-3 h-3 xs:w-4 xs:h-4 text-yellow-500 fill-current" />
                ))}
              </div>
            </div>
          </motion.div>

          {/* Main Hero Content */}
          <div className="text-center mb-8 xs:mb-10 sm:mb-12">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-2xl xs:text-3xl sm:text-4xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 mb-4 xs:mb-6 leading-tight px-2"
            >
              <span className="block xs:inline">Premium Digital Ecosystem for</span>{' '}
              <span className="relative block xs:inline mt-2 xs:mt-0">
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600">
                  {typedText}
                  <span className="animate-pulse">|</span>
                </span>
              </span>
              <span className="block mt-2 xs:mt-0">Businesses</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-sm xs:text-base sm:text-lg md:text-lg lg:text-xl text-gray-600 mb-6 xs:mb-8 max-w-4xl mx-auto leading-relaxed px-4"
            >
              We don't just market landscaping businesses—we architect comprehensive digital ecosystems that help landscaping business owners dominate their markets and scale their operations.
              <span className="font-semibold text-gray-800 block xs:inline">
                <span className="xs:hidden"><br /></span>
                Helping landscaping companies generate more leads, close more deals, and grow their revenue.
              </span>
              <span className="hidden sm:inline"> 200+ landscaping business owners served, $45M+ in client revenue generated.</span>
              <span className="sm:hidden block mt-2">200+ business owners, $45M+ generated.</span>
            </motion.p>

            {/* Key Benefits */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col xs:flex-row flex-wrap justify-center gap-3 xs:gap-4 sm:gap-6 mb-8 xs:mb-10 px-4"
            >
              {[
                { icon: TrendingUp, text: 'Landscaping Business Experts', shortText: 'B2B Experts' },
                { icon: Clock, text: 'Proven Business Growth', shortText: 'Proven Growth' },
                { icon: Shield, text: '200+ Business Owners Served', shortText: '200+ Owners' }
              ].map((benefit, index) => (
                <div key={index} className="flex items-center gap-2 bg-white/60 backdrop-blur-sm rounded-full px-3 xs:px-4 py-2 shadow-md min-w-0 flex-shrink-0">
                  <benefit.icon className="w-4 h-4 xs:w-5 xs:h-5 text-green-600 flex-shrink-0" />
                  <span className="font-semibold text-gray-700 text-xs xs:text-sm sm:text-base">
                    <span className="hidden sm:inline">{benefit.text}</span>
                    <span className="sm:hidden">{benefit.shortText}</span>
                  </span>
                </div>
              ))}
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="flex flex-col sm:flex-row gap-3 xs:gap-4 justify-center items-center mb-8 xs:mb-10 sm:mb-12 px-4"
            >
              <Link href="/contact" className="w-full sm:w-auto">
                <motion.button
                  whileHover={{ scale: 1.05, boxShadow: "0 20px 40px rgba(59, 130, 246, 0.3)" }}
                  whileTap={{ scale: 0.95 }}
                  className="group bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 xs:px-8 py-3 xs:py-4 rounded-xl font-bold text-sm xs:text-base sm:text-lg shadow-xl hover:shadow-2xl transition-all duration-300 flex items-center justify-center gap-2 w-full sm:w-auto touch-target-lg"
                >
                  <span className="hidden xs:inline">Get Free Business Growth Strategy</span>
                  <span className="xs:hidden">Free Business Strategy</span>
                  <ArrowRight className="w-4 h-4 xs:w-5 xs:h-5 group-hover:translate-x-1 transition-transform flex-shrink-0" />
                </motion.button>
              </Link>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="group bg-white/80 backdrop-blur-sm text-gray-700 px-6 xs:px-8 py-3 xs:py-4 rounded-xl font-bold text-sm xs:text-base sm:text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2 border border-white/20 w-full sm:w-auto touch-target-lg"
              >
                <Play className="w-4 h-4 xs:w-5 xs:h-5 text-blue-600 flex-shrink-0" />
                <span className="hidden xs:inline">Watch Success Stories</span>
                <span className="xs:hidden">Success Stories</span>
              </motion.button>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Bottom Wave - Responsive */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1440 120" className="w-full h-12 xs:h-16 sm:h-20 fill-white">
          <path d="M0,64L48,69.3C96,75,192,85,288,80C384,75,480,53,576,48C672,43,768,53,864,64C960,75,1056,85,1152,80C1248,75,1344,53,1392,42.7L1440,32L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z"></path>
        </svg>
      </div>
    </section>
  );
};
