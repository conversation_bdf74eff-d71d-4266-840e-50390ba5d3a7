import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowRight, BookOpen, Calculator, Video, FileText, Download, Phone } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Landscaping Business Resources | GroundUP Digital Growth Academy',
  description: 'Free resources for landscaping business owners. Guides, calculators, templates, and insights to help grow your landscaping business.',
  keywords: 'landscaping business resources, landscaping marketing guides, landscaping business tools, landscaping growth academy',
};

const resourceCategories = [
  {
    icon: BookOpen,
    title: 'Growth Guides',
    description: 'Comprehensive guides for landscaping business growth',
    resources: [
      'Complete Guide to Landscaping Lead Generation',
      'Seasonal Marketing Strategies for Landscapers',
      'Pricing Strategies for Landscaping Services',
      'Building a Premium Landscaping Brand'
    ]
  },
  {
    icon: Calculator,
    title: 'Business Calculators',
    description: 'Tools to help you make data-driven business decisions',
    resources: [
      'ROI Calculator for Marketing Investments',
      'Project Pricing Calculator',
      'Crew Productivity Calculator',
      'Customer Lifetime Value Calculator'
    ]
  },
  {
    icon: Video,
    title: 'Video Academy',
    description: 'Expert training videos for landscaping business owners',
    resources: [
      'Digital Marketing Fundamentals for Landscapers',
      'Sales Techniques for High-Value Projects',
      'Customer Service Excellence',
      'Technology Integration Strategies'
    ]
  },
  {
    icon: FileText,
    title: 'Templates & Tools',
    description: 'Ready-to-use templates for your landscaping business',
    resources: [
      'Proposal Templates',
      'Contract Templates',
      'Customer Onboarding Checklists',
      'Marketing Calendar Templates'
    ]
  }
];

const featuredResources = [
  {
    title: "The Complete Guide to Landscaping Digital Marketing",
    description: "A comprehensive 50-page guide covering everything from SEO to social media marketing specifically for landscaping businesses.",
    type: "PDF Guide",
    downloadCount: "2,500+",
    image: "/images/guide-digital-marketing.jpg"
  },
  {
    title: "Landscaping Business ROI Calculator",
    description: "Calculate the potential return on investment for your marketing efforts with this interactive tool designed for landscaping companies.",
    type: "Interactive Tool",
    downloadCount: "1,800+",
    image: "/images/roi-calculator.jpg"
  },
  {
    title: "Seasonal Marketing Calendar for Landscapers",
    description: "A year-round marketing calendar with proven strategies for each season, specifically tailored for landscaping businesses.",
    type: "Marketing Calendar",
    downloadCount: "3,200+",
    image: "/images/marketing-calendar.jpg"
  }
];

const webinars = [
  {
    title: "AI-Powered Lead Generation for Landscaping Businesses",
    date: "Next: March 15, 2024",
    time: "2:00 PM EST",
    description: "Learn how to leverage artificial intelligence to generate more qualified leads for your landscaping business.",
    registrations: "450+ registered"
  },
  {
    title: "Scaling Your Landscaping Business with Technology",
    date: "Next: March 22, 2024", 
    time: "2:00 PM EST",
    description: "Discover the technology tools and strategies that help landscaping businesses scale efficiently.",
    registrations: "380+ registered"
  }
];

export default function ResourcesPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              GroundUP Digital{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Growth Academy
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Free resources, tools, and insights to help landscaping business owners grow their companies. 
              Everything you need to succeed in the landscaping industry.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Download className="w-5 h-5 mr-2" />
                Browse All Resources
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                Join Growth Academy
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '50+', label: 'Free Resources' },
                { number: '10,000+', label: 'Downloads' },
                { number: '25+', label: 'Video Lessons' },
                { number: '100%', label: 'Free Access' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Resource Categories */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Everything You Need to Grow Your Landscaping Business
            </h2>
            <p className="text-lg text-gray-600">
              Comprehensive resources designed specifically for landscaping business owners. 
              All content is created by industry experts and proven in real landscaping businesses.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {resourceCategories.map((category, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <category.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{category.title}</h3>
                  <p className="text-gray-600 mb-4">{category.description}</p>
                  <ul className="space-y-2">
                    {category.resources.map((resource, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <Download className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {resource}
                      </li>
                    ))}
                  </ul>
                  <Button className="mt-4 w-full bg-gray-100 text-gray-900 hover:bg-gray-200">
                    Browse {category.title}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Featured Resources */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Most Popular Resources
            </h2>
            <p className="text-lg text-gray-600">
              Our most downloaded resources that have helped thousands of landscaping business owners grow their companies.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {featuredResources.map((resource, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="bg-blue-100 h-32 rounded-lg mb-4 flex items-center justify-center">
                    <FileText className="w-12 h-12 text-blue-600" />
                  </div>
                  <div className="mb-2">
                    <span className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">
                      {resource.type}
                    </span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{resource.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{resource.description}</p>
                  <div className="text-xs text-gray-500 mb-4">{resource.downloadCount} downloads</div>
                  <Button className="w-full bg-blue-600 text-white hover:bg-blue-700">
                    <Download className="w-4 h-4 mr-2" />
                    Download Free
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Upcoming Webinars */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Upcoming Webinars for Landscaping Business Owners
            </h2>
            <p className="text-lg text-gray-600">
              Join our live training sessions designed specifically for landscaping business owners. 
              Learn from industry experts and get your questions answered.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {webinars.map((webinar, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="mb-4">
                    <div className="text-sm text-blue-600 font-medium mb-1">{webinar.date}</div>
                    <div className="text-sm text-gray-600 mb-2">{webinar.time}</div>
                    <div className="text-xs text-green-600 font-medium">{webinar.registrations}</div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{webinar.title}</h3>
                  <p className="text-gray-600 mb-6">{webinar.description}</p>
                  <Button className="w-full bg-blue-600 text-white hover:bg-blue-700">
                    Register Free
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Accelerate Your Landscaping Business Growth?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              While our free resources provide valuable insights, our partnership program delivers personalized strategies 
              and hands-on support to transform your landscaping business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Phone className="w-5 h-5 mr-2" />
                Schedule Growth Consultation
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Explore Partnership
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
