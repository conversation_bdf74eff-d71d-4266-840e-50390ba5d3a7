'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Phone, 
  Mail, 
  Calendar,
  CheckCircle,
  Star,
  Award,
  TrendingUp,
  Users,
  Shield,
  Clock,
  ArrowRight,
  X,
  MessageCircle
} from 'lucide-react';
import { useHydration } from '@/hooks/useHydration';

interface ConversionOptimizationProps {
  showTrustSignals?: boolean;
  showUrgency?: boolean;
  showSocialProof?: boolean;
  showExitIntent?: boolean;
  primaryCTA?: string;
  secondaryCTA?: string;
}

export const ConversionOptimization: React.FC<ConversionOptimizationProps> = ({
  showTrustSignals = true,
  showUrgency = true,
  showSocialProof = true,
  showExitIntent = true,
  primaryCTA = "Get Free Marketing Audit",
  secondaryCTA = "Call (*************"
}) => {
  const [showExitIntentModal, setShowExitIntentModal] = useState(false);
  const [recentActivity, setRecentActivity] = useState<string[]>([]);
  const [urgencyTimer, setUrgencyTimer] = useState(24 * 60 * 60); // 24 hours in seconds
  const isMounted = useHydration();

  // Exit intent detection
  useEffect(() => {
    if (!isMounted || !showExitIntent) return;

    const handleMouseLeave = (e: MouseEvent) => {
      if (e.clientY <= 0) {
        setShowExitIntentModal(true);
      }
    };

    document.addEventListener('mouseleave', handleMouseLeave);
    return () => document.removeEventListener('mouseleave', handleMouseLeave);
  }, [isMounted, showExitIntent]);

  // Remove recent activity simulation

  // Urgency timer
  useEffect(() => {
    if (!isMounted || !showUrgency) return;

    const interval = setInterval(() => {
      setUrgencyTimer(prev => Math.max(0, prev - 1));
    }, 1000);

    return () => clearInterval(interval);
  }, [isMounted, showUrgency]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isMounted) return null;

  return (
    <>
      {/* Trust Signals Bar */}
      {showTrustSignals && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-green-600 to-blue-600 text-white py-2 px-4 text-center text-sm font-medium"
        >
          <div className="flex items-center justify-center gap-6 max-w-6xl mx-auto">
            <div className="flex items-center gap-2">
              <Award className="h-4 w-4" />
              <span>365+ Successful Clients</span>
            </div>
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              <span>$78M+ Revenue Generated</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="h-4 w-4" />
              <span>4.9/5 Rating</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <span>Industry Pioneer</span>
            </div>
          </div>
        </motion.div>
      )}



      {/* Social Proof Notifications - Removed */}

      {/* Exit Intent Modal */}
      <AnimatePresence>
        {showExitIntentModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
            onClick={() => setShowExitIntentModal(false)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white rounded-2xl p-8 max-w-md w-full relative"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={() => setShowExitIntentModal(false)}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>

              <div className="text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MessageCircle className="h-8 w-8 text-red-600" />
                </div>
                
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  Wait! Don't Miss Out!
                </h3>
                
                <p className="text-gray-600 mb-6">
                  Get your FREE marketing audit before you leave. See exactly how to get 300% more leads for your business.
                </p>
                
                <div className="space-y-3">
                  <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold py-3 px-6 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all">
                    Yes, Get My Free Audit
                  </button>
                  
                  <button 
                    onClick={() => setShowExitIntentModal(false)}
                    className="w-full text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    No thanks, I'll pass on more leads
                  </button>
                </div>
                
                <div className="mt-4 text-xs text-gray-500">
                  ✓ No spam ✓ No obligation ✓ Results in 30 days
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Sticky CTA Bar */}
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 2 }}
        className="fixed bottom-0 left-0 right-0 bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 z-40 shadow-lg"
      >
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div className="flex-1">
            <p className="font-bold text-lg">Ready to 3X Your Leads?</p>
            <p className="text-sm opacity-90">Join 365+ successful businesses</p>
          </div>
          
          <div className="flex items-center gap-3">
            <button className="bg-white text-blue-600 font-bold px-6 py-2 rounded-lg hover:bg-gray-100 transition-colors">
              {primaryCTA}
            </button>
            <button className="border border-white text-white font-bold px-6 py-2 rounded-lg hover:bg-white hover:text-blue-600 transition-colors">
              {secondaryCTA}
            </button>
          </div>
        </div>
      </motion.div>

      {/* Floating Trust Badges */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 3 }}
        className="fixed bottom-24 xs:bottom-32 right-3 xs:right-4 z-40"
      >
        <div className="bg-white rounded-lg shadow-lg p-3 xs:p-4 border border-gray-200">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-2">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
              ))}
            </div>
            <p className="text-sm font-bold text-gray-900">4.9/5 Rating</p>
            <p className="text-xs text-gray-600">365+ Reviews</p>
          </div>
        </div>
      </motion.div>
    </>
  );
};
