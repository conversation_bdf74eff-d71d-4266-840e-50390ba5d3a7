import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowRight, Users, TrendingUp, Calendar, DollarSign, Star, CheckCircle, Phone } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'CRM & Sales Enablement for Landscaping Businesses | GroundUP Digital',
  description: 'Comprehensive CRM and sales systems designed specifically for landscaping business owners. Manage leads, track projects, and grow revenue.',
  keywords: 'landscaping CRM, landscaping sales software, landscape business management, lawn care CRM, landscaping project management',
};

const crmFeatures = [
  {
    icon: Users,
    title: 'Customer Relationship Management',
    description: 'Complete customer management system designed for landscaping business workflows.',
    benefits: ['Lead tracking & scoring', 'Customer communication history', 'Project timeline management', 'Automated follow-up sequences']
  },
  {
    icon: DollarSign,
    title: 'Sales Pipeline Management',
    description: 'Visual sales pipeline that helps landscaping businesses close more deals.',
    benefits: ['Opportunity tracking', 'Proposal management', 'Revenue forecasting', 'Win/loss analysis']
  },
  {
    icon: Calendar,
    title: 'Project & Schedule Management',
    description: 'Integrated scheduling and project management for landscaping operations.',
    benefits: ['Crew scheduling', 'Equipment tracking', 'Material management', 'Client communication']
  }
];

const packages = [
  {
    name: "CRM Essentials",
    price: "$297",
    period: "/month",
    description: "Core CRM functionality for small landscaping businesses",
    features: [
      "Lead management system",
      "Customer database",
      "Basic project tracking",
      "Email integration",
      "Mobile app access"
    ],
    popular: false
  },
  {
    name: "Sales Pro",
    price: "$597",
    period: "/month", 
    description: "Advanced CRM and sales tools for growing landscaping companies",
    features: [
      "Advanced sales pipeline",
      "Proposal automation",
      "Crew scheduling",
      "Financial reporting",
      "Integration support",
      "Training & onboarding"
    ],
    popular: true
  },
  {
    name: "Enterprise Suite",
    price: "$1,297",
    period: "/month",
    description: "Complete business management platform for large landscaping operations",
    features: [
      "Multi-location management",
      "Advanced analytics",
      "Custom integrations",
      "API access",
      "Dedicated support",
      "Custom development"
    ],
    popular: false
  }
];

export default function CRMSalesPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Users className="w-4 h-4" />
              <span className="text-sm font-semibold">CRM & Sales for Landscaping Businesses</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Complete Business Management for{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscaping Companies
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Comprehensive CRM and sales systems designed specifically for landscaping business owners. 
              Manage leads, track projects, schedule crews, and grow your revenue.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Users className="w-5 h-5 mr-2" />
                See CRM Demo
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                Get Free Trial
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '45%', label: 'Revenue Increase' },
                { number: '60%', label: 'Time Savings' },
                { number: '85%', label: 'Client Retention' },
                { number: '100+', label: 'Businesses Using' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              CRM Built Specifically for Landscaping Business Owners
            </h2>
            <p className="text-lg text-gray-600">
              Every feature is designed around the unique needs of landscaping businesses. 
              From lead generation to project completion, manage your entire business in one platform.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {crmFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              CRM Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Business management solutions designed specifically for landscaping business owners. 
              Choose the CRM package that fits your business size and needs.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Streamline Your Landscaping Business Operations?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a free CRM demo and see how our business management platform can help your landscaping business 
              grow more efficiently and profitably.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Users className="w-5 h-5 mr-2" />
                Get Free Demo
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule Consultation
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
