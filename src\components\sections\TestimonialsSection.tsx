'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Star, Quote, TreePine, Home, Bug } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';

const testimonials = [
  {
    name: '<PERSON>',
    company: 'Verde Landscapes',
    role: 'Business Owner',
    content: 'GroundUP Digital completely transformed our landscaping business. We went from struggling to compete to becoming the #1 landscaping company in Austin. Their exclusive focus on landscaping businesses made all the difference.',
    rating: 5,
    results: '340% increase in leads',
    industry: 'landscaping',
    icon: TreePine,
    location: 'Austin, TX'
  },
  {
    name: '<PERSON>',
    company: 'Premier Outdoor Solutions',
    role: 'Business Owner',
    content: 'Finally, a marketing partner that truly understands landscaping. Their AR/VR presentations helped us close deals we never thought possible. Our average project value increased by 120%.',
    rating: 5,
    results: '290% lead quality increase',
    industry: 'landscaping',
    icon: TreePine,
    location: 'Denver, CO'
  },
  {
    name: 'David Park',
    company: 'GreenTech Landscapes',
    role: 'Business Owner',
    content: 'The IoT solutions they implemented created an entirely new revenue stream for our landscaping business. We now have predictable monthly income from smart irrigation monitoring.',
    rating: 5,
    results: '240% recurring revenue',
    industry: 'landscaping',
    icon: TreePine,
    location: 'Seattle, WA'
  }
];

const additionalTestimonials = [
  {
    name: 'Jennifer Walsh',
    company: 'Premier Lawn Care',
    content: 'Best investment we ever made for our landscaping business. The ROI has been incredible.',
    rating: 5,
    industry: 'landscaping'
  },
  {
    name: 'Robert Kim',
    company: 'Elite Landscape Design',
    content: 'Professional, results-driven, and they understand the landscaping business.',
    rating: 5,
    industry: 'landscaping'
  },
  {
    name: 'Lisa Martinez',
    company: 'Sunshine Landscaping Co.',
    content: 'Doubled our landscaping customer base in just 6 months. Highly recommended!',
    rating: 5,
    industry: 'landscaping'
  }
];

export const TestimonialsSection: React.FC = () => {
  return (
    <section className="py-24 lg:py-32 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center max-w-4xl mx-auto mb-16"
        >
          <div className="inline-flex items-center gap-2 bg-yellow-50 rounded-full px-6 py-3 mb-6">
            <Star className="w-5 h-5 text-yellow-500 fill-current" />
            <span className="text-sm font-semibold text-yellow-700">4.9/5 Average Rating • 200+ Landscaping Business Reviews</span>
            <div className="flex gap-1">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-4 h-4 text-yellow-500 fill-current" />
              ))}
            </div>
          </div>
          
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            What Landscaping Business Owners Say About{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600">
              Their Success
            </span>
          </h2>

          <p className="text-xl text-gray-600">
            Don't just take our word for it. See how we've helped landscaping business owners
            transform their companies and achieve remarkable growth.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 xs:gap-6 sm:gap-8 mb-8 xs:mb-12 sm:mb-16">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-xl transition-shadow duration-300">
                <CardContent className="p-4 xs:p-6 sm:p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center">
                      <testimonial.icon className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.company}</div>
                      <div className="text-sm text-gray-500">{testimonial.location}</div>
                    </div>
                  </div>

                  <div className="relative mb-6">
                    <Quote className="absolute -top-2 -left-2 w-8 h-8 text-blue-200" />
                    <p className="text-gray-600 italic pl-6">
                      "{testimonial.content}"
                    </p>
                  </div>

                  <div className="bg-green-50 rounded-lg p-4 mb-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600 mb-1">
                        {testimonial.results}
                      </div>
                      <div className="text-sm text-green-700">Results Achieved</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 mb-4">
                    <div className="flex gap-1">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 text-yellow-500 fill-current" />
                      ))}
                    </div>
                    <span className="text-sm text-gray-500">({testimonial.rating}.0)</span>
                  </div>

                  <div className="border-t pt-4">
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-500">{testimonial.role}</div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 xs:gap-6 mb-8 xs:mb-12 sm:mb-16"
        >
          {additionalTestimonials.map((testimonial, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-4 xs:p-6 text-center">
              <div className="flex justify-center gap-1 mb-3">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-500 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 italic mb-4">"{testimonial.content}"</p>
              <div className="font-semibold text-gray-900">{testimonial.name}</div>
              <div className="text-sm text-gray-500">{testimonial.company}</div>
            </div>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8 lg:p-12"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Trusted by Local Service Businesses Nationwide
            </h3>
            <p className="text-gray-600">
              Join hundreds of successful businesses that have transformed their growth with our proven strategies.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { number: '500+', label: 'Businesses Served' },
              { number: '4.9★', label: 'Average Rating' },
              { number: '300%', label: 'Avg Lead Increase' },
              { number: '98%', label: 'Client Retention' }
            ].map((metric, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">{metric.number}</div>
                <div className="text-gray-600">{metric.label}</div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};
