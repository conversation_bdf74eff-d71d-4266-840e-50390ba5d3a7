import { Metadata } from 'next';

export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  ogImage?: string;
  noindex?: boolean;
  structuredData?: any;
}

export interface LocalBusinessData {
  name: string;
  description: string;
  address: {
    streetAddress: string;
    addressLocality: string;
    addressRegion: string;
    postalCode: string;
    addressCountry: string;
  };
  phone: string;
  email: string;
  website: string;
  serviceArea: string[];
  services: string[];
  priceRange: string;
  openingHours: string[];
  paymentAccepted: string[];
  foundingDate: string;
  logo: string;
  image: string[];
  sameAs: string[];
}

export const defaultLocalBusiness: LocalBusinessData = {
  name: "GroundUPDigital",
  description: "Professional digital marketing agency specializing in web development, SEO, and lead generation for local service providers including landscapers, roofers, and pest control companies.",
  address: {
    streetAddress: "123 Digital Drive",
    addressLocality: "Austin",
    addressRegion: "TX",
    postalCode: "78701",
    addressCountry: "US"
  },
  phone: "******-123-4567",
  email: "<EMAIL>",
  website: "https://groundupdigital.com",
  serviceArea: [
    "Austin, TX",
    "Dallas, TX", 
    "Houston, TX",
    "San Antonio, TX",
    "Fort Worth, TX"
  ],
  services: [
    "Web Development",
    "Search Engine Optimization",
    "Lead Generation",
    "Social Media Marketing",
    "Pay-Per-Click Advertising",
    "Content Marketing",
    "Brand Reputation Management"
  ],
  priceRange: "$500-$10000",
  openingHours: [
    "Mo-Fr 09:00-18:00",
    "Sa 10:00-16:00"
  ],
  paymentAccepted: [
    "Cash",
    "Credit Card",
    "Check",
    "Bank Transfer"
  ],
  foundingDate: "2020-01-15",
  logo: "https://groundupdigital.com/logo.png",
  image: [
    "https://groundupdigital.com/office.jpg",
    "https://groundupdigital.com/team.jpg"
  ],
  sameAs: [
    "https://www.facebook.com/groundupdigital",
    "https://www.linkedin.com/company/groundupdigital",
    "https://twitter.com/groundupdigital",
    "https://www.instagram.com/groundupdigital"
  ]
};

export function generateLocalBusinessSchema(data: LocalBusinessData = defaultLocalBusiness) {
  return {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "@id": `${data.website}#organization`,
    "name": data.name,
    "description": data.description,
    "url": data.website,
    "logo": {
      "@type": "ImageObject",
      "url": data.logo
    },
    "image": data.image,
    "telephone": data.phone,
    "email": data.email,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": data.address.streetAddress,
      "addressLocality": data.address.addressLocality,
      "addressRegion": data.address.addressRegion,
      "postalCode": data.address.postalCode,
      "addressCountry": data.address.addressCountry
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "30.2672",
      "longitude": "-97.7431"
    },
    "areaServed": data.serviceArea.map(area => ({
      "@type": "City",
      "name": area
    })),
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Digital Marketing Services",
      "itemListElement": data.services.map((service, index) => ({
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": service,
          "description": `Professional ${service.toLowerCase()} services for local businesses`
        }
      }))
    },
    "priceRange": data.priceRange,
    "openingHoursSpecification": data.openingHours.map(hours => {
      const [days, time] = hours.split(' ');
      const [opens, closes] = time.split('-');
      return {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": days.split('-').map(day => {
          const dayMap: { [key: string]: string } = {
            'Mo': 'Monday',
            'Tu': 'Tuesday', 
            'We': 'Wednesday',
            'Th': 'Thursday',
            'Fr': 'Friday',
            'Sa': 'Saturday',
            'Su': 'Sunday'
          };
          return dayMap[day] || day;
        }),
        "opens": opens,
        "closes": closes
      };
    }),
    "paymentAccepted": data.paymentAccepted,
    "foundingDate": data.foundingDate,
    "sameAs": data.sameAs,
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "127",
      "bestRating": "5",
      "worstRating": "1"
    }
  };
}

export function generateServiceSchema(serviceName: string, description: string, price?: string) {
  return {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": serviceName,
    "description": description,
    "provider": {
      "@type": "LocalBusiness",
      "name": defaultLocalBusiness.name,
      "url": defaultLocalBusiness.website
    },
    "areaServed": defaultLocalBusiness.serviceArea.map(area => ({
      "@type": "City", 
      "name": area
    })),
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": serviceName,
      "itemListElement": [{
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": serviceName,
          "description": description
        },
        "price": price || "Contact for pricing",
        "priceCurrency": "USD"
      }]
    }
  };
}

export function generateReviewSchema(reviews: any[]) {
  return reviews.map(review => ({
    "@context": "https://schema.org",
    "@type": "Review",
    "author": {
      "@type": "Person",
      "name": review.author
    },
    "reviewRating": {
      "@type": "Rating",
      "ratingValue": review.rating,
      "bestRating": "5",
      "worstRating": "1"
    },
    "reviewBody": review.text,
    "datePublished": review.date,
    "itemReviewed": {
      "@type": "LocalBusiness",
      "name": defaultLocalBusiness.name,
      "url": defaultLocalBusiness.website
    }
  }));
}

export function generateSEOMetadata(seoData: SEOData): Metadata {
  const baseUrl = 'https://groundupdigital.com';
  
  return {
    title: seoData.title,
    description: seoData.description,
    keywords: seoData.keywords,
    robots: seoData.noindex ? 'noindex,nofollow' : 'index,follow',
    canonical: seoData.canonical || baseUrl,
    openGraph: {
      title: seoData.title,
      description: seoData.description,
      url: seoData.canonical || baseUrl,
      siteName: 'GroundUPDigital',
      images: [{
        url: seoData.ogImage || `${baseUrl}/og-image.jpg`,
        width: 1200,
        height: 630,
        alt: seoData.title
      }],
      locale: 'en_US',
      type: 'website'
    },
    twitter: {
      card: 'summary_large_image',
      title: seoData.title,
      description: seoData.description,
      images: [seoData.ogImage || `${baseUrl}/og-image.jpg`],
      creator: '@groundupdigital'
    },
    alternates: {
      canonical: seoData.canonical || baseUrl
    },
    other: {
      'geo.region': 'US-TX',
      'geo.placename': 'Austin',
      'geo.position': '30.2672;-97.7431',
      'ICBM': '30.2672, -97.7431'
    }
  };
}

// Generate FAQ schema
export function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
}

// Generate breadcrumb schema
export function generateBreadcrumbSchema(breadcrumbs: Array<{ name: string; url: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  };
}

// Generate article schema
export function generateArticleSchema(article: {
  title: string;
  description: string;
  author: string;
  datePublished: string;
  dateModified?: string;
  url: string;
  image?: string;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": article.title,
    "description": article.description,
    "author": {
      "@type": "Person",
      "name": article.author
    },
    "publisher": {
      "@type": "Organization",
      "name": defaultLocalBusiness.name,
      "logo": {
        "@type": "ImageObject",
        "url": defaultLocalBusiness.logo
      }
    },
    "datePublished": article.datePublished,
    "dateModified": article.dateModified || article.datePublished,
    "url": article.url,
    "image": article.image || defaultLocalBusiness.logo
  };
}

// Industry-specific keywords and data
export const industryData = {
  landscaping: {
    keywords: [
      'landscaping services', 'lawn care', 'landscape design', 'tree services',
      'irrigation', 'hardscaping', 'garden maintenance', 'outdoor lighting'
    ],
    services: [
      'Lawn Care & Maintenance', 'Landscape Design', 'Tree Services',
      'Irrigation Systems', 'Hardscaping', 'Seasonal Cleanup'
    ],
    searchTerms: [
      'landscaping near me', 'lawn care service', 'landscape design',
      'tree removal', 'sprinkler installation'
    ]
  },
  roofing: {
    keywords: [
      'roofing services', 'roof repair', 'roof replacement', 'emergency roofing',
      'storm damage', 'roof inspection', 'commercial roofing', 'residential roofing'
    ],
    services: [
      'Roof Repair', 'Roof Replacement', 'Emergency Roofing',
      'Roof Inspection', 'Storm Damage Repair', 'Commercial Roofing'
    ],
    searchTerms: [
      'roofer near me', 'roof repair', 'emergency roof repair',
      'roof replacement', 'storm damage repair'
    ]
  },
  pestControl: {
    keywords: [
      'pest control', 'exterminator', 'termite treatment', 'rodent control',
      'bed bug treatment', 'ant control', 'wildlife removal', 'pest management'
    ],
    services: [
      'General Pest Control', 'Termite Treatment', 'Rodent Control',
      'Bed Bug Treatment', 'Ant Control', 'Wildlife Removal'
    ],
    searchTerms: [
      'pest control near me', 'exterminator', 'termite treatment',
      'bed bug exterminator', 'rodent removal'
    ]
  }
};

// SEO-optimized content templates
export const seoContent = {
  homepage: {
    title: "Digital Marketing Agency for Local Service Providers | GroundUPDigital",
    description: "Professional digital marketing services for landscapers, roofers, and pest control companies. Web development, SEO, lead generation, and PPC advertising that drives results.",
    keywords: "digital marketing agency, local service providers, landscaping marketing, roofing marketing, pest control marketing, web development, SEO services, lead generation"
  },
  services: {
    webDevelopment: {
      title: "Professional Website Development for Local Service Providers",
      description: "Custom website development for landscapers, roofers, and pest control companies. Mobile-responsive, SEO-optimized websites that convert visitors into customers.",
      keywords: "website development, local service websites, landscaping websites, roofing websites, pest control websites, mobile responsive design"
    },
    seo: {
      title: "Local SEO Services for Service Providers | Rank Higher on Google",
      description: "Expert local SEO services for landscaping, roofing, and pest control companies. Dominate local search results and attract more qualified customers in your area.",
      keywords: "local SEO, landscaping SEO, roofing SEO, pest control SEO, Google My Business optimization, local search marketing"
    },
    leadGeneration: {
      title: "Lead Generation Services for Local Service Providers",
      description: "Proven lead generation strategies for landscapers, roofers, and pest control companies. Generate high-quality leads and grow your customer base consistently.",
      keywords: "lead generation, landscaping leads, roofing leads, pest control leads, local service leads, customer acquisition"
    }
  }
};
