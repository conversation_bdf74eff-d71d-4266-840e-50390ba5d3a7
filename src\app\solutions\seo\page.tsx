import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, Search, MapPin, TrendingUp, Users, Star, CheckCircle, Phone } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Local SEO for Landscaping Businesses | GroundUP Digital',
  description: 'Help your landscaping business dominate local search results and attract more customers. Proven SEO strategies specifically for landscaping companies.',
  keywords: 'landscaping SEO, local SEO for landscapers, landscaping business marketing, lawn care SEO, landscape contractor SEO',
};

const seoFeatures = [
  {
    icon: MapPin,
    title: 'Local Search Domination',
    description: 'Get your landscaping business found when customers search for services in your area.',
    benefits: ['Google My Business optimization', 'Local citation building', 'Service area targeting', 'Review management']
  },
  {
    icon: Search,
    title: 'Industry-Specific Keywords',
    description: 'Target the exact terms your potential customers use when searching for landscaping services.',
    benefits: ['Landscaping keyword research', 'Service-specific optimization', 'Seasonal campaign planning', 'Competitor analysis']
  },
  {
    icon: TrendingUp,
    title: 'Proven Results',
    description: 'Our landscaping clients see average increases of 247% in qualified leads from search.',
    benefits: ['Increased website traffic', 'More phone calls', 'Higher quality leads', 'Better ROI tracking']
  }
];

const caseStudy = {
  company: "Green Valley Landscaping",
  location: "Austin, TX",
  results: [
    { metric: "Local Search Rankings", improvement: "+340%" },
    { metric: "Website Traffic", improvement: "+280%" },
    { metric: "Phone Calls", improvement: "+195%" },
    { metric: "New Customers", improvement: "+156%" }
  ]
};

const packages = [
  {
    name: "Local Foundation",
    price: "$1,497",
    period: "/month",
    description: "Perfect for small landscaping businesses looking to establish local presence",
    features: [
      "Google My Business optimization",
      "Local citation building (25 sites)",
      "On-page SEO optimization",
      "Monthly ranking reports",
      "Review management setup"
    ],
    popular: false
  },
  {
    name: "Market Leader",
    price: "$2,497",
    period: "/month", 
    description: "Ideal for growing landscaping companies ready to dominate their market",
    features: [
      "Everything in Local Foundation",
      "Advanced keyword targeting (50+ terms)",
      "Content marketing strategy",
      "Local link building campaign",
      "Competitor monitoring",
      "Conversion tracking setup"
    ],
    popular: true
  },
  {
    name: "Regional Domination",
    price: "$3,997",
    period: "/month",
    description: "For established landscaping businesses expanding across multiple service areas",
    features: [
      "Everything in Market Leader",
      "Multi-location SEO strategy",
      "Advanced schema markup",
      "Video SEO optimization",
      "Dedicated account manager",
      "Quarterly strategy sessions"
    ],
    popular: false
  }
];

export default function SEOPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Search className="w-4 h-4" />
              <span className="text-sm font-semibold">Local SEO for Landscaping Businesses</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Dominate Local Search for Your{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscaping Business
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Get found by customers searching for landscaping services in your area. 
              Our proven SEO strategies help landscaping business owners attract more qualified leads and grow their revenue.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Phone className="w-5 h-5 mr-2" />
                Get Free SEO Audit
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                View Case Studies
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '247%', label: 'Avg Lead Increase' },
                { number: '200+', label: 'Landscaping Clients' },
                { number: '89%', label: 'Client Retention' },
                { number: '24/7', label: 'Support Available' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Why Landscaping Businesses Choose Our SEO Services
            </h2>
            <p className="text-lg text-gray-600">
              We understand the unique challenges landscaping business owners face. Our SEO strategies are specifically designed 
              to help landscaping companies attract local customers and grow their business.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {seoFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Real Results for Landscaping Businesses
              </h2>
              <p className="text-lg text-gray-600">
                See how we helped {caseStudy.company} dominate local search in {caseStudy.location}
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A family-owned landscaping business in {caseStudy.location} struggling to compete with larger companies. 
                      After implementing our SEO strategy, they became the #1 landscaping company in their area.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Read Full Case Study
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              SEO Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Choose the perfect SEO package to help your landscaping business grow. All packages include dedicated support 
              and are specifically designed for landscaping companies.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Dominate Local Search for Your Landscaping Business?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a free SEO audit and discover exactly how to get your landscaping business found by more customers online.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Phone className="w-5 h-5 mr-2" />
                Get Free SEO Audit
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule Consultation
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
