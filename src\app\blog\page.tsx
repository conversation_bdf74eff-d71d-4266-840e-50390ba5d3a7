'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  Calendar, 
  User, 
  ArrowRight,
  Search,
  TrendingUp,
  Lightbulb
} from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

const blogPosts = [
  {
    id: 1,
    title: '10 Local SEO Tips Every Landscaper Should Know in 2024',
    slug: 'local-seo-tips-landscapers-2024',
    excerpt: 'Discover the latest local SEO strategies that are helping landscaping companies dominate their local markets and generate more qualified leads.',
    author: '<PERSON>',
    date: '2024-01-15',
    category: 'SEO',
    readTime: '8 min read',
    featured: true,
    image: '/blog/local-seo-landscaping.jpg'
  },
  {
    id: 2,
    title: 'How to Generate Emergency Roofing Leads with Google Ads',
    slug: 'roofing-google-ads-emergency-leads',
    excerpt: 'Learn the exact Google Ads strategies that roofing contractors use to capture high-value emergency repair leads 24/7.',
    author: '<PERSON>',
    date: '2024-01-12',
    category: 'PP<PERSON>',
    readTime: '6 min read',
    featured: true,
    image: '/blog/roofing-google-ads.jpg'
  },
  {
    id: 3,
    title: 'Building Trust Online: Reputation Management for Pest Control',
    slug: 'pest-control-reputation-management',
    excerpt: 'Why online reviews matter more than ever for pest control companies and how to build a 5-star reputation that drives business.',
    author: 'Emily Rodriguez',
    date: '2024-01-10',
    category: 'Reputation',
    readTime: '7 min read',
    featured: false,
    image: '/blog/pest-control-reviews.jpg'
  },
  {
    id: 4,
    title: 'Website Design Trends for Local Service Businesses',
    slug: 'web-design-trends-local-services',
    excerpt: 'The latest web design trends that are converting visitors into customers for landscaping, roofing, and pest control companies.',
    author: 'Sarah Johnson',
    date: '2024-01-08',
    category: 'Web Design',
    readTime: '5 min read',
    featured: false,
    image: '/blog/web-design-trends.jpg'
  },
  {
    id: 5,
    title: 'Social Media Marketing for Landscapers: A Complete Guide',
    slug: 'landscaping-social-media-guide',
    excerpt: 'How to showcase your landscaping work on social media and turn followers into paying customers with proven strategies.',
    author: 'Mike Chen',
    date: '2024-01-05',
    category: 'Social Media',
    readTime: '9 min read',
    featured: false,
    image: '/blog/landscaping-social-media.jpg'
  },
  {
    id: 6,
    title: 'Lead Generation Funnels That Work for Service Businesses',
    slug: 'lead-generation-funnels-service-businesses',
    excerpt: 'Step-by-step guide to creating lead generation funnels that consistently bring in qualified prospects for your local service business.',
    author: 'Emily Rodriguez',
    date: '2024-01-03',
    category: 'Lead Generation',
    readTime: '10 min read',
    featured: false,
    image: '/blog/lead-generation-funnels.jpg'
  }
];

const categories = [
  { name: 'All Posts', count: 6, active: true },
  { name: 'SEO', count: 2, active: false },
  { name: 'PPC', count: 1, active: false },
  { name: 'Web Design', count: 1, active: false },
  { name: 'Social Media', count: 1, active: false },
  { name: 'Lead Generation', count: 1, active: false }
];

export default function BlogPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="lg">
        <Container>
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="heading-1 text-gray-900 mb-6">
                Digital Marketing{' '}
                <span className="gradient-text">Insights & Tips</span>
              </h1>
              
              <p className="text-large text-gray-600 mb-8">
                Stay ahead of the competition with the latest digital marketing strategies, 
                tips, and insights specifically for local service providers.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <div className="relative max-w-md mx-auto sm:mx-0">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    placeholder="Search articles..."
                    className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  />
                </div>
                <Button variant="primary">
                  Subscribe to Newsletter
                </Button>
              </div>
            </motion.div>
          </div>
        </Container>
      </Section>

      {/* Categories */}
      <Section background="white" padding="sm">
        <Container>
          <div className="flex flex-wrap gap-4 justify-center">
            {categories.map((category, index) => (
              <motion.button
                key={category.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                  category.active
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category.name} ({category.count})
              </motion.button>
            ))}
          </div>
        </Container>
      </Section>

      {/* Featured Posts */}
      <Section background="white" padding="lg">
        <Container>
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Featured Articles</h2>
            <p className="text-gray-600">Our most popular and impactful content</p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 xs:gap-6 sm:gap-8 mb-8 xs:mb-12 sm:mb-16">
            {blogPosts.filter(post => post.featured).map((post, index) => (
              <motion.div
                key={post.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
              >
                <Link href={`/blog/${post.slug}`}>
                  <Card className="h-full overflow-hidden group cursor-pointer">
                  {/* Image Placeholder */}
                  <div className="h-48 bg-gradient-to-br from-blue-100 to-green-100 flex items-center justify-center">
                    <div className="text-center">
                      <Lightbulb className="h-12 w-12 text-blue-600 mx-auto mb-2" />
                      <span className="text-sm text-gray-600">Featured Article</span>
                    </div>
                  </div>
                  
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Badge variant="primary" size="sm">
                        {post.category}
                      </Badge>
                      <span className="text-sm text-gray-500">{post.readTime}</span>
                    </div>
                    
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                      {post.title}
                    </h3>
                    
                    <p className="text-gray-600">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-gray-600" />
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">{post.author}</div>
                          <div className="text-xs text-gray-500 flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            {new Date(post.date).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      
                      <Button variant="ghost" size="sm" className="group-hover:translate-x-1 transition-transform duration-200">
                        Read More
                        <ArrowRight className="ml-1 h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </div>
        </Container>
      </Section>

      {/* All Posts */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Latest Articles</h2>
            <p className="text-gray-600">Stay updated with our latest insights and tips</p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 xs:gap-6 sm:gap-8">
            {blogPosts.filter(post => !post.featured).map((post, index) => (
              <motion.div
                key={post.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Link href={`/blog/${post.slug}`}>
                  <Card className="h-full overflow-hidden group cursor-pointer">
                    {/* Image Placeholder */}
                    <div className="h-40 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      <TrendingUp className="h-8 w-8 text-gray-400" />
                    </div>

                    <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Badge variant="secondary" size="sm">
                        {post.category}
                      </Badge>
                      <span className="text-sm text-gray-500">{post.readTime}</span>
                    </div>
                    
                    <h3 className="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                      {post.title}
                    </h3>
                    
                    <p className="text-gray-600 text-sm">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                      <div className="flex items-center space-x-2">
                        <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                          <User className="h-3 w-3 text-gray-600" />
                        </div>
                        <div className="text-xs text-gray-500">
                          {post.author}
                        </div>
                      </div>
                      
                      <div className="text-xs text-gray-500 flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {new Date(post.date).toLocaleDateString()}
                      </div>
                    </div>
                  </CardContent>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              Load More Articles
            </Button>
          </div>
        </Container>
      </Section>

      {/* Newsletter CTA */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="heading-2 text-gray-900 mb-6">
              Stay Ahead of the Competition
            </h2>
            <p className="text-large text-gray-600 mb-8">
              Get the latest digital marketing tips and strategies delivered to your inbox every week.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              />
              <Button variant="primary">
                Subscribe
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
