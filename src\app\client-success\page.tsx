import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowRight, TrendingUp, Users, DollarSign, Award, CheckCircle, Phone, Star } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Landscaping Business Success Stories | GroundUP Digital Client Results',
  description: 'Real results from 200+ landscaping business owners. See how GroundUP Digital has helped landscaping companies achieve 247% lead increases and 156% revenue growth.',
  keywords: 'landscaping business success stories, landscaping marketing results, landscaping business growth case studies, GroundUP Digital results',
};

const successMetrics = [
  { icon: TrendingUp, metric: '247%', label: 'Average Lead Increase', description: 'Our landscaping clients see dramatic increases in qualified leads' },
  { icon: DollarSign, metric: '156%', label: 'Revenue Growth', description: 'Average revenue increase within first 12 months' },
  { icon: Users, metric: '89%', label: 'Client Retention', description: 'Landscaping business owners stay with us year after year' },
  { icon: Award, metric: '200+', label: 'Success Stories', description: 'Landscaping businesses transformed across the country' }
];

const caseStudies = [
  {
    company: "Verde Landscapes",
    owner: "Mike Rodriguez",
    location: "Austin, TX",
    businessType: "Residential & Commercial Landscaping",
    challenge: "Struggling to compete with larger companies and generate consistent leads",
    solution: "Comprehensive digital ecosystem with AI-powered lead generation and local SEO",
    results: [
      { metric: "Revenue Growth", value: "+163%", timeframe: "18 months" },
      { metric: "Monthly Leads", value: "+340%", timeframe: "12 months" },
      { metric: "Project Value", value: "+85%", timeframe: "Average increase" },
      { metric: "Market Share", value: "+45%", timeframe: "Local market" }
    ],
    testimonial: "GroundUP Digital didn't just improve our marketing—they transformed our entire business. We went from struggling to compete to becoming the go-to landscaping company in Austin.",
    image: "/images/case-study-verde.jpg"
  },
  {
    company: "Premier Outdoor Solutions",
    owner: "Sarah Chen",
    location: "Denver, CO",
    businessType: "High-End Landscape Design",
    challenge: "Needed to attract affluent clients and showcase premium design capabilities",
    solution: "AR/VR visualization platform with premium content marketing and targeted advertising",
    results: [
      { metric: "Lead Quality", value: "+290%", timeframe: "High-value leads" },
      { metric: "Project Size", value: "+120%", timeframe: "Average value" },
      { metric: "Close Rate", value: "+75%", timeframe: "Sales improvement" },
      { metric: "Brand Recognition", value: "+200%", timeframe: "Market awareness" }
    ],
    testimonial: "The AR/VR presentations completely changed how we sell our services. Clients can now see exactly what their landscape will look like, and our close rate has skyrocketed.",
    image: "/images/case-study-premier.jpg"
  },
  {
    company: "GreenTech Landscapes",
    owner: "David Park",
    location: "Seattle, WA",
    businessType: "Smart Landscaping & Maintenance",
    challenge: "Wanted to differentiate with technology and create recurring revenue streams",
    solution: "IoT integration platform with smart irrigation systems and client engagement apps",
    results: [
      { metric: "Recurring Revenue", value: "+240%", timeframe: "Monthly contracts" },
      { metric: "Client Retention", value: "+95%", timeframe: "Year-over-year" },
      { metric: "Operational Efficiency", value: "+60%", timeframe: "Time savings" },
      { metric: "Upsell Rate", value: "+180%", timeframe: "Additional services" }
    ],
    testimonial: "The IoT solutions created an entirely new revenue stream for us. Our clients love the smart systems, and we love the predictable monthly income.",
    image: "/images/case-study-greentech.jpg"
  }
];

const industryResults = [
  { category: "Residential Landscaping", companies: 120, avgGrowth: "185%", topResult: "340% lead increase" },
  { category: "Commercial Landscaping", companies: 45, avgGrowth: "210%", topResult: "450% revenue growth" },
  { category: "Landscape Design", companies: 25, avgGrowth: "165%", topResult: "290% project value increase" },
  { category: "Lawn Care Services", companies: 35, avgGrowth: "195%", topResult: "380% customer acquisition" }
];

export default function ClientSuccessPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Real Success Stories from{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscaping Business Owners
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              See how 200+ landscaping business owners have transformed their companies with GroundUP Digital. 
              Real results, real growth, real success stories.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Phone className="w-5 h-5 mr-2" />
                Discuss Your Success Story
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                Download Case Studies
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
          </div>
        </Container>
      </Section>

      {/* Success Metrics */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Proven Results Across 200+ Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Our track record speaks for itself. Here are the average results our landscaping business partners achieve.
            </p>
          </div>
          
          <div className="grid md:grid-cols-4 gap-6">
            {successMetrics.map((metric, index) => (
              <Card key={index} className="text-center h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <metric.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{metric.metric}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{metric.label}</h3>
                  <p className="text-sm text-gray-600">{metric.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Detailed Case Studies */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              In-Depth Success Stories
            </h2>
            <p className="text-lg text-gray-600">
              Dive deep into how we've helped landscaping business owners overcome challenges and achieve remarkable growth.
            </p>
          </div>
          
          <div className="space-y-12">
            {caseStudies.map((study, index) => (
              <Card key={index} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="grid lg:grid-cols-2 gap-0">
                    <div className="p-8">
                      <div className="mb-6">
                        <h3 className="text-2xl font-bold text-gray-900 mb-2">{study.company}</h3>
                        <p className="text-gray-600 mb-1">{study.owner} • {study.location}</p>
                        <p className="text-sm text-blue-600 font-medium">{study.businessType}</p>
                      </div>
                      
                      <div className="mb-6">
                        <h4 className="font-semibold text-gray-900 mb-2">Challenge:</h4>
                        <p className="text-gray-600 text-sm mb-4">{study.challenge}</p>
                        
                        <h4 className="font-semibold text-gray-900 mb-2">Solution:</h4>
                        <p className="text-gray-600 text-sm mb-4">{study.solution}</p>
                      </div>
                      
                      <div className="bg-blue-50 p-4 rounded-lg mb-6">
                        <p className="text-gray-700 italic text-sm">"{study.testimonial}"</p>
                        <p className="text-gray-900 font-medium text-sm mt-2">— {study.owner}</p>
                      </div>
                      
                      <Button className="bg-blue-600 text-white hover:bg-blue-700">
                        Read Full Case Study
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                    
                    <div className="bg-gray-50 p-8">
                      <h4 className="font-semibold text-gray-900 mb-4">Results Achieved:</h4>
                      <div className="grid grid-cols-2 gap-4">
                        {study.results.map((result, idx) => (
                          <div key={idx} className="text-center p-4 bg-white rounded-lg">
                            <div className="text-2xl font-bold text-green-600 mb-1">{result.value}</div>
                            <div className="text-sm font-medium text-gray-900 mb-1">{result.metric}</div>
                            <div className="text-xs text-gray-600">{result.timeframe}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Industry Results */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Success Across All Landscaping Specialties
            </h2>
            <p className="text-lg text-gray-600">
              No matter your landscaping specialty, we have proven strategies that deliver results for your specific business type.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {industryResults.map((industry, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{industry.category}</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Companies Served:</span>
                      <span className="font-semibold text-gray-900">{industry.companies}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Average Growth:</span>
                      <span className="font-semibold text-green-600">{industry.avgGrowth}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Top Result:</span>
                      <span className="font-semibold text-blue-600">{industry.topResult}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Write Your Own Success Story?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Join 200+ landscaping business owners who have transformed their companies with GroundUP Digital. 
              Let's discuss how we can help you achieve similar results.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Phone className="w-5 h-5 mr-2" />
                Schedule Success Consultation
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Start Your Partnership
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
