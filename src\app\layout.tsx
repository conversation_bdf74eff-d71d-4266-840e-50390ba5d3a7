import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { FloatingCTA } from "@/components/ui/FloatingCTA";
import { FixedUIElements } from "@/components/ui/FixedUIElements";
import { ConversionOptimization } from "@/components/ui/ConversionOptimization";
import { SEOHead, OrganizationSchema, WebsiteSchema } from "@/components/seo/StructuredData";
import { EEATOptimization } from "@/components/seo/EEATOptimization";
import { TechnicalSEO } from "@/components/seo/TechnicalSEO";
import { generateSEOMetadata, generateLocalBusinessSchema, seoContent } from "@/lib/seo";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: 'swap',
});

export const metadata: Metadata = generateSEOMetadata(seoContent.homepage);

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const localBusinessSchema = generateLocalBusinessSchema();

  return (
    <html lang="en" className={inter.variable}>
      <head>
        <SEOHead structuredData={[localBusinessSchema]}>
          <OrganizationSchema />
          <WebsiteSchema />
        </SEOHead>
      </head>
      <body className="font-sans antialiased bg-white text-gray-900" suppressHydrationWarning={true}>
        <Header />
        <main className="min-h-screen">{children}</main>
        <Footer />
        <FloatingCTA />
        <FixedUIElements
          showSocialIcons={true}
          showScrollToTop={true}
          scrollToTopVariant="rocket"
          scrollToTopShowAfter={300}
        />
        <ConversionOptimization
          showTrustSignals={true}
          showUrgency={false}
          showSocialProof={false}
          showExitIntent={true}
        />
        <EEATOptimization
          pageType="homepage"
          title="GroundUP Digital - Pioneer in Local Service Marketing"
          description="The pioneer in digital marketing for landscaping, roofing, and pest control businesses."
        />
      </body>
    </html>
  );
}
