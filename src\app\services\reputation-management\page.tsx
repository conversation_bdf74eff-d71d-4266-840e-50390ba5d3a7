import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Reputation Management for Local Service Businesses | GroundUPDigital',
  description: 'Protect and enhance your online reputation with our comprehensive reputation management services for landscapers, roofers, and pest control companies.',
  keywords: 'reputation management, online reviews, Google reviews, local business reputation, review management',
  openGraph: {
    title: 'Reputation Management for Local Service Businesses | GroundUPDigital',
    description: 'Protect and enhance your online reputation with our comprehensive reputation management services for landscapers, roofers, and pest control companies.',
    type: 'website',
  },
};

export default function ReputationManagementPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-green-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Reputation Management That <span className="text-blue-600">Builds Trust</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Protect and enhance your online reputation with our comprehensive reputation management services. 
              Turn satisfied customers into powerful advocates for your business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Reputation Audit
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                See Success Stories
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Why Reputation Matters */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Why Online Reputation Matters
            </h2>
            <div className="grid md:grid-cols-3 gap-8 mb-16">
              {[
                { 
                  stat: '92%', 
                  description: 'of consumers read online reviews before hiring a local service provider',
                  icon: '👥'
                },
                { 
                  stat: '4.3+', 
                  description: 'star rating needed to be considered by most customers',
                  icon: '⭐'
                },
                { 
                  stat: '68%', 
                  description: 'more likely to hire a business with positive reviews',
                  icon: '📈'
                }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-4">{stat.stat}</div>
                  <p className="text-gray-600">{stat.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Comprehensive Reputation Management
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: 'Review Monitoring',
                  description: 'Track reviews across all major platforms in real-time with instant alerts.',
                  features: ['Google My Business', 'Facebook reviews', 'Yelp monitoring', 'Industry-specific sites'],
                  icon: '🔍'
                },
                {
                  title: 'Review Generation',
                  description: 'Systematic approach to encourage satisfied customers to leave positive reviews.',
                  features: ['Automated follow-ups', 'Review request campaigns', 'Incentive programs', 'Multi-channel outreach'],
                  icon: '⭐'
                },
                {
                  title: 'Response Management',
                  description: 'Professional responses to all reviews that maintain your brand reputation.',
                  features: ['Positive review responses', 'Negative review resolution', 'Brand voice consistency', '24/7 monitoring'],
                  icon: '💬'
                },
                {
                  title: 'Crisis Management',
                  description: 'Rapid response to reputation threats and negative publicity.',
                  features: ['Crisis response plans', 'Damage control strategies', 'Media monitoring', 'Recovery campaigns'],
                  icon: '🚨'
                },
                {
                  title: 'SEO Optimization',
                  description: 'Optimize your online presence to highlight positive content and reviews.',
                  features: ['Search result optimization', 'Positive content promotion', 'Review schema markup', 'Local SEO enhancement'],
                  icon: '🔍'
                },
                {
                  title: 'Competitor Analysis',
                  description: 'Monitor competitor reputations and identify opportunities for differentiation.',
                  features: ['Competitor review tracking', 'Market positioning', 'Opportunity identification', 'Benchmarking reports'],
                  icon: '📊'
                }
              ].map((service, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Review Platforms */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              We Monitor All Major Review Platforms
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { platform: 'Google My Business', importance: 'Most Important', icon: '🗺️' },
                { platform: 'Facebook', importance: 'High Impact', icon: '📘' },
                { platform: 'Yelp', importance: 'Local Discovery', icon: '🍽️' },
                { platform: 'Better Business Bureau', importance: 'Trust Factor', icon: '🏛️' },
                { platform: 'Angie\'s List', importance: 'Home Services', icon: '🏠' },
                { platform: 'HomeAdvisor', importance: 'Lead Generation', icon: '🔨' },
                { platform: 'Thumbtack', importance: 'Service Marketplace', icon: '👍' },
                { platform: 'Industry-Specific', importance: 'Niche Authority', icon: '🎯' }
              ].map((platform, index) => (
                <div key={index} className="text-center bg-white p-6 rounded-lg shadow-lg border border-gray-100">
                  <div className="text-4xl mb-4">{platform.icon}</div>
                  <h3 className="font-semibold text-gray-900 mb-2">{platform.platform}</h3>
                  <p className="text-sm text-blue-600">{platform.importance}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Our Reputation Management Process
            </h2>
            <div className="grid lg:grid-cols-4 gap-8">
              {[
                {
                  step: '1',
                  title: 'Audit & Assessment',
                  description: 'Comprehensive analysis of your current online reputation across all platforms.',
                  icon: '🔍'
                },
                {
                  step: '2',
                  title: 'Strategy Development',
                  description: 'Custom reputation management strategy tailored to your business goals.',
                  icon: '📋'
                },
                {
                  step: '3',
                  title: 'Implementation',
                  description: 'Execute review generation, monitoring, and response strategies.',
                  icon: '⚙️'
                },
                {
                  step: '4',
                  title: 'Monitor & Optimize',
                  description: 'Continuous monitoring and optimization for sustained reputation growth.',
                  icon: '📈'
                }
              ].map((process, index) => (
                <div key={index} className="text-center">
                  <div className="bg-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    {process.step}
                  </div>
                  <div className="text-4xl mb-4">{process.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{process.title}</h3>
                  <p className="text-gray-600">{process.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Reputation Management Results
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                { metric: '4.8★', label: 'Average Rating Improvement', icon: '⭐' },
                { metric: '300%', label: 'More Positive Reviews', icon: '👍' },
                { metric: '85%', label: 'Faster Response Time', icon: '⚡' },
                { metric: '250%', label: 'Increase in Trust Signals', icon: '🛡️' }
              ].map((result, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{result.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{result.metric}</div>
                  <div className="text-gray-600">{result.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Build a 5-Star Reputation?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Get a free reputation audit and discover how we can help you build trust and attract more customers.
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Get Your Free Reputation Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
