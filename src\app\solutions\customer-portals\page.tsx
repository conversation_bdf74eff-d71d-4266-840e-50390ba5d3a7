import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, Monitor, Users, Calendar, Camera, CreditCard, Star, CheckCircle, Phone, MessageSquare } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Customer Portal Development for Landscaping Businesses | GroundUP Digital',
  description: 'Branded client portals that enhance customer relationships for landscaping businesses. Project updates, maintenance schedules, billing access, and service requests.',
  keywords: 'landscaping customer portal, client portal landscaping, landscaping business portal, customer dashboard landscaping, client communication portal',
};

const portalFeatures = [
  {
    icon: Camera,
    title: 'Project Progress Tracking',
    description: 'Real-time project updates with photo documentation that keeps clients engaged and informed.',
    benefits: ['Live project timelines', 'Photo progress galleries', 'Milestone notifications', 'Completion certificates']
  },
  {
    icon: Calendar,
    title: 'Service Management Hub',
    description: 'Complete service scheduling and maintenance management for ongoing client relationships.',
    benefits: ['Maintenance scheduling', 'Service history tracking', 'Appointment booking', 'Seasonal reminders']
  },
  {
    icon: CreditCard,
    title: 'Billing & Payment Center',
    description: 'Streamlined billing access and payment processing that improves cash flow.',
    benefits: ['Invoice access', 'Online payments', 'Payment history', 'Automatic billing']
  }
];

const portalTypes = [
  {
    name: 'Premium Client Portal',
    description: 'Comprehensive portal for high-end residential landscaping clients',
    features: ['Project galleries & timelines', 'Maintenance calendar', 'Direct messaging', 'Invoice & payment access', 'Service request forms', 'Seasonal planning tools']
  },
  {
    name: 'Commercial Client Dashboard',
    description: 'Professional portal designed for commercial landscaping accounts',
    features: ['Multi-property management', 'Service level agreements', 'Compliance reporting', 'Budget tracking', 'Team communication', 'Performance analytics']
  },
  {
    name: 'Maintenance Client Hub',
    description: 'Specialized portal for ongoing maintenance and lawn care clients',
    features: ['Service schedules', 'Weather-based updates', 'Treatment notifications', 'Problem reporting', 'Seasonal packages', 'Renewal management']
  },
  {
    name: 'Design Client Studio',
    description: 'Interactive portal for landscape design and build clients',
    features: ['Design collaboration tools', 'Material selection', '3D visualization access', 'Change order management', 'Progress documentation', 'Final project showcase']
  }
];

const packages = [
  {
    name: "Portal Essentials",
    price: "$797",
    period: "/month",
    description: "Basic client portal functionality for small landscaping businesses",
    features: [
      "Branded client portal",
      "Project photo sharing",
      "Basic service scheduling",
      "Invoice access",
      "Client messaging",
      "Mobile-responsive design"
    ],
    popular: false
  },
  {
    name: "Premium Portal Suite",
    price: "$1,497",
    period: "/month", 
    description: "Advanced client portal for growing landscaping companies",
    features: [
      "Custom branded portal",
      "Advanced project tracking",
      "Automated notifications",
      "Payment processing",
      "Service request management",
      "Analytics & reporting"
    ],
    popular: true
  },
  {
    name: "Enterprise Portal Platform",
    price: "$2,997",
    period: "/month",
    description: "Complete portal ecosystem for large landscaping operations",
    features: [
      "Multi-portal management",
      "Advanced customization",
      "API integrations",
      "White-label solutions",
      "Advanced analytics",
      "Dedicated portal manager"
    ],
    popular: false
  }
];

const caseStudy = {
  company: "Luxury Landscapes Ltd",
  location: "Scottsdale, AZ",
  results: [
    { metric: "Client Satisfaction", improvement: "+92%" },
    { metric: "Payment Speed", improvement: "+65%" },
    { metric: "Service Requests", improvement: "+180%" },
    { metric: "Client Retention", improvement: "+45%" }
  ]
};

const clientBenefits = [
  { benefit: "24/7 Project Access", description: "Clients can view project progress anytime, anywhere" },
  { benefit: "Transparent Communication", description: "Direct messaging and real-time updates" },
  { benefit: "Convenient Payments", description: "Easy online payment processing and history" },
  { benefit: "Service Scheduling", description: "Self-service appointment booking and management" },
  { benefit: "Document Storage", description: "Access to contracts, warranties, and documentation" },
  { benefit: "Maintenance Tracking", description: "Complete service history and upcoming needs" }
];

export default function CustomerPortalsPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Monitor className="w-4 h-4" />
              <span className="text-sm font-semibold">Customer Portals for Landscaping Businesses</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Branded Client Portals That Enhance{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Customer Relationships
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Premium client portals that help landscaping business owners provide exceptional customer experiences, 
              improve communication, and build stronger long-term relationships.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Monitor className="w-5 h-5 mr-2" />
                See Portal Demo
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                View Portal Examples
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '92%', label: 'Client Satisfaction' },
                { number: '65%', label: 'Faster Payments' },
                { number: '45%', label: 'Client Retention' },
                { number: '70+', label: 'Portals Built' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Client Portal Solutions That Transform Customer Relationships
            </h2>
            <p className="text-lg text-gray-600">
              Our client portals are designed specifically for landscaping business owners who want to provide premium 
              customer experiences and build lasting relationships with their clients.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {portalFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Client Benefits */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Benefits That Keep Landscaping Clients Engaged
            </h2>
            <p className="text-lg text-gray-600">
              See how client portals enhance the customer experience and build stronger relationships for landscaping businesses.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-6">
            {clientBenefits.map((item, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="flex items-center mb-3">
                    <MessageSquare className="w-5 h-5 text-green-600 mr-2" />
                    <h3 className="text-lg font-bold text-gray-900">{item.benefit}</h3>
                  </div>
                  <p className="text-gray-600 text-sm">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Portal Types Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Specialized Portals for Every Landscaping Business Type
            </h2>
            <p className="text-lg text-gray-600">
              Custom client portals designed for different types of landscaping services and client relationships.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {portalTypes.map((portal, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{portal.name}</h3>
                  <p className="text-gray-600 mb-4">{portal.description}</p>
                  <ul className="space-y-2">
                    {portal.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Client Portal Success Story: Enhanced Relationships
              </h2>
              <p className="text-lg text-gray-600">
                See how {caseStudy.company} used our client portal to transform customer relationships and business operations
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A premium landscaping business in {caseStudy.location} wanted to differentiate through exceptional customer service. 
                      Our branded client portal transformed their customer relationships and operational efficiency.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Read Full Case Study
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Client Portal Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Professional client portal solutions designed specifically for landscaping business owners. 
              Choose the package that enhances your customer relationships and business operations.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* FAQ Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Frequently Asked Questions About Customer Portals for Landscaping
              </h2>
              <p className="text-lg text-gray-600">
                Common questions landscaping business owners have about customer portals and self-service platforms.
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "What can customers do in a landscaping business portal?",
                  answer: "Customers can view project progress photos, schedule services, make payments, access maintenance schedules, submit service requests, view invoices, track seasonal reminders, and communicate directly with your landscaping team."
                },
                {
                  question: "How do customer portals reduce landscaping business workload?",
                  answer: "Portals automate 60-70% of routine customer interactions: payment processing, appointment scheduling, basic inquiries, document access, and status updates. This frees your team to focus on actual landscaping work and complex customer needs."
                },
                {
                  question: "Will customers actually use a landscaping business portal?",
                  answer: "Yes! When designed well, 80%+ of customers actively use portals. The convenience of 24/7 access to their landscaping project information, easy payment options, and self-service scheduling drives high adoption rates."
                },
                {
                  question: "Can customer portals integrate with my existing landscaping software?",
                  answer: "Absolutely! We integrate portals with popular landscaping tools like QuickBooks, scheduling software, project management systems, and CRM platforms, ensuring seamless data flow and eliminating duplicate work."
                },
                {
                  question: "How do customer portals improve landscaping business retention?",
                  answer: "Portals increase customer engagement and satisfaction through transparency, convenience, and self-service options. Customers with portal access have 40-50% higher retention rates and spend 25% more on additional services."
                }
              ].map((faq, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{faq.question}</h3>
                  <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Enhance Your Landscaping Customer Relationships?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a free client portal consultation and discover how branded portals can improve customer satisfaction 
              and grow your landscaping business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Monitor className="w-5 h-5 mr-2" />
                Get Portal Consultation
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule Demo
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
