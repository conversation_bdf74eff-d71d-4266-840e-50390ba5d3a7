'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  Calendar, 
  User, 
  Clock, 
  Share2, 
  BookOpen,
  ChevronRight,
  Facebook,
  Twitter,
  Linkedin,
  ArrowRight,
  Tag
} from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

// Table of Contents data
const tableOfContents = [
  { id: 'introduction', title: 'Introduction', level: 2 },
  { id: 'optimize-google-business-profile', title: '1. Optimize Your Google Business Profile', level: 2 },
  { id: 'key-elements', title: 'Key Elements to Include', level: 3 },
  { id: 'target-location-keywords', title: '2. Target Location-Specific Keywords', level: 2 },
  { id: 'build-local-citations', title: '3. Build Local Citations', level: 2 },
  { id: 'encourage-reviews', title: '4. Encourage Customer Reviews', level: 2 },
  { id: 'create-location-content', title: '5. Create Location-Specific Content', level: 2 },
];

// Related articles
const relatedArticles = [
  {
    id: 2,
    title: 'How to Generate Emergency Roofing Leads with Google Ads',
    excerpt: 'Learn the exact Google Ads strategies that roofing contractors use to capture high-value emergency repair leads 24/7.',
    category: 'PPC',
    readTime: '6 min read',
    slug: 'roofing-google-ads-emergency-leads'
  },
  {
    id: 3,
    title: 'Building Trust Online: Reputation Management for Pest Control',
    excerpt: 'Why online reviews matter more than ever for pest control companies and how to build a 5-star reputation.',
    category: 'Reputation',
    readTime: '7 min read',
    slug: 'pest-control-reputation-management'
  },
  {
    id: 4,
    title: 'Website Design Trends for Local Service Businesses',
    excerpt: 'The latest web design trends that are converting visitors into customers for local service companies.',
    category: 'Web Design',
    readTime: '5 min read',
    slug: 'web-design-trends-local-services'
  }
];

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  author: string;
  authorBio: string;
  date: string;
  category: string;
  tags: string[];
  readTime: string;
  featured: boolean;
  image: string;
}

interface BlogDetailContentProps {
  blogPost: BlogPost;
}

export const BlogDetailContent: React.FC<BlogDetailContentProps> = ({ blogPost }) => {
  const [activeSection, setActiveSection] = useState('');

  // Handle scroll spy for table of contents
  useEffect(() => {
    const handleScroll = () => {
      const sections = tableOfContents.map(item => document.getElementById(item.id));
      const scrollPosition = window.scrollY + 100;

      for (let i = sections.length - 1; i >= 0; i--) {
        const section = sections[i];
        if (section && section.offsetTop <= scrollPosition) {
          setActiveSection(tableOfContents[i].id);
          break;
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <>
      {/* Breadcrumb */}
      <Section background="gray" padding="sm">
        <Container>
          <nav className="text-sm">
            <ol className="flex items-center space-x-2 text-gray-600">
              <li>
                <Link href="/" className="hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li><ChevronRight className="h-4 w-4" /></li>
              <li>
                <Link href="/blog" className="hover:text-blue-600 transition-colors">
                  Blog
                </Link>
              </li>
              <li><ChevronRight className="h-4 w-4" /></li>
              <li className="text-gray-900 font-medium truncate">
                {blogPost.title}
              </li>
            </ol>
          </nav>
        </Container>
      </Section>

      {/* Main Content */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-12 gap-8">
              {/* Table of Contents - Left Sidebar */}
              <div className="lg:col-span-3">
                <div className="sticky top-24">
                  <Card className="p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <BookOpen className="h-5 w-5 text-blue-600" />
                      <h3 className="font-semibold text-gray-900">Table of Contents</h3>
                    </div>
                    <nav className="space-y-2">
                      {tableOfContents.map((item) => (
                        <button
                          key={item.id}
                          onClick={() => scrollToSection(item.id)}
                          className={`block w-full text-left text-sm transition-colors duration-200 ${
                            item.level === 3 ? 'pl-4' : ''
                          } ${
                            activeSection === item.id
                              ? 'text-blue-600 font-medium'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                        >
                          {item.title}
                        </button>
                      ))}
                    </nav>
                  </Card>
                </div>
              </div>

              {/* Main Article Content */}
              <div className="lg:col-span-6">
                <article>
                  {/* Article Header */}
                  <header className="mb-8">
                    <div className="mb-4">
                      <Badge variant="primary" size="sm">
                        {blogPost.category}
                      </Badge>
                    </div>
                    
                    <h1 className="text-4xl font-bold text-gray-900 mb-4 leading-tight">
                      {blogPost.title}
                    </h1>
                    
                    <p className="text-xl text-gray-600 mb-6">
                      {blogPost.excerpt}
                    </p>
                    
                    {/* Article Meta */}
                    <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-6">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span>{blogPost.author}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(blogPost.date).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span>{blogPost.readTime}</span>
                      </div>
                    </div>
                    
                    {/* Social Share */}
                    <div className="flex items-center gap-4 pb-6 border-b border-gray-200">
                      <span className="text-sm font-medium text-gray-700">Share:</span>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm" className="p-2">
                          <Facebook className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" className="p-2">
                          <Twitter className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" className="p-2">
                          <Linkedin className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" className="p-2">
                          <Share2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </header>

                  {/* Featured Image */}
                  <div className="mb-8">
                    <div className="aspect-video bg-gradient-to-br from-blue-100 to-green-100 rounded-xl flex items-center justify-center">
                      <div className="text-center">
                        <BookOpen className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                        <span className="text-gray-600">Featured Article Image</span>
                      </div>
                    </div>
                  </div>

                  {/* Article Content */}
                  <div 
                    className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-blue-600 prose-strong:text-gray-900"
                    dangerouslySetInnerHTML={{ __html: blogPost.content }}
                  />

                  {/* Tags */}
                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <div className="flex items-center gap-2 mb-4">
                      <Tag className="h-4 w-4 text-gray-600" />
                      <span className="text-sm font-medium text-gray-700">Tags:</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {blogPost.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" size="sm">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Author Bio */}
                  <div className="mt-8 p-6 bg-gray-50 rounded-xl">
                    <div className="flex items-start gap-4">
                      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <User className="h-8 w-8 text-blue-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">About {blogPost.author}</h4>
                        <p className="text-gray-600 text-sm">{blogPost.authorBio}</p>
                      </div>
                    </div>
                  </div>
                </article>
              </div>

              {/* Right Sidebar - Related Articles */}
              <div className="lg:col-span-3">
                <div className="sticky top-24 space-y-6">
                  {/* Newsletter Signup */}
                  <Card className="p-6">
                    <h3 className="font-semibold text-gray-900 mb-4">Stay Updated</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Get the latest digital marketing tips delivered to your inbox.
                    </p>
                    <div className="space-y-3">
                      <input
                        type="email"
                        placeholder="Your email address"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none text-sm"
                      />
                      <Button variant="primary" size="sm" className="w-full">
                        Subscribe
                      </Button>
                    </div>
                  </Card>

                  {/* Related Articles */}
                  <Card className="p-6">
                    <h3 className="font-semibold text-gray-900 mb-4">Related Articles</h3>
                    <div className="space-y-4">
                      {relatedArticles.map((article) => (
                        <Link
                          key={article.id}
                          href={`/blog/${article.slug}`}
                          className="block group"
                        >
                          <div className="p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="secondary" size="xs">
                                {article.category}
                              </Badge>
                              <span className="text-xs text-gray-500">{article.readTime}</span>
                            </div>
                            <h4 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors text-sm mb-2 leading-tight">
                              {article.title}
                            </h4>
                            <p className="text-xs text-gray-600 line-clamp-2">
                              {article.excerpt}
                            </p>
                          </div>
                        </Link>
                      ))}
                    </div>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </Container>
      </Section>

      {/* Related Articles Section */}
      <Section background="gray" padding="lg">
        <Container>
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">You Might Also Like</h2>
            <p className="text-gray-600">More insights to help grow your business</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {relatedArticles.map((article, index) => (
              <motion.div
                key={article.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full overflow-hidden group cursor-pointer">
                  <div className="h-40 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                    <BookOpen className="h-8 w-8 text-gray-400" />
                  </div>
                  
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Badge variant="secondary" size="sm">
                        {article.category}
                      </Badge>
                      <span className="text-sm text-gray-500">{article.readTime}</span>
                    </div>
                    
                    <h3 className="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                      {article.title}
                    </h3>
                    
                    <p className="text-gray-600 text-sm">
                      {article.excerpt}
                    </p>
                    
                    <Link href={`/blog/${article.slug}`}>
                      <Button variant="ghost" size="sm" className="group-hover:translate-x-1 transition-transform duration-200">
                        Read More
                        <ArrowRight className="ml-1 h-4 w-4" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </Container>
      </Section>
    </>
  );
};
