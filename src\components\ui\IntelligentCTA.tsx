'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useHydration } from '@/hooks/useHydration';

export const IntelligentCTA: React.FC = () => {
  const [showStickyCTA, setShowStickyCTA] = useState(false);
  const isMounted = useHydration();

  // Intelligent sticky CTA detection
  useEffect(() => {
    if (!isMounted) return;

    let timeOnPage = 0;
    const startTime = Date.now();

    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollProgress = scrollTop / docHeight;
      timeOnPage = (Date.now() - startTime) / 1000;

      // Show sticky CTA based on user intent signals:
      // 1. Scrolled 50% of page
      // 2. Spent 20+ seconds on page
      // 3. Scrolled past 1000px (highly engaged user)
      if (scrollProgress > 0.5 || timeOnPage > 20 || scrollTop > 1000) {
        setShowStickyCTA(true);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isMounted]);

  return (
    <AnimatePresence>
      {showStickyCTA && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          className="fixed bottom-0 left-0 right-0 bg-gradient-to-r from-blue-600 to-green-600 text-white p-4 z-40 shadow-lg"
        >
          <div className="max-w-6xl mx-auto flex items-center justify-between">
            <div className="flex-1">
              <p className="font-bold text-lg">Ready to Transform Your Landscaping Business?</p>
              <p className="text-sm opacity-90">Join 200+ successful landscaping businesses</p>
            </div>
            
            <div className="flex items-center gap-3">
              <a
                href="/partnership"
                onClick={() => setShowStickyCTA(false)}
                className="bg-white text-blue-600 font-bold px-6 py-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                Get Free Audit
              </a>
              <a
                href="tel:+***********"
                onClick={() => setShowStickyCTA(false)}
                className="border border-white text-white font-bold px-6 py-2 rounded-lg hover:bg-white hover:text-blue-600 transition-colors"
              >
                Call Now
              </a>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
