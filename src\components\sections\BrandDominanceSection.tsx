'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { TreePine, Award, Target, Zap, Users, TrendingUp, Shield, CheckCircle } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';

export const BrandDominanceSection: React.FC = () => {
  const dominanceMetrics = [
    {
      icon: TreePine,
      number: '100%',
      label: 'Landscaping Focus',
      description: 'Exclusively serving landscaping business owners'
    },
    {
      icon: Users,
      number: '200+',
      label: 'Landscaping Businesses',
      description: 'Successful landscaping companies served'
    },
    {
      icon: TrendingUp,
      number: '$45M+',
      label: 'Revenue Generated',
      description: 'For our landscaping business partners'
    },
    {
      icon: Award,
      number: '247%',
      label: 'Average Lead Increase',
      description: 'For landscaping business owners'
    }
  ];

  const specializations = [
    {
      icon: Target,
      title: 'Hyper-Specialized Expertise',
      description: 'Deep understanding of landscaping industry challenges, seasonal patterns, and customer behavior.',
      benefits: ['Industry-specific strategies', 'Seasonal campaign optimization', 'Local market expertise']
    },
    {
      icon: Zap,
      title: 'Advanced Technology Stack',
      description: 'Cutting-edge solutions designed specifically for landscaping business operations.',
      benefits: ['AI-powered lead generation', 'AR/VR design visualization', 'Smart IoT integration']
    },
    {
      icon: Shield,
      title: 'Proven Track Record',
      description: 'Consistent results helping landscaping businesses dominate their local markets.',
      benefits: ['200+ successful partnerships', '$45M+ revenue generated', '89% client retention rate']
    }
  ];

  return (
    <Section background="white" padding="lg">
      <Container>
        {/* Header */}
        <div className="max-w-4xl mx-auto text-center mb-12 lg:mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center gap-2 bg-green-100 rounded-full px-4 py-2 mb-6"
          >
            <TreePine className="w-5 h-5 text-green-600" />
            <span className="text-sm font-semibold text-green-700">The Landscaping Industry's #1 Digital Partner</span>
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight"
          >
            Why Landscaping Business Owners Choose{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600">
              GroundUP Digital
            </span>
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-lg text-gray-600 leading-relaxed"
          >
            We're not just another marketing agency. We're the exclusive digital growth partner for landscaping professionals, 
            with deep industry expertise and proven results that speak for themselves.
          </motion.p>
        </div>

        {/* Dominance Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8 mb-16"
        >
          {dominanceMetrics.map((metric, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <metric.icon className="w-8 h-8 text-blue-600" />
              </div>
              <div className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">{metric.number}</div>
              <div className="text-lg font-semibold text-gray-800 mb-1">{metric.label}</div>
              <div className="text-sm text-gray-600">{metric.description}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Specializations */}
        <div className="grid md:grid-cols-3 gap-8">
          {specializations.map((spec, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-green-100 rounded-lg flex items-center justify-center mb-4">
                    <spec.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{spec.title}</h3>
                  <p className="text-gray-600 mb-4 leading-relaxed">{spec.description}</p>
                  <ul className="space-y-2">
                    {spec.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-700">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-12 lg:mt-16"
        >
          <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8 lg:p-12">
            <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
              Ready to Join 200+ Successful Landscaping Businesses?
            </h3>
            <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
              Discover why landscaping business owners across the country trust GroundUP Digital 
              as their exclusive digital growth partner.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.a
                href="/partnership"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r from-blue-600 to-green-600 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-green-700 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Start Your Partnership
                <TreePine className="w-5 h-5 ml-2" />
              </motion.a>
              <motion.a
                href="/why-groundup"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center justify-center px-8 py-3 bg-white text-gray-700 font-semibold rounded-lg border border-gray-300 hover:bg-gray-50 transition-all duration-200 shadow-md hover:shadow-lg"
              >
                Learn More About Us
                <Award className="w-5 h-5 ml-2" />
              </motion.a>
            </div>
          </div>
        </motion.div>
      </Container>
    </Section>
  );
};
