'use client';

import React from 'react';
import { FixedSocialIcons } from './FixedSocialIcons';
import { ScrollToTop } from './ScrollToTop';
import { ClientOnly } from './ClientOnly';

interface FixedUIElementsProps {
  showSocialIcons?: boolean;
  showScrollToTop?: boolean;
  scrollToTopVariant?: 'default' | 'rocket' | 'arrow';
  scrollToTopShowAfter?: number;
}

export const FixedUIElements: React.FC<FixedUIElementsProps> = ({
  showSocialIcons = true,
  showScrollToTop = true,
  scrollToTopVariant = 'default',
  scrollToTopShowAfter = 300
}) => {
  return (
    <ClientOnly>
      {/* Fixed Social Icons on the left */}
      {showSocialIcons && <FixedSocialIcons />}

      {/* Scroll to Top Button on the right */}
      {showScrollToTop && (
        <ScrollToTop
          variant={scrollToTopVariant}
          showAfter={scrollToTopShowAfter}
        />
      )}
    </ClientOnly>
  );
};
