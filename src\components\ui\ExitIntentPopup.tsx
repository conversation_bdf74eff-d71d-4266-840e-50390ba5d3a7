'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Calendar, Star, CheckCircle, ArrowRight, Gift } from 'lucide-react';
import Link from 'next/link';

export const ExitIntentPopup: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasShown, setHasShown] = useState(false);

  useEffect(() => {
    const handleMouseLeave = (e: MouseEvent) => {
      // Only trigger if mouse leaves from the top of the page and hasn't been shown yet
      if (e.clientY <= 0 && !hasShown) {
        setIsVisible(true);
        setHasShown(true);
      }
    };

    // Also show after 30 seconds if user hasn't left
    const timer = setTimeout(() => {
      if (!hasShown) {
        setIsVisible(true);
        setHasShown(true);
      }
    }, 30000);

    document.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave);
      clearTimeout(timer);
    };
  }, [hasShown]);

  const handleClose = () => {
    setIsVisible(false);
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={handleClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 50 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 relative overflow-hidden max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Background Pattern */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-100 to-green-100 rounded-full -translate-y-16 translate-x-16"></div>
            
            {/* Close Button */}
            <button
              onClick={handleClose}
              className="absolute top-3 xs:top-4 right-3 xs:right-4 text-gray-400 hover:text-gray-600 transition-colors z-10 touch-target"
            >
              <X className="w-6 h-6" />
            </button>

            <div className="p-4 xs:p-5 sm:p-6 relative">
              {/* Header */}
              <div className="text-center mb-4">
                <div className="inline-flex items-center gap-2 bg-blue-50 rounded-full px-3 py-1.5 mb-3">
                  <Gift className="w-4 h-4 text-blue-600" />
                  <span className="text-xs font-semibold text-blue-700">Exclusive Landscaping Business Audit</span>
                </div>

                <h2 className="text-lg xs:text-xl sm:text-2xl font-bold text-gray-900 mb-2">
                  Get Your <span className="text-blue-600">FREE $3,500</span> Marketing Audit
                </h2>

                <p className="text-sm text-gray-600">
                  Claim your complimentary landscaping business audit and discover
                  how to generate 247%+ more leads for your landscaping business.
                </p>
              </div>

              {/* Benefits */}
              <div className="space-y-2 mb-4">
                {[
                  'Comprehensive landscaping website analysis',
                  'Custom lead generation strategy for landscapers',
                  'Local market competitor analysis',
                  'No obligation or high-pressure sales'
                ].map((benefit, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    className="flex items-center gap-2"
                  >
                    <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                    <span className="text-sm text-gray-700">{benefit}</span>
                  </motion.div>
                ))}
              </div>

              {/* Social Proof */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-1 mb-1">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 text-yellow-500 fill-current" />
                      ))}
                      <span className="text-sm font-semibold text-gray-700 ml-2">4.9/5</span>
                    </div>
                    <p className="text-sm text-gray-600">500+ businesses served</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-blue-600">300%+</div>
                    <p className="text-sm text-gray-600">Average lead increase</p>
                  </div>
                </div>
              </div>

              {/* CTA Button */}
              <Link href="/contact">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full bg-gradient-to-r from-blue-600 to-green-600 text-white py-4 rounded-xl font-bold text-lg flex items-center justify-center gap-2 shadow-xl hover:shadow-2xl transition-all"
                  onClick={handleClose}
                >
                  <Calendar className="w-5 h-5" />
                  Claim My FREE $3,500 Audit
                  <ArrowRight className="w-5 h-5" />
                </motion.button>
              </Link>

              {/* Urgency */}
              <div className="text-center mt-4">
                <p className="text-sm text-gray-500">
                  <span className="font-semibold text-orange-600">Limited Time:</span> Only 3 spots available this month
                </p>
              </div>

              {/* No Thanks Link */}
              <div className="text-center mt-4">
                <button
                  onClick={handleClose}
                  className="text-sm text-gray-400 hover:text-gray-600 transition-colors underline"
                >
                  No thanks, I'll figure it out myself
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
