import React from 'react';
import Head from 'next/head';

interface EEATOptimizationProps {
  pageType: 'homepage' | 'service' | 'blog' | 'about' | 'contact';
  title: string;
  description: string;
  authorName?: string;
  authorBio?: string;
  publishDate?: string;
  lastModified?: string;
  expertise?: string[];
  certifications?: string[];
  awards?: string[];
  testimonialCount?: number;
  averageRating?: number;
  businessYears?: number;
  clientCount?: number;
  revenueGenerated?: string;
}

export const EEATOptimization: React.FC<EEATOptimizationProps> = ({
  pageType,
  title,
  description,
  authorName = "GroundUP Digital Team",
  authorBio = "Digital marketing experts specializing in landscaping, roofing, and pest control businesses",
  publishDate,
  lastModified,
  expertise = [
    "Digital Marketing for Local Service Businesses",
    "Landscaping Industry Marketing",
    "Roofing Contractor Marketing", 
    "Pest Control Marketing",
    "Local SEO Optimization",
    "Google Ads Management",
    "Conversion Rate Optimization"
  ],
  certifications = [
    "Google Ads Certified",
    "Google Analytics Certified", 
    "Facebook Blueprint Certified",
    "HubSpot Certified",
    "Bing Ads Certified"
  ],
  awards = [
    "Top Digital Marketing Agency 2024",
    "Best Local Service Marketing Company",
    "Excellence in Customer Service Award"
  ],
  testimonialCount = 365,
  averageRating = 4.9,
  businessYears = 4,
  clientCount = 365,
  revenueGenerated = "$78M+"
}) => {
  // E-E-A-T Structured Data
  const eeAtStructuredData = {
    "@context": "https://schema.org",
    "@graph": [
      {
        "@type": "Organization",
        "@id": "https://groundupdigital.com/#organization",
        "name": "GroundUP Digital",
        "url": "https://groundupdigital.com",
        "logo": {
          "@type": "ImageObject",
          "url": "https://groundupdigital.com/logo.png",
          "width": 300,
          "height": 100
        },
        "description": "The pioneer in digital marketing for landscaping, roofing, and pest control businesses. Enterprise-grade solutions at affordable prices.",
        "foundingDate": "2020",
        "numberOfEmployees": "25-50",
        "areaServed": {
          "@type": "Country",
          "name": "United States"
        },
        "expertise": expertise,
        "award": awards,
        "hasCredential": certifications.map(cert => ({
          "@type": "EducationalOccupationalCredential",
          "name": cert,
          "credentialCategory": "Professional Certification"
        })),
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": averageRating,
          "reviewCount": testimonialCount,
          "bestRating": 5,
          "worstRating": 1
        },
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": "******-123-4567",
          "contactType": "customer service",
          "availableLanguage": "English",
          "areaServed": "US"
        },
        "sameAs": [
          "https://facebook.com/groundupdigital",
          "https://linkedin.com/company/groundupdigital",
          "https://twitter.com/groundupdigital",
          "https://instagram.com/groundupdigital",
          "https://youtube.com/@groundupdigital"
        ]
      },
      {
        "@type": "WebSite",
        "@id": "https://groundupdigital.com/#website",
        "url": "https://groundupdigital.com",
        "name": "GroundUP Digital",
        "description": "The pioneer in digital marketing for landscaping, roofing, and pest control businesses.",
        "publisher": {
          "@id": "https://groundupdigital.com/#organization"
        },
        "inLanguage": "en-US",
        "potentialAction": {
          "@type": "SearchAction",
          "target": "https://groundupdigital.com/search?q={search_term_string}",
          "query-input": "required name=search_term_string"
        }
      },
      {
        "@type": "WebPage",
        "@id": `https://groundupdigital.com${typeof window !== 'undefined' ? window.location.pathname : ''}#webpage`,
        "url": `https://groundupdigital.com${typeof window !== 'undefined' ? window.location.pathname : ''}`,
        "name": title,
        "description": description,
        "isPartOf": {
          "@id": "https://groundupdigital.com/#website"
        },
        "about": {
          "@id": "https://groundupdigital.com/#organization"
        },
        "datePublished": publishDate || "2024-01-01",
        "dateModified": lastModified || new Date().toISOString(),
        "author": {
          "@type": "Organization",
          "@id": "https://groundupdigital.com/#organization"
        },
        "publisher": {
          "@id": "https://groundupdigital.com/#organization"
        },
        "inLanguage": "en-US",
        "mainEntity": {
          "@id": "https://groundupdigital.com/#organization"
        }
      }
    ]
  };

  // Professional Author Schema
  const authorSchema = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": authorName,
    "description": authorBio,
    "worksFor": {
      "@type": "Organization",
      "name": "GroundUP Digital",
      "url": "https://groundupdigital.com"
    },
    "hasOccupation": {
      "@type": "Occupation",
      "name": "Digital Marketing Specialist",
      "occupationLocation": {
        "@type": "Country",
        "name": "United States"
      },
      "skills": expertise
    },
    "hasCredential": certifications.map(cert => ({
      "@type": "EducationalOccupationalCredential",
      "name": cert
    }))
  };

  // Trust Signals Schema
  const trustSignalsSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Digital Marketing Services",
    "provider": {
      "@id": "https://groundupdigital.com/#organization"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": averageRating,
      "reviewCount": testimonialCount,
      "bestRating": 5
    },
    "offers": {
      "@type": "Offer",
      "availability": "https://schema.org/InStock",
      "priceRange": "$1,997 - $9,997",
      "priceCurrency": "USD"
    }
  };

  return (
    <Head>
      {/* E-E-A-T Meta Tags */}
      <meta name="author" content={authorName} />
      <meta name="expertise" content={expertise.join(', ')} />
      <meta name="experience" content={`${businessYears} years serving ${clientCount}+ clients`} />
      <meta name="authoritativeness" content={`${revenueGenerated} revenue generated, ${averageRating}/5 rating from ${testimonialCount} reviews`} />
      <meta name="trustworthiness" content={`${certifications.join(', ')}, ${awards.join(', ')}`} />
      
      {/* Content Quality Signals */}
      <meta name="content-type" content={pageType} />
      <meta name="content-freshness" content={lastModified || new Date().toISOString()} />
      <meta name="content-depth" content="comprehensive" />
      <meta name="content-accuracy" content="verified" />
      
      {/* Business Credentials */}
      <meta name="business-years" content={businessYears.toString()} />
      <meta name="client-count" content={clientCount.toString()} />
      <meta name="revenue-generated" content={revenueGenerated} />
      <meta name="certifications" content={certifications.join(', ')} />
      
      {/* Social Proof */}
      <meta name="testimonial-count" content={testimonialCount.toString()} />
      <meta name="average-rating" content={averageRating.toString()} />
      <meta name="awards" content={awards.join(', ')} />
      
      {/* Industry Authority */}
      <meta name="industry-focus" content="Landscaping, Roofing, Pest Control" />
      <meta name="specialization" content="Local Service Business Marketing" />
      <meta name="market-position" content="Pioneer and Industry Leader" />
      
      {/* Content Verification */}
      <meta name="fact-checked" content="true" />
      <meta name="expert-reviewed" content="true" />
      <meta name="data-sources" content="Internal analytics, client results, industry reports" />
      
      {/* User Intent Optimization */}
      <meta name="search-intent" content="commercial, informational" />
      <meta name="user-journey-stage" content="awareness, consideration, decision" />
      <meta name="conversion-goal" content="lead generation, consultation booking" />
      
      {/* Structured Data for E-E-A-T */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(eeAtStructuredData)
        }}
      />
      
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(authorSchema)
        }}
      />
      
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(trustSignalsSchema)
        }}
      />
      
      {/* Core Web Vitals Optimization */}
      <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* Performance Hints */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
      <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      
      {/* Security Headers */}
      <meta httpEquiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:;" />
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
      
      {/* Mobile Optimization */}
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="format-detection" content="telephone=yes" />
      
      {/* Accessibility */}
      <meta name="color-scheme" content="light" />
      <meta name="theme-color" content="#2563eb" />
    </Head>
  );
};
