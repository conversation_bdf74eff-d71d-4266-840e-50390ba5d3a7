import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'CRM Solutions for Roofing Companies | Streamline Operations | GroundUPDigital',
  description: 'Streamline your roofing business with custom CRM solutions. Manage customers, track projects, handle insurance claims, and automate operations for maximum efficiency.',
  keywords: 'roofing CRM, roofing software, roof repair CRM, roofing contractor software, insurance claim management',
  openGraph: {
    title: 'CRM Solutions for Roofing Companies | Streamline Operations | GroundUPDigital',
    description: 'Streamline your roofing business with custom CRM solutions. Manage customers, track projects, handle insurance claims, and automate operations for maximum efficiency.',
    type: 'website',
  },
};

export default function RoofingCRMPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              CRM Solutions for <span className="text-blue-600">Roofing Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Streamline your roofing business operations with custom CRM solutions designed for emergency repairs, 
              insurance claims, and project management. Manage customers, track jobs, and automate workflows efficiently.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Free CRM Demo
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                View CRM Features
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* CRM Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Transform Your Roofing Business Operations
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '50%', label: 'Faster Emergency Response', icon: '⚡' },
                { metric: '75%', label: 'Improvement in Project Tracking', icon: '📊' },
                { metric: '90%', label: 'Better Insurance Claim Management', icon: '📋' },
                { metric: '400%', label: 'ROI Within First Year', icon: '💰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Core CRM Features */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Comprehensive CRM Features for Roofing Companies
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Emergency Call Management',
                  description: 'Prioritize and dispatch emergency roofing calls with intelligent routing.',
                  capabilities: ['Emergency call prioritization', 'Automated dispatch', 'Response time tracking', 'Customer notifications', 'Crew availability'],
                  icon: '🚨'
                },
                {
                  feature: 'Insurance Claim Tracking',
                  description: 'Manage insurance claims from initial assessment to final payment.',
                  capabilities: ['Claim documentation', 'Adjuster coordination', 'Photo management', 'Progress tracking', 'Payment processing'],
                  icon: '📋'
                },
                {
                  feature: 'Project Management',
                  description: 'Track roofing projects from estimate to completion with milestone tracking.',
                  capabilities: ['Project timelines', 'Material tracking', 'Crew scheduling', 'Progress photos', 'Client updates'],
                  icon: '🏗️'
                },
                {
                  feature: 'Customer Communication',
                  description: 'Automated communication for estimates, updates, and follow-ups.',
                  capabilities: ['Automated messaging', 'Appointment reminders', 'Progress updates', 'Review requests', 'Emergency notifications'],
                  icon: '💬'
                },
                {
                  feature: 'Estimating & Invoicing',
                  description: 'Professional estimates and automated invoicing with payment processing.',
                  capabilities: ['Digital estimates', 'Material calculations', 'Labor tracking', 'Automated invoicing', 'Payment processing'],
                  icon: '💳'
                },
                {
                  feature: 'Weather Integration',
                  description: 'Weather-based scheduling and emergency response automation.',
                  capabilities: ['Weather alerts', 'Schedule adjustments', 'Emergency preparation', 'Storm tracking', 'Crew notifications'],
                  icon: '🌤️'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.feature}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.capabilities.map((capability, capIndex) => (
                      <li key={capIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {capability}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Emergency Response Workflow */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Emergency Response Workflow
              </h2>
              <p className="text-xl text-blue-100">
                Streamlined emergency response process for urgent roofing situations.
              </p>
            </div>
            <div className="grid md:grid-cols-5 gap-8">
              {[
                { step: '1', title: 'Emergency Call', description: 'Customer calls with urgent roofing issue', time: '0 min' },
                { step: '2', title: 'Triage & Prioritize', description: 'System categorizes urgency and assigns priority', time: '2 min' },
                { step: '3', title: 'Crew Dispatch', description: 'Nearest available crew automatically notified', time: '5 min' },
                { step: '4', title: 'Customer Update', description: 'Automated ETA and crew info sent to customer', time: '7 min' },
                { step: '5', title: 'Service Delivery', description: 'Crew arrives and provides emergency service', time: '30-60 min' }
              ].map((phase, index) => (
                <div key={index} className="text-center text-white">
                  <div className="bg-white text-blue-600 w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                    {phase.step}
                  </div>
                  <h3 className="text-lg font-semibold mb-2">{phase.title}</h3>
                  <p className="text-blue-100 mb-2 text-sm">{phase.description}</p>
                  <span className="text-xs text-blue-200 font-semibold">{phase.time}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Insurance Claim Management */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Insurance Claim Management System
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  stage: 'Initial Assessment',
                  tasks: ['Damage documentation', 'Photo collection', 'Preliminary estimate', 'Insurance notification'],
                  automation: 'Automated damage assessment forms and photo organization',
                  icon: '🔍'
                },
                {
                  stage: 'Adjuster Coordination',
                  tasks: ['Meeting scheduling', 'Documentation sharing', 'Scope agreement', 'Estimate approval'],
                  automation: 'Automated adjuster communication and document sharing',
                  icon: '🤝'
                },
                {
                  stage: 'Project Execution',
                  tasks: ['Material ordering', 'Crew scheduling', 'Progress tracking', 'Quality control'],
                  automation: 'Automated progress updates and milestone tracking',
                  icon: '🔨'
                },
                {
                  stage: 'Final Settlement',
                  tasks: ['Final inspection', 'Documentation submission', 'Payment processing', 'Warranty setup'],
                  automation: 'Automated final documentation and payment tracking',
                  icon: '✅'
                }
              ].map((stage, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4 text-center">{stage.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">{stage.stage}</h3>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-700 mb-2">Key Tasks:</h4>
                    <ul className="space-y-1">
                      {stage.tasks.map((task, taskIndex) => (
                        <li key={taskIndex} className="text-sm text-gray-600">
                          • {task}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-3">
                    <h5 className="font-semibold text-blue-800 text-sm mb-1">Automation:</h5>
                    <p className="text-blue-700 text-xs">{stage.automation}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Mobile CRM Features */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Mobile CRM for Roofing Crews
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Field Team Capabilities:</h3>
                <ul className="space-y-4">
                  {[
                    'Real-time job updates and status changes',
                    'Photo documentation with automatic organization',
                    'Digital signatures for work completion',
                    'Material usage tracking and inventory updates',
                    'Time tracking with GPS verification',
                    'Customer communication and notifications',
                    'Emergency job prioritization and routing',
                    'Offline access for areas with poor connectivity'
                  ].map((capability, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{capability}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Mobile App Benefits:</h3>
                <div className="space-y-6">
                  {[
                    { metric: '60%', description: 'Faster job completion documentation', icon: '📱' },
                    { metric: '85%', description: 'Improvement in crew efficiency', icon: '⚡' },
                    { metric: '95%', description: 'Reduction in paperwork errors', icon: '✅' },
                    { metric: '40%', description: 'Better customer communication', icon: '💬' }
                  ].map((benefit, index) => (
                    <div key={index} className="flex items-center bg-white p-4 rounded-lg shadow-lg">
                      <div className="text-3xl mr-4">{benefit.icon}</div>
                      <div>
                        <div className="text-2xl font-bold text-blue-600">{benefit.metric}</div>
                        <div className="text-gray-600">{benefit.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Integration Capabilities */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seamless Integrations for Roofing Businesses
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Essential Integrations:</h3>
                <div className="grid grid-cols-2 gap-4">
                  {[
                    { name: 'QuickBooks', category: 'Accounting', icon: '💼' },
                    { name: 'Weather APIs', category: 'Weather Data', icon: '🌤️' },
                    { name: 'Google Calendar', category: 'Scheduling', icon: '📅' },
                    { name: 'Stripe/PayPal', category: 'Payments', icon: '💳' },
                    { name: 'Insurance Portals', category: 'Claims', icon: '📋' },
                    { name: 'Material Suppliers', category: 'Inventory', icon: '📦' }
                  ].map((integration, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg shadow-lg text-center">
                      <div className="text-2xl mb-2">{integration.icon}</div>
                      <h4 className="font-semibold text-gray-900">{integration.name}</h4>
                      <p className="text-sm text-gray-600">{integration.category}</p>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Integration Benefits:</h3>
                <ul className="space-y-4">
                  {[
                    'Eliminate double data entry across systems',
                    'Sync financial data with accounting software',
                    'Automate weather-based scheduling adjustments',
                    'Process insurance payments directly within CRM',
                    'Real-time material pricing and availability',
                    'Automated compliance and documentation'
                  ].map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{benefit}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-8 bg-blue-50 rounded-lg p-6">
                  <h4 className="font-semibold text-blue-800 mb-2">Custom Integrations Available</h4>
                  <p className="text-blue-700 text-sm">
                    Need integration with specific roofing software or insurance systems? 
                    We can build custom integrations to connect your CRM with any business system.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Roofing CRM Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Storm Guard Roofing: 400% Growth with Custom CRM
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Storm Guard Roofing was overwhelmed during storm season with manual processes and poor communication. 
                    Our custom CRM solution streamlined their emergency response and insurance claim management.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">400%</div>
                      <div className="text-sm text-gray-600">Business Growth</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">15min</div>
                      <div className="text-sm text-gray-600">Emergency Response Time</div>
                    </div>
                  </div>
                  <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">CRM Implementation Results:</h4>
                  <ul className="space-y-3">
                    {[
                      '50% faster emergency response times',
                      '90% improvement in insurance claim processing',
                      '75% reduction in administrative overhead',
                      '400% increase in annual revenue',
                      '95% customer satisfaction rating',
                      '60% improvement in crew productivity'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Streamline Your Roofing Operations?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Get a free CRM consultation and discover how we can help you manage emergency calls, 
              track insurance claims, and grow your roofing business efficiently.
            </p>
            <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Get Your Free Roofing CRM Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
