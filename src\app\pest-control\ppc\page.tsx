import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'PPC Advertising for Pest Control Companies | Emergency Leads | GroundUPDigital',
  description: 'Drive immediate leads with targeted PPC campaigns for pest control companies. Get more customers for emergency pest removal, termite control, and seasonal treatments.',
  keywords: 'pest control PPC, exterminator advertising, pest management ads, Google Ads for pest control, pest control lead generation',
  openGraph: {
    title: 'PPC Advertising for Pest Control Companies | Emergency Leads | GroundUPDigital',
    description: 'Drive immediate leads with targeted PPC campaigns for pest control companies. Get more customers for emergency pest removal, termite control, and seasonal treatments.',
    type: 'website',
  },
};

export default function PestControlPPCPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-yellow-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              PPC Advertising for <span className="text-green-600">Pest Control Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Generate immediate leads and emergency calls with targeted PPC campaigns for pest control companies. 
              Get more customers for pest removal, termite control, and seasonal treatments with proven Google Ads strategies.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Get Free PPC Audit
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View Campaign Results
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Pest Control PPC Results That Drive Emergency Calls
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '$3.80', label: 'Average Cost Per Lead', icon: '💰' },
                { metric: '750%', label: 'Return on Ad Spend', icon: '📈' },
                { metric: '28%', label: 'Average Conversion Rate', icon: '🎯' },
                { metric: '12min', label: 'Average Response Time', icon: '⏰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-green-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Emergency PPC Strategy */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Emergency Pest Control PPC Strategy
              </h2>
              <p className="text-xl text-green-100">
                Capture high-intent emergency pest control leads when customers need help most.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  strategy: 'Emergency Keywords',
                  description: 'Target urgent pest control searches with high-intent keywords and immediate response messaging.',
                  keywords: ['emergency pest control', 'bed bug removal', '24/7 exterminator', 'wasp nest removal'],
                  icon: '🚨'
                },
                {
                  strategy: 'Seasonal Campaigns',
                  description: 'Automatically activate campaigns based on seasonal pest activity and weather patterns.',
                  keywords: ['ant control spring', 'mosquito treatment summer', 'rodent control winter', 'termite inspection'],
                  icon: '🍂'
                },
                {
                  strategy: 'Local Targeting',
                  description: 'Hyper-local targeting for immediate service area coverage during pest emergencies.',
                  keywords: ['pest control near me', 'local exterminator', 'pest removal [city]', 'bug control [zip]'],
                  icon: '📍'
                }
              ].map((strategy, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{strategy.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{strategy.strategy}</h3>
                  <p className="text-green-100 mb-4">{strategy.description}</p>
                  <div className="bg-green-700 rounded-lg p-4">
                    <h4 className="font-semibold mb-2">Key Keywords:</h4>
                    <ul className="space-y-1">
                      {strategy.keywords.map((keyword, keywordIndex) => (
                        <li key={keywordIndex} className="text-sm text-green-100">
                          "{keyword}"
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Campaign Types */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Pest Control PPC Campaign Types
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  campaign: 'Emergency Pest Removal',
                  description: 'Target urgent pest removal needs with immediate response messaging.',
                  keywords: ['emergency pest control', 'bed bug removal', 'wasp removal', 'ant infestation'],
                  bidStrategy: 'High bids for immediate visibility',
                  icon: '🚨'
                },
                {
                  campaign: 'Termite Control',
                  description: 'Specialized campaigns for termite inspection and treatment services.',
                  keywords: ['termite inspection', 'termite treatment', 'termite damage', 'wood destroying insects'],
                  bidStrategy: 'High-value termite targeting',
                  icon: '🐛'
                },
                {
                  campaign: 'Seasonal Pest Control',
                  description: 'Weather and season-triggered campaigns for specific pest problems.',
                  keywords: ['mosquito control', 'tick treatment', 'spider control', 'seasonal pest prevention'],
                  bidStrategy: 'Seasonal bid adjustments',
                  icon: '🍂'
                },
                {
                  campaign: 'Commercial Pest Control',
                  description: 'Target commercial properties and business owners.',
                  keywords: ['commercial pest control', 'restaurant pest control', 'office pest management', 'warehouse pest control'],
                  bidStrategy: 'High-value commercial targeting',
                  icon: '🏢'
                },
                {
                  campaign: 'Preventive Treatments',
                  description: 'Target proactive customers interested in pest prevention.',
                  keywords: ['pest prevention', 'quarterly pest control', 'pest maintenance', 'home pest protection'],
                  bidStrategy: 'Lower cost, higher volume',
                  icon: '🛡️'
                },
                {
                  campaign: 'Specialty Services',
                  description: 'Campaigns for specialized pest control services.',
                  keywords: ['wildlife removal', 'bird control', 'rodent exclusion', 'fumigation services'],
                  bidStrategy: 'Specialized service focus',
                  icon: '🦎'
                }
              ].map((campaign, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{campaign.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{campaign.campaign}</h3>
                  <p className="text-gray-600 mb-4">{campaign.description}</p>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-700 mb-2">Target Keywords:</h4>
                    <ul className="space-y-1">
                      {campaign.keywords.map((keyword, keywordIndex) => (
                        <li key={keywordIndex} className="text-sm text-gray-600">
                          "{keyword}"
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="bg-green-50 rounded-lg p-3">
                    <h5 className="font-semibold text-green-800 text-sm mb-1">Bid Strategy:</h5>
                    <p className="text-green-700 text-xs">{campaign.bidStrategy}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Seasonal Automation */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seasonal Campaign Automation
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  season: 'Spring',
                  automation: 'Activate ant and termite campaigns, increase bids for spring pests',
                  keywords: ['ant control', 'termite swarms', 'spring pest prevention'],
                  response: 'March activation',
                  icon: '🌸'
                },
                {
                  season: 'Summer',
                  automation: 'Launch mosquito and wasp campaigns, outdoor pest focus',
                  keywords: ['mosquito control', 'wasp removal', 'tick treatment'],
                  response: 'June activation',
                  icon: '☀️'
                },
                {
                  season: 'Fall',
                  automation: 'Focus on rodent prevention and spider control campaigns',
                  keywords: ['rodent control', 'spider treatment', 'winter pest prevention'],
                  response: 'September activation',
                  icon: '🍂'
                },
                {
                  season: 'Winter',
                  automation: 'Emphasize indoor pest control and rodent exclusion',
                  keywords: ['indoor pest control', 'rodent exclusion', 'winter pest problems'],
                  response: 'December activation',
                  icon: '❄️'
                }
              ].map((season, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                  <div className="text-4xl mb-4">{season.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{season.season}</h3>
                  <p className="text-gray-600 mb-4">{season.automation}</p>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-700 mb-2">Focus Keywords:</h4>
                    <ul className="space-y-1">
                      {season.keywords.map((keyword, keywordIndex) => (
                        <li key={keywordIndex} className="text-sm text-gray-600">
                          "{keyword}"
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="bg-green-50 rounded-lg p-3">
                    <span className="text-sm font-semibold text-green-800">Activation: </span>
                    <span className="text-sm text-green-700">{season.response}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Landing Page Optimization */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              High-Converting Landing Pages for Pest Control
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Emergency Landing Page Elements:</h3>
                <ul className="space-y-4">
                  {[
                    'Prominent emergency phone number with click-to-call',
                    'Clear "24/7 Emergency Service" messaging',
                    'Trust signals: licenses, certifications, EPA compliance',
                    'Before/after photos of pest treatments',
                    'Customer testimonials with pest problems solved',
                    'Fast response time guarantees',
                    'Service area map and coverage information',
                    'Emergency contact form with immediate response'
                  ].map((element, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{element}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Conversion Optimization Results:</h3>
                <div className="space-y-6">
                  {[
                    { metric: '52%', description: 'Higher conversion rate with emergency-focused pages', icon: '📈' },
                    { metric: '85%', description: 'More phone calls from mobile users', icon: '📞' },
                    { metric: '70%', description: 'Increase in emergency service requests', icon: '🚨' },
                    { metric: '40%', description: 'Lower cost per emergency lead', icon: '💰' }
                  ].map((result, index) => (
                    <div key={index} className="flex items-center bg-white p-4 rounded-lg shadow-lg">
                      <div className="text-3xl mr-4">{result.icon}</div>
                      <div>
                        <div className="text-2xl font-bold text-green-600">{result.metric}</div>
                        <div className="text-gray-600">{result.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pest-Specific Targeting */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Pest-Specific Campaign Targeting
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  targeting: 'High-Value Pests',
                  description: 'Target expensive pest problems with higher bid strategies.',
                  pests: ['Termites', 'Bed bugs', 'Carpenter ants', 'Rodent infestations'],
                  strategy: 'Premium pricing, immediate response'
                },
                {
                  targeting: 'Seasonal Pests',
                  description: 'Time-sensitive campaigns for seasonal pest activity.',
                  pests: ['Mosquitoes', 'Wasps', 'Ticks', 'Stink bugs'],
                  strategy: 'Weather-triggered, seasonal optimization'
                },
                {
                  targeting: 'Emergency Pests',
                  description: 'Urgent response campaigns for immediate pest threats.',
                  pests: ['Wasp nests', 'Bee swarms', 'Snake removal', 'Bat exclusion'],
                  strategy: '24/7 availability, rapid response'
                }
              ].map((target, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{target.targeting}</h3>
                  <p className="text-gray-600 mb-6">{target.description}</p>
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-700 mb-3">Target Pests:</h4>
                    <ul className="space-y-2">
                      {target.pests.map((pest, pestIndex) => (
                        <li key={pestIndex} className="flex items-center text-gray-600">
                          <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                          {pest}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4">
                    <h5 className="font-semibold text-green-800 mb-1">Strategy:</h5>
                    <p className="text-green-700 text-sm">{target.strategy}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Pest Control PPC Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    BugBusters Pro: 850% ROI with Emergency PPC Campaigns
                  </h3>
                  <p className="text-gray-600 mb-6">
                    BugBusters Pro was missing emergency calls during peak pest season. Our emergency-focused PPC campaigns 
                    and pest-specific landing pages transformed their lead generation.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">850%</div>
                      <div className="text-sm text-gray-600">Return on Ad Spend</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">$3.20</div>
                      <div className="text-sm text-gray-600">Cost Per Emergency Lead</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Campaign Results:</h4>
                  <ul className="space-y-3">
                    {[
                      '300+ emergency calls per month',
                      '28% average conversion rate',
                      '$180,000 additional revenue in 6 months',
                      '75% reduction in cost per acquisition',
                      '4.9-star average customer rating',
                      '60% increase in recurring service contracts'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Generate Immediate Pest Control Leads?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Get a free PPC audit and discover how we can drive qualified emergency leads to your pest control business starting today.
            </p>
            <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
              Get Your Free Pest Control PPC Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
