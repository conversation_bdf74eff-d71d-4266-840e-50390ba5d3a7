'use client';

import React from 'react';
import Link from 'next/link';
import { 
  MapPin, 
  Phone, 
  Mail, 
  Facebook, 
  Twitter, 
  Linkedin, 
  Instagram,
  ArrowRight,
  Award,
  Users,
  TrendingUp
} from 'lucide-react';

export function Footer() {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    solutions: [
      { name: 'Hyper-Local SEO', href: '/solutions/seo' },
      { name: 'Conversion-Optimized Websites', href: '/solutions/web-design' },
      { name: 'AI-Powered Lead Generation', href: '/solutions/ai-lead-generation' },
      { name: 'AR/VR Design Visualization', href: '/solutions/ar-vr-visualization' },
      { name: 'Precision Paid Advertising', href: '/solutions/ppc' },
      { name: 'Smart Landscaping & IoT', href: '/solutions/iot-integration' },
      { name: 'Strategic Content Marketing', href: '/solutions/content-marketing' },
      { name: 'CRM & Sales Enablement', href: '/solutions/crm-sales' },
      { name: 'Market Intelligence', href: '/solutions/market-intelligence' },
    ],
    company: [
      { name: 'Why GroundUP', href: '/why-groundup' },
      { name: 'Client Success', href: '/client-success' },
      { name: 'Resources', href: '/resources' },
      { name: 'Partnership', href: '/partnership' },
      { name: 'Privacy Policy', href: '/privacy-policy' },
      { name: 'Terms of Service', href: '/terms-of-service' },
    ],
  };

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: '#' },
    { name: 'Twitter', icon: Twitter, href: '#' },
    { name: 'LinkedIn', icon: Linkedin, href: '#' },
    { name: 'Instagram', icon: Instagram, href: '#' },
  ];

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="container section-padding-md">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 xs:gap-8">

          {/* Company Info */}
          <div className="sm:col-span-2 lg:col-span-1">
            <div className="mb-6">
              <h3 className="text-xl xs:text-2xl font-bold text-white mb-2">GroundUPDigital</h3>
              <p className="text-sm xs:text-base text-gray-300 mb-4 leading-relaxed">
                Premium digital ecosystem exclusively for landscaping professionals.
                We architect comprehensive growth solutions from the ground up.
              </p>
            </div>
            
            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin className="h-4 w-4 xs:h-5 xs:w-5 text-blue-400 flex-shrink-0 mt-0.5" />
                <span className="text-xs xs:text-sm text-gray-300 leading-relaxed">
                  <span className="hidden sm:inline">123 Digital Marketing Blvd, Marketing City, MC 12345</span>
                  <span className="sm:hidden">Marketing City, MC 12345</span>
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 xs:h-5 xs:w-5 text-blue-400 flex-shrink-0" />
                <a href="tel:+15551234567" className="text-xs xs:text-sm text-gray-300 hover:text-white transition-colors touch-target">
                  (*************
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 xs:h-5 xs:w-5 text-blue-400 flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="text-xs xs:text-sm text-gray-300 hover:text-white transition-colors touch-target break-all">
                  <EMAIL>
                </a>
              </div>
            </div>

            {/* Social Media */}
            <div className="mt-6">
              <h4 className="text-base xs:text-lg font-semibold text-white mb-3">Follow Us</h4>
              <div className="flex space-x-3 xs:space-x-4">
                {socialLinks.map((social) => {
                  const Icon = social.icon;
                  return (
                    <a
                      key={social.name}
                      href={social.href}
                      className="bg-gray-800 p-2 xs:p-2.5 rounded-lg hover:bg-blue-600 transition-colors touch-target"
                      aria-label={`Follow us on ${social.name}`}
                    >
                      <Icon className="h-4 w-4 xs:h-5 xs:w-5" />
                    </a>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Solutions */}
          <div className="sm:col-span-1">
            <h4 className="text-base xs:text-lg font-semibold text-white mb-4 xs:mb-6">Our Solutions</h4>
            <ul className="space-y-2 xs:space-y-3">
              {footerLinks.solutions.slice(0, 8).map((service, index) => (
                <li key={index}>
                  <Link
                    href={service.href}
                    className="text-xs xs:text-sm text-gray-300 hover:text-white transition-colors flex items-center group touch-target py-1"
                  >
                    <ArrowRight className="h-2 w-2 xs:h-3 xs:w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0" />
                    <span className="truncate">{service.name}</span>
                  </Link>
                </li>
              ))}
              {footerLinks.solutions.length > 8 && (
                <li>
                  <Link
                    href="/solutions"
                    className="text-xs xs:text-sm text-blue-400 hover:text-blue-300 transition-colors flex items-center group touch-target py-1 font-medium"
                  >
                    <ArrowRight className="h-2 w-2 xs:h-3 xs:w-3 mr-2 group-hover:translate-x-1 transition-transform flex-shrink-0" />
                    View All Solutions
                  </Link>
                </li>
              )}
            </ul>
          </div>

          {/* Landscaping Focus */}
          <div className="sm:col-span-1 lg:col-span-1">
            <h4 className="text-base xs:text-lg font-semibold text-white mb-4 xs:mb-6">🌱 Landscaping Excellence</h4>
            <div className="space-y-3 xs:space-y-4">
              <p className="text-xs xs:text-sm text-gray-300 leading-relaxed">
                Exclusively dedicated to landscaping professionals. Every solution, every innovation, every strategy designed specifically for your industry.
              </p>

              <ul className="space-y-2 xs:space-y-3">
                {[
                  { name: 'Residential Landscaping', href: '/solutions/residential' },
                  { name: 'Commercial Landscaping', href: '/solutions/commercial' },
                  { name: 'Landscape Design', href: '/solutions/design' },
                  { name: 'Hardscaping Services', href: '/solutions/hardscaping' },
                  { name: 'Irrigation & Maintenance', href: '/solutions/irrigation' },
                  { name: 'Tree Services', href: '/solutions/tree-services' }
                ].map((link, index) => (
                  <li key={index}>
                    <Link href={link.href} className="text-xs xs:text-sm text-gray-300 hover:text-green-400 transition-colors touch-target block py-1 truncate">
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>

              <div className="pt-2 xs:pt-3 border-t border-gray-700">
                <Link href="/solutions" className="text-xs xs:text-sm text-green-400 hover:text-green-300 transition-colors touch-target block py-1 font-medium">
                  View All Solutions →
                </Link>
              </div>
            </div>
          </div>

          {/* Company & Resources */}
          <div className="sm:col-span-2 lg:col-span-1">
            <h4 className="text-base xs:text-lg font-semibold text-white mb-4 xs:mb-6">Company & Resources</h4>
            <ul className="space-y-2 xs:space-y-3 mb-6 xs:mb-8">
              {footerLinks.company.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-xs xs:text-sm text-gray-300 hover:text-white transition-colors flex items-center group touch-target py-1"
                  >
                    <ArrowRight className="h-2 w-2 xs:h-3 xs:w-3 mr-2 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0" />
                    <span className="truncate">{link.name}</span>
                  </Link>
                </li>
              ))}
            </ul>

            {/* Stats */}
            <div className="bg-gray-800 rounded-lg p-3 xs:p-4">
              <h5 className="text-white font-semibold mb-3 xs:mb-4 text-sm xs:text-base">Our Impact</h5>
              <div className="space-y-2 xs:space-y-3">
                <div className="flex items-center space-x-2 xs:space-x-3">
                  <Users className="h-3 w-3 xs:h-4 xs:w-4 text-blue-400 flex-shrink-0" />
                  <span className="text-gray-300 text-xs xs:text-sm">200+ Landscaping Clients</span>
                </div>
                <div className="flex items-center space-x-2 xs:space-x-3">
                  <TrendingUp className="h-3 w-3 xs:h-4 xs:w-4 text-green-400 flex-shrink-0" />
                  <span className="text-gray-300 text-xs xs:text-sm">247% Avg Lead Increase</span>
                </div>
                <div className="flex items-center space-x-2 xs:space-x-3">
                  <Award className="h-3 w-3 xs:h-4 xs:w-4 text-yellow-400 flex-shrink-0" />
                  <span className="text-gray-300 text-xs xs:text-sm">89% Client Retention</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Newsletter Signup */}
      <div className="border-t border-gray-800">
        <div className="container py-6 xs:py-8">
          <div className="max-w-4xl mx-auto text-center">
            <h4 className="text-lg xs:text-xl font-semibold text-white mb-2">Stay Updated with Landscaping Growth Insights</h4>
            <p className="text-sm xs:text-base text-gray-300 mb-4 xs:mb-6 px-4">
              Get exclusive landscaping business growth strategies, case studies, and industry insights delivered to your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 xs:gap-4 max-w-md mx-auto px-4">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-3 xs:px-4 py-2 xs:py-3 rounded-lg bg-gray-800 text-white border border-gray-700 focus:border-blue-500 focus:outline-none text-sm xs:text-base touch-target"
              />
              <button className="bg-blue-600 text-white px-4 xs:px-6 py-2 xs:py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors text-sm xs:text-base touch-target">
                Subscribe
              </button>
            </div>
            <p className="text-gray-400 text-xs xs:text-sm mt-3 px-4">
              No spam. Unsubscribe anytime. We respect your privacy.
            </p>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="container py-4 xs:py-6">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0 gap-4">
            <div className="text-gray-400 text-xs xs:text-sm text-center sm:text-left">
              © {currentYear} GroundUPDigital. All rights reserved.
            </div>
            <div className="flex flex-wrap items-center justify-center sm:justify-end gap-3 xs:gap-4 sm:gap-6 text-xs xs:text-sm text-gray-400">
              <Link href="/privacy-policy" className="hover:text-white transition-colors touch-target">
                Privacy Policy
              </Link>
              <Link href="/terms-of-service" className="hover:text-white transition-colors touch-target">
                Terms of Service
              </Link>
              <Link href="/sitemap.xml" className="hover:text-white transition-colors touch-target">
                Sitemap
              </Link>
            </div>
          </div>
        </div>
      </div>


    </footer>
  );
}
