'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  ChevronRight,
  Leaf,
  Sun,
  Snowflake,
  Calendar,
  TrendingUp,
  Target,
  Clock,
  CheckCircle,
  ArrowRight,
  Phone,
  Star,
  BarChart3,
  Users,
  Award
} from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { ModernCard, ServiceCard, StatsCard, FeatureCard, CTACard } from '@/components/ui/ModernCard';
import { StaggerContainer, StaggerItem, Reveal, GradientText } from '@/components/ui/AnimatedElements';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

const seasonalServices = [
  {
    icon: <Leaf className="w-6 h-6 text-green-600" />,
    title: "Spring Awakening Campaigns",
    description: "Capture the spring rush with targeted campaigns for lawn care startup, spring cleanup, and landscaping installations.",
    features: [
      "Early bird spring cleanup promotions",
      "Lawn care service startup campaigns",
      "Landscaping installation marketing",
      "Irrigation system activation ads",
      "Seasonal SEO optimization"
    ],
    price: "Starting at $2,997",
    season: "March - May"
  },
  {
    icon: <Sun className="w-6 h-6 text-yellow-600" />,
    title: "Summer Maintenance Mastery",
    description: "Dominate the peak season with maintenance contracts, irrigation services, and drought-resistant landscaping campaigns.",
    features: [
      "Weekly maintenance contract campaigns",
      "Irrigation repair and installation ads",
      "Drought-resistant landscaping promotion",
      "Commercial property maintenance",
      "Emergency service marketing"
    ],
    price: "Starting at $3,497",
    season: "June - August",
    popular: true
  },
  {
    icon: <Leaf className="w-6 h-6 text-orange-600" />,
    title: "Fall Cleanup Domination",
    description: "Maximize fall revenue with leaf removal, winter prep, and holiday lighting installation campaigns.",
    features: [
      "Leaf removal service campaigns",
      "Winter preparation marketing",
      "Holiday lighting installation",
      "Tree and shrub care promotion",
      "Seasonal contract renewals"
    ],
    price: "Starting at $2,497",
    season: "September - November"
  },
  {
    icon: <Snowflake className="w-6 h-6 text-blue-600" />,
    title: "Winter Revenue Strategies",
    description: "Generate off-season revenue with snow removal, holiday services, and planning campaigns for next year.",
    features: [
      "Snow removal service marketing",
      "Holiday decoration services",
      "Winter landscape maintenance",
      "Next season planning campaigns",
      "Equipment maintenance promotion"
    ],
    price: "Starting at $1,997",
    season: "December - February"
  }
];

const seasonalStats = [
  {
    number: "400%",
    label: "Seasonal Revenue Increase",
    icon: <TrendingUp className="w-6 h-6" />,
    trend: "up" as const,
    trendValue: "+65% year-over-year"
  },
  {
    number: "89%",
    label: "Client Retention Rate",
    icon: <Users className="w-6 h-6" />,
    trend: "up" as const,
    trendValue: "Across all seasons"
  },
  {
    number: "12M+",
    label: "Seasonal Revenue Generated",
    icon: <Award className="w-6 h-6" />,
    trend: "up" as const,
    trendValue: "For landscaping clients"
  },
  {
    number: "4.9/5",
    label: "Campaign Satisfaction",
    icon: <Star className="w-6 h-6" />,
    trend: "up" as const,
    trendValue: "89 seasonal campaigns"
  }
];

const seasonalFeatures = [
  {
    icon: <Calendar className="w-6 h-6" />,
    title: "Year-Round Planning",
    description: "Strategic planning that anticipates seasonal demands and prepares campaigns months in advance for maximum impact.",
    benefits: [
      "12-month marketing calendar",
      "Seasonal trend analysis",
      "Competitor seasonal monitoring",
      "Budget allocation optimization"
    ]
  },
  {
    icon: <Target className="w-6 h-6" />,
    title: "Season-Specific Targeting",
    description: "Precise audience targeting based on seasonal needs, weather patterns, and regional landscaping requirements.",
    benefits: [
      "Weather-triggered campaigns",
      "Geographic seasonal targeting",
      "Demographic seasonal preferences",
      "Behavioral pattern optimization"
    ]
  },
  {
    icon: <Clock className="w-6 h-6" />,
    title: "Perfect Timing Execution",
    description: "Launch campaigns at the optimal moment when customers are actively searching for seasonal landscaping services.",
    benefits: [
      "Optimal launch timing",
      "Seasonal keyword optimization",
      "Weather-based adjustments",
      "Real-time campaign optimization"
    ]
  }
];

const testimonials = [
  {
    name: "Jennifer Walsh",
    company: "Four Seasons Landscaping",
    location: "Denver, CO",
    quote: "GroundUP Digital's seasonal campaigns transformed our business. We now generate consistent revenue year-round instead of just during peak season. Our winter revenue alone increased 300%.",
    rating: 5,
    results: "300% winter revenue increase"
  },
  {
    name: "Mike Rodriguez",
    company: "Seasonal Lawn Care Pro",
    location: "Minneapolis, MN",
    quote: "Their understanding of seasonal landscaping patterns is incredible. They know exactly when to launch each campaign for maximum impact. We're booked solid every season now.",
    rating: 5,
    results: "Booked solid year-round"
  },
  {
    name: "Sarah Thompson",
    company: "All Season Landscapes",
    location: "Boston, MA",
    quote: "The seasonal marketing calendar they created for us is pure gold. We know exactly what to promote when, and our revenue has increased 400% across all seasons.",
    rating: 5,
    results: "400% revenue increase"
  }
];

export const SeasonalMarketingContent: React.FC = () => {
  return (
    <>
      {/* Breadcrumb */}
      <Section background="gray" padding="sm">
        <Container>
          <nav className="text-sm" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2 text-gray-600">
              <li>
                <Link href="/" className="hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li><ChevronRight className="h-4 w-4" aria-hidden="true" /></li>
              <li>
                <Link href="/services" className="hover:text-blue-600 transition-colors">
                  Services
                </Link>
              </li>
              <li><ChevronRight className="h-4 w-4" aria-hidden="true" /></li>
              <li>
                <Link href="/services/landscaping" className="hover:text-blue-600 transition-colors">
                  Landscaping
                </Link>
              </li>
              <li><ChevronRight className="h-4 w-4" aria-hidden="true" /></li>
              <li className="text-gray-900 font-medium">Seasonal Marketing</li>
            </ol>
          </nav>
        </Container>
      </Section>

      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center">
            <Reveal>
              <div className="inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Calendar className="h-4 w-4" />
                Seasonal Marketing Specialists
              </div>
              
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                Dominate Every Season with{' '}
                <GradientText colors={['from-green-600', 'via-yellow-500', 'to-orange-600']}>
                  Strategic Seasonal Campaigns
                </GradientText>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                The only agency that specializes in year-round landscaping marketing strategies. 
                We've helped 89+ landscaping companies generate over $12M in seasonal revenue with our proven campaign frameworks.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button variant="primary" size="lg" className="group">
                  Get Your Seasonal Marketing Calendar
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button variant="outline" size="lg" className="group">
                  <Phone className="mr-2 h-5 w-5" />
                  Call (*************
                </Button>
              </div>
              
              <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>400% seasonal revenue increase</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Year-round planning</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Weather-triggered campaigns</span>
                </div>
              </div>
            </Reveal>
          </div>
        </Container>
      </Section>

      {/* Stats Section */}
      <Section background="white" padding="lg">
        <Container>
          <StaggerContainer className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {seasonalStats.map((stat, index) => (
              <StaggerItem key={stat.label}>
                <StatsCard {...stat} />
              </StaggerItem>
            ))}
          </StaggerContainer>
        </Container>
      </Section>

      {/* Seasonal Services Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="text-center mb-16">
            <Reveal>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Complete Seasonal Marketing Solutions for Every Season
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                From spring awakening to winter revenue strategies, we provide comprehensive marketing campaigns 
                that maximize your landscaping business revenue throughout the entire year.
              </p>
            </Reveal>
          </div>

          <StaggerContainer className="grid lg:grid-cols-2 gap-8">
            {seasonalServices.map((service, index) => (
              <StaggerItem key={service.title}>
                <ServiceCard {...service} />
              </StaggerItem>
            ))}
          </StaggerContainer>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="text-center mb-16">
            <Reveal>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Why Our Seasonal Marketing Strategies Work
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                We understand the unique challenges and opportunities each season brings to landscaping businesses. 
                Our data-driven approach ensures maximum ROI year-round.
              </p>
            </Reveal>
          </div>

          <StaggerContainer className="grid lg:grid-cols-3 gap-8">
            {seasonalFeatures.map((feature, index) => (
              <StaggerItem key={feature.title}>
                <FeatureCard {...feature} />
              </StaggerItem>
            ))}
          </StaggerContainer>
        </Container>
      </Section>

      {/* Testimonials Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="text-center mb-16">
            <Reveal>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Success Stories from Seasonal Marketing Clients
              </h2>
              <p className="text-xl text-gray-600">
                See how landscaping businesses have transformed their seasonal revenue with our proven strategies.
              </p>
            </Reveal>
          </div>

          <StaggerContainer className="grid lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <StaggerItem key={testimonial.name}>
                <ModernCard variant="elevated" size="lg" className="h-full">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  
                  <blockquote className="text-gray-700 mb-6 leading-relaxed">
                    "{testimonial.quote}"
                  </blockquote>
                  
                  <div className="border-t border-gray-100 pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-semibold text-gray-900">{testimonial.name}</div>
                        <div className="text-sm text-gray-600">{testimonial.company}</div>
                        <div className="text-sm text-gray-500">{testimonial.location}</div>
                      </div>
                      <Badge variant="primary" size="sm">
                        {testimonial.results}
                      </Badge>
                    </div>
                  </div>
                </ModernCard>
              </StaggerItem>
            ))}
          </StaggerContainer>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <Reveal>
              <CTACard
                title="Ready to Dominate Every Season?"
                description="Get your comprehensive seasonal marketing calendar and start generating consistent revenue year-round with our proven landscaping marketing strategies."
                buttonText="Get Your Seasonal Marketing Plan"
                variant="primary"
              />
            </Reveal>
          </div>
        </Container>
      </Section>
    </>
  );
};
