import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowR<PERSON>, Wifi, Droplets, Thermometer, Zap, Star, CheckCircle, Phone, Smartphone } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Smart Landscaping & IoT Solutions | GroundUP Digital',
  description: 'Connected landscaping systems that create ongoing service opportunities. IoT sensors, smart irrigation, and automated monitoring for landscaping businesses.',
  keywords: 'smart landscaping, IoT landscaping, smart irrigation systems, connected landscaping, automated landscape monitoring, landscaping technology',
};

const iotFeatures = [
  {
    icon: Droplets,
    title: 'Smart Irrigation Systems',
    description: 'Intelligent watering systems that optimize water usage and create recurring service opportunities.',
    benefits: ['Automated watering schedules', 'Weather-based adjustments', 'Water usage monitoring', 'Remote system control']
  },
  {
    icon: Thermometer,
    title: 'Environmental Monitoring',
    description: 'Comprehensive monitoring of soil, weather, and plant health conditions.',
    benefits: ['Soil moisture sensors', 'Temperature monitoring', 'Light level tracking', 'Plant health alerts']
  },
  {
    icon: Smartphone,
    title: 'Client Dashboard & Apps',
    description: 'Custom mobile apps that keep clients engaged and create upselling opportunities.',
    benefits: ['Real-time system status', 'Maintenance notifications', 'Service scheduling', 'Performance analytics']
  }
];

const iotSolutions = [
  {
    name: 'Smart Irrigation Management',
    description: 'Automated irrigation systems with weather integration and remote monitoring',
    features: ['Weather-based scheduling', 'Zone-specific control', 'Water usage analytics', 'Leak detection alerts']
  },
  {
    name: 'Landscape Health Monitoring',
    description: 'Comprehensive monitoring of landscape conditions and plant health',
    features: ['Soil moisture tracking', 'Nutrient level monitoring', 'Disease early detection', 'Growth optimization']
  },
  {
    name: 'Maintenance Automation',
    description: 'Automated systems that schedule and track landscape maintenance needs',
    features: ['Automated scheduling', 'Equipment monitoring', 'Service reminders', 'Performance tracking']
  },
  {
    name: 'Client Engagement Platform',
    description: 'Mobile apps and dashboards that keep clients connected to their landscape',
    features: ['Real-time updates', 'Service requests', 'Photo documentation', 'Billing integration']
  }
];

const packages = [
  {
    name: "IoT Starter",
    price: "$1,497",
    period: "/month",
    description: "Essential IoT solutions for landscaping businesses ready to offer smart services",
    features: [
      "Basic smart irrigation setup",
      "Client mobile app",
      "Environmental monitoring",
      "Monthly system reports",
      "Technical support"
    ],
    popular: false
  },
  {
    name: "Smart Landscape Pro",
    price: "$2,997",
    period: "/month", 
    description: "Comprehensive IoT platform for landscaping companies ready to lead with technology",
    features: [
      "Advanced IoT sensor network",
      "Custom client dashboard",
      "Predictive maintenance alerts",
      "Integration with business systems",
      "Dedicated IoT specialist",
      "Quarterly system optimization"
    ],
    popular: true
  },
  {
    name: "Connected Enterprise",
    price: "$4,997",
    period: "/month",
    description: "Enterprise IoT solution for large landscaping businesses managing multiple properties",
    features: [
      "Multi-property IoT management",
      "Advanced analytics platform",
      "Custom IoT development",
      "White-label client solutions",
      "Dedicated development team",
      "Ongoing innovation support"
    ],
    popular: false
  }
];

const caseStudy = {
  company: "SmartScape Solutions",
  location: "Portland, OR",
  results: [
    { metric: "Recurring Revenue", improvement: "+240%" },
    { metric: "Client Retention", improvement: "+85%" },
    { metric: "Service Efficiency", improvement: "+60%" },
    { metric: "Upsell Opportunities", improvement: "+180%" }
  ]
};

const benefits = [
  { icon: Zap, title: "Recurring Revenue", description: "Create ongoing service contracts with smart systems" },
  { icon: Droplets, title: "Water Efficiency", description: "Reduce water usage by up to 40% with smart irrigation" },
  { icon: Star, title: "Client Satisfaction", description: "Increase satisfaction with proactive monitoring" },
  { icon: Wifi, title: "Competitive Edge", description: "Differentiate with cutting-edge technology" }
];

export default function IoTIntegrationPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Wifi className="w-4 h-4" />
              <span className="text-sm font-semibold">Smart Landscaping & IoT Solutions</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Connected Landscaping Systems for{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Forward-Thinking Businesses
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              IoT-powered landscaping solutions that create ongoing service opportunities, improve efficiency, 
              and keep your landscaping business connected to every client property.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Wifi className="w-5 h-5 mr-2" />
                See IoT Demo
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                View Smart Projects
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '240%', label: 'Recurring Revenue' },
                { number: '40%', label: 'Water Savings' },
                { number: '85%', label: 'Client Retention' },
                { number: '25+', label: 'IoT Deployments' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Benefits Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Why Landscaping Businesses Choose IoT Integration
            </h2>
            <p className="text-lg text-gray-600">
              Smart landscaping systems create new revenue streams, improve client satisfaction, and position your 
              landscaping business as a technology leader in your market.
            </p>
          </div>
          
          <div className="grid md:grid-cols-4 gap-6">
            {benefits.map((benefit, index) => (
              <Card key={index} className="text-center h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <benefit.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">{benefit.title}</h3>
                  <p className="text-sm text-gray-600">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Comprehensive IoT Solutions for Landscaping Professionals
            </h2>
            <p className="text-lg text-gray-600">
              Complete smart landscaping systems designed specifically for landscaping business owners. 
              Every solution creates ongoing service opportunities and client engagement.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {iotFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* IoT Solutions Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Complete Smart Landscaping Ecosystem
            </h2>
            <p className="text-lg text-gray-600">
              Integrated IoT solutions that transform traditional landscaping into smart, connected systems 
              that benefit both your business and your clients.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {iotSolutions.map((solution, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{solution.name}</h3>
                  <p className="text-gray-600 mb-4">{solution.description}</p>
                  <ul className="space-y-2">
                    {solution.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                IoT Success Story: Creating Recurring Revenue
              </h2>
              <p className="text-lg text-gray-600">
                See how {caseStudy.company} used our IoT solutions to transform their business model and create predictable recurring revenue
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A progressive landscaping business in {caseStudy.location} wanted to create recurring revenue streams 
                      and differentiate from competitors. Our IoT solutions helped them build a sustainable, technology-driven business model.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Read IoT Case Study
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              IoT Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Smart landscaping technology designed specifically for landscaping business owners. 
              Choose the IoT solution that creates new revenue opportunities for your business.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Transform Your Landscaping Business with IoT?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Discover how smart landscaping systems can create recurring revenue, improve efficiency, 
              and position your business as a technology leader.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Wifi className="w-5 h-5 mr-2" />
                See IoT Demo
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule Consultation
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
