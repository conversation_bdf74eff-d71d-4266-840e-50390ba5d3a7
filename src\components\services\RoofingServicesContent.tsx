'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  ChevronRight,
  Home,
  Search,
  MousePointer,
  Globe,
  BarChart3,
  Users,
  Award,
  CheckCircle,
  ArrowRight,
  Phone,
  Star,
  TrendingUp,
  Target,
  Zap,
  Shield,
  AlertTriangle
} from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { ModernCard, ServiceCard, StatsCard, FeatureCard, CTACard } from '@/components/ui/ModernCard';
import { StaggerContainer, StaggerItem, Reveal, AnimatedCounter, GradientText } from '@/components/ui/AnimatedElements';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

const roofingServices = [
  {
    icon: <Search className="w-6 h-6 text-blue-600" />,
    title: "Roofing SEO",
    description: "Dominate local search results for roof repair, replacement, and storm damage restoration services in your area.",
    features: [
      "Local SEO for 'roofer near me' and emergency searches",
      "Google Business Profile optimization for roofing",
      "Storm damage and emergency keyword targeting",
      "Technical SEO for faster mobile loading",
      "Local citation building for roofing directories"
    ],
    price: "Starting at $2,497"
  },
  {
    icon: <AlertTriangle className="w-6 h-6 text-red-600" />,
    title: "Storm Damage Marketing",
    description: "Rapid-response marketing campaigns that capture storm damage leads within hours of severe weather events.",
    features: [
      "24-hour emergency campaign deployment",
      "Weather-triggered automated campaigns",
      "Geo-targeted storm damage ads",
      "Emergency landing page creation",
      "Real-time lead tracking and routing"
    ],
    price: "Starting at $3,997",
    popular: true
  },
  {
    icon: <MousePointer className="w-6 h-6 text-blue-600" />,
    title: "Roofing Google Ads",
    description: "High-converting Google Ads campaigns that generate qualified leads for roof repairs, replacements, and inspections.",
    features: [
      "Emergency roof repair campaigns",
      "Insurance claim assistance targeting",
      "Seasonal campaign optimization",
      "Competitor conquest campaigns",
      "Call tracking and lead attribution"
    ],
    price: "Starting at $2,997"
  },
  {
    icon: <Globe className="w-6 h-6 text-blue-600" />,
    title: "Roofing Web Design",
    description: "Professional, mobile-responsive websites that build trust and convert visitors into roofing customers.",
    features: [
      "Mobile-first responsive design",
      "Before/after project galleries",
      "Insurance claim assistance pages",
      "Emergency contact forms",
      "Customer testimonial sections"
    ],
    price: "Starting at $4,997"
  }
];

const roofingStats = [
  {
    number: "400%",
    label: "Storm Lead Increase",
    icon: <TrendingUp className="w-6 h-6" />,
    trend: "up" as const,
    trendValue: "+60% this season"
  },
  {
    number: "120+",
    label: "Roofing Contractors",
    icon: <Users className="w-6 h-6" />,
    trend: "up" as const,
    trendValue: "Growing monthly"
  },
  {
    number: "$35M+",
    label: "Revenue Generated",
    icon: <Award className="w-6 h-6" />,
    trend: "up" as const,
    trendValue: "For roofing clients"
  },
  {
    number: "4.9/5",
    label: "Client Satisfaction",
    icon: <Star className="w-6 h-6" />,
    trend: "up" as const,
    trendValue: "89 reviews"
  }
];

const roofingFeatures = [
  {
    icon: <Shield className="w-6 h-6" />,
    title: "Storm Response Expertise",
    description: "We specialize in rapid-response marketing for storm damage, helping you capture leads within hours of severe weather events.",
    benefits: [
      "24-hour emergency campaign deployment",
      "Weather monitoring and automated triggers",
      "Storm damage keyword expertise",
      "Insurance claim process optimization"
    ]
  },
  {
    icon: <Target className="w-6 h-6" />,
    title: "Roofing Industry Focus",
    description: "Exclusive focus on roofing contractors means we understand your business cycles, customer pain points, and competitive landscape.",
    benefits: [
      "Deep knowledge of roofing customer journey",
      "Emergency vs. planned service optimization",
      "Insurance claim assistance marketing",
      "Seasonal campaign management"
    ]
  },
  {
    icon: <Zap className="w-6 h-6" />,
    title: "Rapid Lead Generation",
    description: "Our proven strategies generate qualified roofing leads within 24 hours, helping you respond faster than competitors.",
    benefits: [
      "Emergency lead capture systems",
      "High-intent keyword targeting",
      "Conversion-optimized landing pages",
      "Real-time lead routing and tracking"
    ]
  }
];

const testimonials = [
  {
    name: "Tom Rodriguez",
    company: "Apex Roofing Solutions",
    location: "Dallas, TX",
    quote: "GroundUP Digital's storm damage campaigns are incredible. After the last hailstorm, we generated 47 qualified leads in 48 hours. Our revenue increased 350% that quarter.",
    rating: 5,
    results: "350% revenue increase"
  },
  {
    name: "Jennifer Walsh",
    company: "Premier Roof Repair",
    location: "Oklahoma City, OK",
    quote: "They understand the roofing business like no other agency. Their emergency response campaigns have made us the go-to roofer in our area for storm damage.",
    rating: 5,
    results: "47 leads in 48 hours"
  },
  {
    name: "Mark Thompson",
    company: "Elite Roofing Contractors",
    location: "Atlanta, GA",
    quote: "The ROI is phenomenal. Every storm season, we're booked solid thanks to their marketing. Best investment we've made for our roofing business.",
    rating: 5,
    results: "900% ROI"
  }
];

export const RoofingServicesContent: React.FC = () => {
  return (
    <>
      {/* Breadcrumb */}
      <Section background="gray" padding="sm">
        <Container>
          <nav className="text-sm">
            <ol className="flex items-center space-x-2 text-gray-600">
              <li>
                <Link href="/" className="hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li><ChevronRight className="h-4 w-4" /></li>
              <li>
                <Link href="/services" className="hover:text-blue-600 transition-colors">
                  Services
                </Link>
              </li>
              <li><ChevronRight className="h-4 w-4" /></li>
              <li className="text-gray-900 font-medium">Roofing Marketing</li>
            </ol>
          </nav>
        </Container>
      </Section>

      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center">
            <Reveal>
              <div className="inline-flex items-center gap-2 bg-red-100 text-red-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Home className="h-4 w-4" />
                #1 Roofing Marketing Agency
              </div>
              
              <h1 className="text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 xs:mb-6 leading-tight">
                Dominate Storm Season with{' '}
                <GradientText colors={['from-red-600', 'via-orange-600', 'to-yellow-600']}>
                  Expert Roofing Marketing
                </GradientText>
              </h1>
              
              <p className="text-base xs:text-lg sm:text-xl text-gray-600 mb-6 xs:mb-8 leading-relaxed">
                The only digital marketing agency that exclusively serves roofing contractors. 
                We've helped 120+ roofing companies generate over $35M in revenue with our storm-response marketing strategies.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-3 xs:gap-4 justify-center mb-8 xs:mb-10 sm:mb-12">
                <Button variant="primary" size="lg" className="group">
                  Get Your Free Roofing Marketing Audit
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button variant="outline" size="lg" className="group">
                  <Phone className="mr-2 h-5 w-5" />
                  Emergency: (*************
                </Button>
              </div>
              
              <div className="flex flex-col xs:flex-row items-center justify-center gap-3 xs:gap-6 text-xs xs:text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>24-hour storm response</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>400% storm lead increase</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Insurance claim expertise</span>
                </div>
              </div>
            </Reveal>
          </div>
        </Container>
      </Section>

      {/* Stats Section */}
      <Section background="white" padding="lg">
        <Container>
          <StaggerContainer className="grid grid-cols-2 sm:grid-cols-4 gap-3 xs:gap-4 sm:gap-6">
            {roofingStats.map((stat, index) => (
              <StaggerItem key={stat.label}>
                <StatsCard {...stat} />
              </StaggerItem>
            ))}
          </StaggerContainer>
        </Container>
      </Section>

      {/* Services Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="text-center mb-8 xs:mb-12 sm:mb-16">
            <Reveal>
              <h2 className="text-xl xs:text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Complete Digital Marketing Solutions for Roofing Contractors
              </h2>
              <p className="text-base xs:text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
                From emergency storm response to year-round lead generation, we provide everything your roofing business needs to dominate your local market.
              </p>
            </Reveal>
          </div>

          <StaggerContainer className="grid grid-cols-1 lg:grid-cols-2 gap-4 xs:gap-6 sm:gap-8">
            {roofingServices.map((service, index) => (
              <StaggerItem key={service.title}>
                <ServiceCard {...service} />
              </StaggerItem>
            ))}
          </StaggerContainer>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="text-center mb-16">
            <Reveal>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Why Roofing Contractors Choose GroundUP Digital
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                We're not just another marketing agency. We're roofing industry specialists who understand storm seasons, insurance claims, and emergency response marketing.
              </p>
            </Reveal>
          </div>

          <StaggerContainer className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 xs:gap-6 sm:gap-8">
            {roofingFeatures.map((feature, index) => (
              <StaggerItem key={feature.title}>
                <FeatureCard {...feature} />
              </StaggerItem>
            ))}
          </StaggerContainer>
        </Container>
      </Section>

      {/* Testimonials Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="text-center mb-16">
            <Reveal>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Success Stories from Roofing Contractors
              </h2>
              <p className="text-xl text-gray-600">
                See how we've helped roofing companies across the country dominate storm seasons and grow their businesses.
              </p>
            </Reveal>
          </div>

          <StaggerContainer className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 xs:gap-6 sm:gap-8">
            {testimonials.map((testimonial, index) => (
              <StaggerItem key={testimonial.name}>
                <ModernCard variant="elevated" size="lg" className="h-full">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  
                  <blockquote className="text-gray-700 mb-6 leading-relaxed">
                    "{testimonial.quote}"
                  </blockquote>
                  
                  <div className="border-t border-gray-100 pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-semibold text-gray-900">{testimonial.name}</div>
                        <div className="text-sm text-gray-600">{testimonial.company}</div>
                        <div className="text-sm text-gray-500">{testimonial.location}</div>
                      </div>
                      <Badge variant="primary" size="sm">
                        {testimonial.results}
                      </Badge>
                    </div>
                  </div>
                </ModernCard>
              </StaggerItem>
            ))}
          </StaggerContainer>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <Reveal>
              <CTACard
                title="Ready to Dominate Your Next Storm Season?"
                description="Join 120+ successful roofing contractors who trust GroundUP Digital to generate consistent, high-quality storm damage and repair leads."
                buttonText="Get Your Free Storm Marketing Strategy"
                variant="primary"
              />
            </Reveal>
          </div>
        </Container>
      </Section>
    </>
  );
};
