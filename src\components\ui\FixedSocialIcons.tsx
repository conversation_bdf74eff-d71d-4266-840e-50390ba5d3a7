'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useHydration } from '@/hooks/useHydration';
import { 
  MessageCircle, 
  Phone, 
  Mail, 
  Facebook, 
  Instagram,
  Linkedin,
  Twitter,
  X,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface SocialIcon {
  id: string;
  icon: React.ReactNode;
  label: string;
  href: string;
  color: string;
  hoverColor: string;
  action?: () => void;
}

const socialIcons: SocialIcon[] = [
  {
    id: 'whatsapp',
    icon: (
      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
      </svg>
    ),
    label: 'WhatsApp Chat - Get instant support for your business',
    href: 'https://wa.me/***********?text=Hi! I\'m interested in your digital marketing services for my landscaping/roofing/pest control business. Can you help me get more leads?',
    color: 'bg-green-500',
    hoverColor: 'hover:bg-green-600'
  },
  {
    id: 'messenger',
    icon: (
      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M12 0C5.373 0 0 4.975 0 11.111c0 3.497 1.745 6.616 4.472 8.652V24l4.086-2.242c1.09.301 2.246.464 3.442.464 6.627 0 12-4.974 12-11.111C24 4.975 18.627 0 12 0zm1.193 14.963l-3.056-3.259-5.963 3.259L10.732 8.1l3.13 3.259L19.752 8.1l-6.559 6.863z"/>
      </svg>
    ),
    label: 'Facebook Messenger - Direct messaging',
    href: 'https://m.me/groundupdigital',
    color: 'bg-blue-600',
    hoverColor: 'hover:bg-blue-700'
  },
  {
    id: 'phone',
    icon: <Phone className="w-5 h-5" aria-hidden="true" />,
    label: 'Call Now - Speak with our experts',
    href: 'tel:+***********',
    color: 'bg-orange-500',
    hoverColor: 'hover:bg-orange-600'
  },
  {
    id: 'email',
    icon: <Mail className="w-5 h-5" aria-hidden="true" />,
    label: 'Send Email - Get detailed information',
    href: 'mailto:<EMAIL>?subject=Digital Marketing Inquiry - Pioneer Solutions&body=Hi GroundUP Digital Team,%0D%0A%0D%0AI\'m interested in your enterprise-grade digital marketing solutions for my business.%0D%0A%0D%0ABusiness Type: [Landscaping/Roofing/Pest Control]%0D%0ALocation: %0D%0ACurrent Challenges: %0D%0A%0D%0APlease contact me to discuss how you can help grow my business.%0D%0A%0D%0AThank you!',
    color: 'bg-red-500',
    hoverColor: 'hover:bg-red-600'
  },
  {
    id: 'youtube',
    icon: (
      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
      </svg>
    ),
    label: 'YouTube - Watch our success stories',
    href: 'https://youtube.com/@groundupdigital',
    color: 'bg-red-600',
    hoverColor: 'hover:bg-red-700'
  },
  {
    id: 'tiktok',
    icon: (
      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
      </svg>
    ),
    label: 'TikTok - Follow for marketing tips',
    href: 'https://tiktok.com/@groundupdigital',
    color: 'bg-black',
    hoverColor: 'hover:bg-gray-800'
  },
  {
    id: 'pinterest',
    icon: (
      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.**************.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z"/>
      </svg>
    ),
    label: 'Pinterest - Design inspiration',
    href: 'https://pinterest.com/groundupdigital',
    color: 'bg-red-500',
    hoverColor: 'hover:bg-red-600'
  },
  {
    id: 'linkedin',
    icon: <Linkedin className="w-5 h-5" aria-hidden="true" />,
    label: 'LinkedIn - Professional network',
    href: 'https://linkedin.com/company/groundupdigital',
    color: 'bg-blue-700',
    hoverColor: 'hover:bg-blue-800'
  },
  {
    id: 'instagram',
    icon: <Instagram className="w-5 h-5" aria-hidden="true" />,
    label: 'Instagram - Behind the scenes',
    href: 'https://instagram.com/groundupdigital',
    color: 'bg-gradient-to-br from-purple-500 to-pink-500',
    hoverColor: 'hover:from-purple-600 hover:to-pink-600'
  }
];

export const FixedSocialIcons: React.FC = () => {
  const [isHovered, setIsHovered] = useState(false);
  const [hoveredIcon, setHoveredIcon] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const isMounted = useHydration();

  // Scroll-triggered visibility
  useEffect(() => {
    if (!isMounted) return;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setIsVisible(currentScrollY > 250); // Show after 250px scroll
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isMounted]);

  // Remove WhatsApp notification functionality

  const handleIconClick = (icon: SocialIcon) => {
    if (icon.action) {
      icon.action();
    } else {
      window.open(icon.href, '_blank', 'noopener,noreferrer');
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent, icon: SocialIcon) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleIconClick(icon);
    }
  };

  // Don't render on server to avoid hydration mismatch
  if (!isMounted) {
    return null;
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ x: -100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: -100, opacity: 0 }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
          className="fixed left-0 top-1/2 transform -translate-y-1/2 fixed-social-icons"
          role="complementary"
          aria-label="Social media and contact options"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className="flex flex-col items-start">
        {/* Main Contact Icon - Always Visible */}
        <motion.div
          className="relative"
          initial={{ x: -60 }}
          animate={{ x: 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
        >
          <motion.button
            onClick={() => handleIconClick(socialIcons[0])} // WhatsApp as main contact
            className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white p-3 rounded-r-lg shadow-lg transition-all duration-300 relative overflow-hidden focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 touch-target"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Contact us on WhatsApp"
            tabIndex={0}
          >
            {/* Shimmer effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              animate={{
                x: [-100, 100]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: 'linear'
              }}
            />

            <MessageCircle className="w-5 h-5" />
          </motion.button>

          {/* Pulse indicator */}
          <motion.div
            className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [1, 0.7, 1]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />
        </motion.div>

        {/* Social Icons Container - Show on Hover */}
        <AnimatePresence>
          {isHovered && (
            <motion.div
              initial={{ x: -300, opacity: 0, scale: 0.8 }}
              animate={{ x: 0, opacity: 1, scale: 1 }}
              exit={{ x: -300, opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.3, ease: 'easeOut' }}
              className="flex flex-col space-y-2 mt-2"
            >
              {socialIcons.filter(icon => ['messenger', 'phone', 'email'].includes(icon.id)).map((icon, index) => (
                <motion.div
                  key={icon.id}
                  initial={{ x: -100, opacity: 0, scale: 0.8 }}
                  animate={{ x: 0, opacity: 1, scale: 1 }}
                  transition={{
                    duration: 0.4,
                    delay: index * 0.08,
                    ease: 'easeOut',
                    type: 'spring',
                    stiffness: 100
                  }}
                  className="relative group"
                  onMouseEnter={() => setHoveredIcon(icon.id)}
                  onMouseLeave={() => setHoveredIcon(null)}
                >
                  <motion.button
                    onClick={() => handleIconClick(icon)}
                    onKeyDown={(e) => handleKeyDown(e, icon)}
                    className={`${icon.color} ${icon.hoverColor} text-white p-3 rounded-r-lg shadow-lg transition-all duration-300 relative overflow-hidden focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800 touch-target`}
                    whileHover={{
                      scale: 1.1,
                      x: 5,
                      rotate: [0, -2, 2, 0]
                    }}
                    whileTap={{ scale: 0.95 }}
                    aria-label={icon.label}
                    tabIndex={0}
                    role="button"
                  >
                    {/* Ripple effect */}
                    <motion.div
                      className="absolute inset-0 bg-white/20 rounded-r-lg"
                      initial={{ scale: 0, opacity: 0 }}
                      animate={hoveredIcon === icon.id ? { 
                        scale: 1, 
                        opacity: [0, 1, 0] 
                      } : { scale: 0, opacity: 0 }}
                      transition={{ duration: 0.6 }}
                    />
                    
                    {/* Icon with pulse animation */}
                    <motion.div
                      animate={hoveredIcon === icon.id ? {
                        scale: [1, 1.2, 1],
                        rotate: [0, 5, -5, 0]
                      } : { scale: 1, rotate: 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      {icon.icon}
                    </motion.div>
                  </motion.button>

                  {/* Tooltip */}
                  <AnimatePresence>
                    {hoveredIcon === icon.id && (
                      <motion.div
                        initial={{ opacity: 0, x: -10, scale: 0.8 }}
                        animate={{ opacity: 1, x: 0, scale: 1 }}
                        exit={{ opacity: 0, x: -10, scale: 0.8 }}
                        transition={{ duration: 0.2 }}
                        className="absolute left-full ml-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap shadow-lg z-10"
                      >
                        {icon.label}
                        <div className="absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-gray-900"></div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
