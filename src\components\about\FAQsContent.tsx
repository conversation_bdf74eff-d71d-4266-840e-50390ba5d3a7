'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { 
  ChevronDown, 
  ChevronRight, 
  Search, 
  HelpCircle,
  MessageCircle,
  Phone,
  Mail
} from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

const faqCategories = [
  { id: 'general', name: 'General', count: 8 },
  { id: 'services', name: 'Services', count: 12 },
  { id: 'pricing', name: 'Pricing', count: 6 },
  { id: 'process', name: 'Process', count: 10 },
  { id: 'results', name: 'Results', count: 7 },
  { id: 'support', name: 'Support', count: 5 }
];

const faqs = [
  {
    id: 1,
    category: 'general',
    question: 'What makes GroundUP Digital different from other digital marketing agencies?',
    answer: 'We specialize exclusively in local service businesses like landscaping, roofing, and pest control. This focus allows us to understand your unique challenges and deliver proven strategies that work specifically for your industry. Our team has generated over $50M in revenue for local service providers.'
  },
  {
    id: 2,
    category: 'general',
    question: 'Do you work with businesses outside of landscaping, roofing, and pest control?',
    answer: 'While we specialize in these three industries, we also work with other local service businesses including HVAC, plumbing, electrical, and home improvement companies. Our strategies are adaptable to any local service business model.'
  },
  {
    id: 3,
    category: 'services',
    question: 'What digital marketing services do you offer?',
    answer: 'We offer a comprehensive suite of services including SEO, Google Ads (PPC), web design and development, social media marketing, reputation management, lead generation systems, CRM implementation, marketing automation, and data analytics.'
  },
  {
    id: 4,
    category: 'services',
    question: 'Do you provide website design and development?',
    answer: 'Yes, we create custom, mobile-responsive websites optimized for conversions. Our websites are built with SEO best practices, fast loading speeds, and clear calls-to-action to turn visitors into leads.'
  },
  {
    id: 5,
    category: 'pricing',
    question: 'How much do your services cost?',
    answer: 'Our pricing varies based on your specific needs, market size, and goals. We offer packages starting from $2,500/month for small local businesses up to enterprise solutions for larger companies. We provide custom quotes after understanding your requirements.'
  },
  {
    id: 6,
    category: 'pricing',
    question: 'Do you require long-term contracts?',
    answer: 'We offer both month-to-month and contract options. While we believe in the value of long-term partnerships for best results, we don\'t lock you into lengthy contracts. Most clients choose 6-12 month agreements for optimal campaign performance.'
  },
  {
    id: 7,
    category: 'process',
    question: 'How long does it take to see results?',
    answer: 'Results vary by service: Google Ads can generate leads within days, while SEO typically takes 3-6 months for significant improvements. We provide monthly reports showing progress and typically see meaningful improvements within the first 90 days.'
  },
  {
    id: 8,
    category: 'process',
    question: 'What is your onboarding process like?',
    answer: 'Our onboarding includes: 1) Strategy consultation and goal setting, 2) Market and competitor analysis, 3) Account setup and optimization, 4) Campaign launch, and 5) Initial performance review. The entire process typically takes 2-3 weeks.'
  },
  {
    id: 9,
    category: 'results',
    question: 'What kind of results can I expect?',
    answer: 'Our clients typically see 200-400% increase in qualified leads within 6 months. Specific results depend on your market, competition, and investment level. We provide realistic projections during our initial consultation.'
  },
  {
    id: 10,
    category: 'results',
    question: 'How do you measure and report on success?',
    answer: 'We track key metrics including website traffic, lead generation, conversion rates, cost per lead, and ROI. You\'ll receive detailed monthly reports and have access to a client dashboard for real-time performance monitoring.'
  },
  {
    id: 11,
    category: 'support',
    question: 'What kind of support do you provide?',
    answer: 'Every client gets a dedicated account manager, monthly strategy calls, 24/7 campaign monitoring, and priority email/phone support. We also provide training on tools and systems we implement.'
  },
  {
    id: 12,
    category: 'support',
    question: 'Can you help with emergency or seasonal campaigns?',
    answer: 'Absolutely! We specialize in rapid response campaigns for emergencies (storm damage, pest outbreaks) and seasonal promotions. Our team can launch emergency campaigns within 24-48 hours.'
  }
];

export const FAQsContent: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('general');
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredFAQs = faqs.filter(faq => {
    const matchesCategory = activeCategory === 'all' || faq.category === activeCategory;
    const matchesSearch = searchTerm === '' || 
      faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const toggleFAQ = (id: number) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <>
      {/* Breadcrumb */}
      <Section background="gray" padding="sm">
        <Container>
          <nav className="text-sm">
            <ol className="flex items-center space-x-2 text-gray-600">
              <li>
                <Link href="/" className="hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li><ChevronRight className="h-4 w-4" /></li>
              <li>
                <Link href="/about" className="hover:text-blue-600 transition-colors">
                  About
                </Link>
              </li>
              <li><ChevronRight className="h-4 w-4" /></li>
              <li className="text-gray-900 font-medium">FAQs</li>
            </ol>
          </nav>
        </Container>
      </Section>

      {/* Hero Section */}
      <Section background="gradient" padding="lg">
        <Container>
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <HelpCircle className="h-4 w-4" />
                Frequently Asked Questions
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Got Questions? We've Got{' '}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600">
                  Answers
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8">
                Find answers to the most common questions about our digital marketing services, 
                processes, and how we help local service businesses grow.
              </p>
              
              {/* Search Bar */}
              <div className="relative max-w-md mx-auto">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search FAQs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                />
              </div>
            </motion.div>
          </div>
        </Container>
      </Section>

      {/* FAQ Categories */}
      <Section background="white" padding="sm">
        <Container>
          <div className="flex flex-wrap gap-4 justify-center">
            <button
              onClick={() => setActiveCategory('all')}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                activeCategory === 'all'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              All Questions ({faqs.length})
            </button>
            {faqCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                  activeCategory === category.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category.name} ({category.count})
              </button>
            ))}
          </div>
        </Container>
      </Section>

      {/* FAQ Content */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            {filteredFAQs.length === 0 ? (
              <div className="text-center py-12">
                <HelpCircle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No FAQs Found</h3>
                <p className="text-gray-600">
                  Try adjusting your search terms or browse different categories.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredFAQs.map((faq, index) => (
                  <motion.div
                    key={faq.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                  >
                    <Card className="overflow-hidden">
                      <button
                        onClick={() => toggleFAQ(faq.id)}
                        className="w-full p-6 text-left hover:bg-gray-50 transition-colors duration-200"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1 pr-4">
                            <div className="flex items-center gap-3 mb-2">
                              <Badge variant="secondary" size="xs">
                                {faqCategories.find(cat => cat.id === faq.category)?.name}
                              </Badge>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900">
                              {faq.question}
                            </h3>
                          </div>
                          <ChevronDown 
                            className={`h-5 w-5 text-gray-500 transition-transform duration-200 ${
                              openFAQ === faq.id ? 'rotate-180' : ''
                            }`}
                          />
                        </div>
                      </button>
                      
                      <AnimatePresence>
                        {openFAQ === faq.id && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3 }}
                            className="overflow-hidden"
                          >
                            <div className="px-6 pb-6">
                              <div className="pt-4 border-t border-gray-100">
                                <p className="text-gray-700 leading-relaxed">
                                  {faq.answer}
                                </p>
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </Container>
      </Section>

      {/* Contact CTA */}
      <Section background="gradient" padding="lg">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Still Have Questions?
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Our team is here to help. Get in touch and we'll answer any questions you have about growing your local service business.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact">
                  <Button variant="primary" size="lg" className="group">
                    <MessageCircle className="mr-2 h-5 w-5" />
                    Start a Conversation
                  </Button>
                </Link>
                
                <Button variant="outline" size="lg" className="group">
                  <Phone className="mr-2 h-5 w-5" />
                  Call (*************
                </Button>
              </div>
              
              <div className="mt-6 text-sm text-gray-600">
                <p>
                  Or email us directly at{' '}
                  <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700">
                    <EMAIL>
                  </a>
                </p>
              </div>
            </motion.div>
          </div>
        </Container>
      </Section>
    </>
  );
};
