import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'SEO for Roofing Companies | Dominate Local Search | GroundUPDigital',
  description: 'Dominate local search results with our specialized SEO services for roofing companies. Rank #1 for roof repair, roof replacement, and emergency roofing services.',
  keywords: 'roofing SEO, roof repair SEO, roofing contractor SEO, emergency roofing SEO, local roofing marketing',
  openGraph: {
    title: 'SEO for Roofing Companies | Dominate Local Search | GroundUPDigital',
    description: 'Dominate local search results with our specialized SEO services for roofing companies. Rank #1 for roof repair, roof replacement, and emergency roofing services.',
    type: 'website',
  },
};

export default function RoofingSEOPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              SEO for <span className="text-blue-600">Roofing Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Dominate local search results and attract more customers with our specialized SEO services 
              designed specifically for roofing contractors and emergency roofing services.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Free SEO Audit
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                View SEO Results
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Roofing SEO Results That Drive Business
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '#1', label: 'Local Search Rankings', icon: '🏆' },
                { metric: '500%', label: 'Increase in Emergency Calls', icon: '📞' },
                { metric: '90%', label: 'More Qualified Leads', icon: '🎯' },
                { metric: '4 Months', label: 'Average Time to Page 1', icon: '⏰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Roofing Keywords */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              High-Value Roofing Keywords We Target
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  category: 'Emergency Roofing',
                  keywords: ['emergency roof repair', 'roof leak repair', '24 hour roofer', 'emergency roofer near me', 'storm damage repair'],
                  searchVolume: '18,000+ monthly searches'
                },
                {
                  category: 'Roof Replacement',
                  keywords: ['roof replacement', 'new roof installation', 'roof replacement cost', 'roofing contractors', 'roof installation'],
                  searchVolume: '22,000+ monthly searches'
                },
                {
                  category: 'Roof Repair',
                  keywords: ['roof repair', 'roof repair near me', 'roof leak repair', 'shingle repair', 'roof maintenance'],
                  searchVolume: '35,000+ monthly searches'
                },
                {
                  category: 'Commercial Roofing',
                  keywords: ['commercial roofing', 'flat roof repair', 'commercial roof replacement', 'industrial roofing', 'TPO roofing'],
                  searchVolume: '8,500+ monthly searches'
                },
                {
                  category: 'Roof Inspection',
                  keywords: ['roof inspection', 'roof estimate', 'free roof inspection', 'roof assessment', 'roof evaluation'],
                  searchVolume: '12,000+ monthly searches'
                },
                {
                  category: 'Insurance Claims',
                  keywords: ['insurance roof claim', 'storm damage claim', 'hail damage roof', 'insurance roofing', 'roof claim help'],
                  searchVolume: '9,800+ monthly searches'
                }
              ].map((category, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{category.category}</h3>
                  <p className="text-sm text-blue-600 font-semibold mb-4">{category.searchVolume}</p>
                  <ul className="space-y-2">
                    {category.keywords.map((keyword, keywordIndex) => (
                      <li key={keywordIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        "{keyword}"
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Emergency SEO Strategy */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Emergency Roofing SEO Strategy
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Why Emergency SEO Matters:</h3>
                <ul className="space-y-4">
                  {[
                    '65% of roofing searches are for emergency services',
                    'Emergency calls convert 3x higher than general inquiries',
                    'Storm events can generate 500%+ increase in search volume',
                    'Emergency searchers are ready to hire immediately',
                    'Local emergency rankings drive the highest ROI'
                  ].map((stat, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{stat}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Our Emergency SEO Tactics:</h3>
                <div className="space-y-4">
                  {[
                    { tactic: 'Storm Response Pages', description: 'Dedicated pages for storm damage and emergency repairs' },
                    { tactic: '24/7 Service Optimization', description: 'Emphasize round-the-clock availability in content' },
                    { tactic: 'Local Emergency Keywords', description: 'Target "emergency roofer near me" variations' },
                    { tactic: 'Fast Response Messaging', description: 'Highlight quick response times and availability' },
                    { tactic: 'Insurance Integration', description: 'Optimize for insurance claim-related searches' }
                  ].map((item, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg shadow-lg">
                      <h4 className="font-semibold text-gray-900 mb-2">{item.tactic}</h4>
                      <p className="text-gray-600 text-sm">{item.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* SEO Strategy */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Comprehensive Roofing SEO Strategy
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  strategy: 'Local SEO Domination',
                  description: 'Dominate local search results for roofing services in your service area.',
                  tactics: ['Google My Business optimization', 'Local citation building', 'Location-based content', 'Service area targeting'],
                  icon: '📍'
                },
                {
                  strategy: 'Emergency Service SEO',
                  description: 'Capture high-intent emergency roofing searches when customers need help most.',
                  tactics: ['24/7 service optimization', 'Storm response content', 'Emergency keyword targeting', 'Fast response messaging'],
                  icon: '🚨'
                },
                {
                  strategy: 'Insurance Claim SEO',
                  description: 'Target customers dealing with insurance claims and storm damage.',
                  tactics: ['Insurance claim content', 'Storm damage pages', 'Claim process guides', 'Insurance partnership content'],
                  icon: '📋'
                },
                {
                  strategy: 'Technical SEO',
                  description: 'Ensure your website performs perfectly for both users and search engines.',
                  tactics: ['Site speed optimization', 'Mobile responsiveness', 'Schema markup', 'Core Web Vitals'],
                  icon: '⚙️'
                },
                {
                  strategy: 'Content Marketing',
                  description: 'Educational content that positions you as the local roofing expert.',
                  tactics: ['Roofing guides', 'Maintenance tips', 'Material comparisons', 'Cost breakdowns'],
                  icon: '📝'
                },
                {
                  strategy: 'Review & Reputation',
                  description: 'Build trust and authority through positive reviews and reputation management.',
                  tactics: ['Review generation campaigns', 'Review response management', 'Reputation monitoring', 'Trust signals'],
                  icon: '⭐'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg border-l-4 border-blue-600">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.strategy}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.tactics.map((tactic, tacticIndex) => (
                      <li key={tacticIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {tactic}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Storm Response SEO */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Storm Response SEO
              </h2>
              <p className="text-xl text-blue-100">
                Be the first roofing company customers find when storms hit your area.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Weather Monitoring',
                  description: 'Automated SEO campaigns triggered by severe weather in your service area.',
                  icon: '🌩️'
                },
                {
                  feature: 'Rapid Content Deployment',
                  description: 'Pre-built storm damage content that goes live immediately after weather events.',
                  icon: '⚡'
                },
                {
                  feature: 'Emergency Keyword Optimization',
                  description: 'Real-time optimization for emergency roofing keywords during storm events.',
                  icon: '🎯'
                }
              ].map((feature, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{feature.feature}</h3>
                  <p className="text-blue-100">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Roofing SEO Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Elite Roofing: From Page 2 to #1 in Emergency Search
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Elite Roofing was losing emergency calls to competitors and struggling with online visibility. 
                    Our emergency-focused SEO strategy transformed their digital presence.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">#1</div>
                      <div className="text-sm text-gray-600">Emergency Rankings</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">600%</div>
                      <div className="text-sm text-gray-600">Emergency Call Increase</div>
                    </div>
                  </div>
                  <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Results After 4 Months:</h4>
                  <ul className="space-y-3">
                    {[
                      'Ranking #1 for "emergency roof repair [city]"',
                      'Ranking #1 for "24 hour roofer near me"',
                      'Ranking #2 for "storm damage repair"',
                      '500% increase in emergency calls',
                      '85% increase in insurance claim jobs',
                      '4.8-star average Google rating'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Dominate Emergency Roofing Search?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Get a free SEO audit and discover how we can get your roofing business ranking #1 for emergency searches.
            </p>
            <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Get Your Free Roofing SEO Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
