import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types for our database tables
export interface ContactForm {
  id?: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  service_interest?: string;
  message: string;
  created_at?: string;
}

export interface BlogPost {
  id?: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featured_image?: string;
  published: boolean;
  created_at?: string;
  updated_at?: string;
  author?: string;
  tags?: string[];
}

export interface CaseStudy {
  id?: string;
  title: string;
  slug: string;
  client_name: string;
  industry: 'landscaping' | 'roofing' | 'pest-control';
  challenge: string;
  solution: string;
  results: string;
  featured_image?: string;
  before_image?: string;
  after_image?: string;
  metrics: {
    leads_increase?: number;
    traffic_increase?: number;
    conversion_rate?: number;
    roi?: number;
  };
  published: boolean;
  created_at?: string;
}

// Database functions
export const dbFunctions = {
  // Contact form submission
  async submitContactForm(data: ContactForm) {
    const { error } = await supabase
      .from('contact_forms')
      .insert([data]);
    
    if (error) throw error;
    return { success: true };
  },

  // Blog posts
  async getBlogPosts(limit?: number) {
    let query = supabase
      .from('blog_posts')
      .select('*')
      .eq('published', true)
      .order('created_at', { ascending: false });
    
    if (limit) {
      query = query.limit(limit);
    }
    
    const { data, error } = await query;
    if (error) throw error;
    return data;
  },

  async getBlogPost(slug: string) {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('slug', slug)
      .eq('published', true)
      .single();
    
    if (error) throw error;
    return data;
  },

  // Case studies
  async getCaseStudies(industry?: string, limit?: number) {
    let query = supabase
      .from('case_studies')
      .select('*')
      .eq('published', true)
      .order('created_at', { ascending: false });
    
    if (industry) {
      query = query.eq('industry', industry);
    }
    
    if (limit) {
      query = query.limit(limit);
    }
    
    const { data, error } = await query;
    if (error) throw error;
    return data;
  },

  async getCaseStudy(slug: string) {
    const { data, error } = await supabase
      .from('case_studies')
      .select('*')
      .eq('slug', slug)
      .eq('published', true)
      .single();
    
    if (error) throw error;
    return data;
  }
};
