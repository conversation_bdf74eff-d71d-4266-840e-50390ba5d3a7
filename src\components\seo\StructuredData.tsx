import React from 'react';

interface StructuredDataProps {
  data: any;
}

export const StructuredData: React.FC<StructuredDataProps> = ({ data }) => {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(data, null, 2)
      }}
    />
  );
};

interface SEOHeadProps {
  structuredData?: any[];
  children?: React.ReactNode;
}

export const SEOHead: React.FC<SEOHeadProps> = ({ structuredData = [], children }) => {
  return (
    <>
      {children}
      {structuredData.map((data, index) => (
        <StructuredData key={index} data={data} />
      ))}
    </>
  );
};

// Breadcrumb Schema Component
interface BreadcrumbItem {
  name: string;
  url: string;
}

interface BreadcrumbSchemaProps {
  items: BreadcrumbItem[];
}

export const BreadcrumbSchema: React.FC<BreadcrumbSchemaProps> = ({ items }) => {
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  };

  return <StructuredData data={breadcrumbSchema} />;
};

// FAQ Schema Component
interface FAQItem {
  question: string;
  answer: string;
}

interface FAQSchemaProps {
  faqs: FAQItem[];
}

export const FAQSchema: React.FC<FAQSchemaProps> = ({ faqs }) => {
  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };

  return <StructuredData data={faqSchema} />;
};

// Article Schema Component
interface ArticleSchemaProps {
  title: string;
  description: string;
  author: string;
  datePublished: string;
  dateModified?: string;
  image: string;
  url: string;
}

export const ArticleSchema: React.FC<ArticleSchemaProps> = ({
  title,
  description,
  author,
  datePublished,
  dateModified,
  image,
  url
}) => {
  const articleSchema = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": title,
    "description": description,
    "image": image,
    "author": {
      "@type": "Person",
      "name": author
    },
    "publisher": {
      "@type": "Organization",
      "name": "GroundUPDigital",
      "logo": {
        "@type": "ImageObject",
        "url": "https://groundupdigital.com/logo.png"
      }
    },
    "datePublished": datePublished,
    "dateModified": dateModified || datePublished,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": url
    }
  };

  return <StructuredData data={articleSchema} />;
};

// Product/Service Schema Component
interface ServiceSchemaProps {
  name: string;
  description: string;
  provider: string;
  areaServed: string[];
  offers?: {
    price?: string;
    priceCurrency?: string;
    availability?: string;
  };
}

export const ServiceSchema: React.FC<ServiceSchemaProps> = ({
  name,
  description,
  provider,
  areaServed,
  offers
}) => {
  const serviceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": name,
    "description": description,
    "provider": {
      "@type": "LocalBusiness",
      "name": provider,
      "url": "https://groundupdigital.com"
    },
    "areaServed": areaServed.map(area => ({
      "@type": "City",
      "name": area
    })),
    ...(offers && {
      "offers": {
        "@type": "Offer",
        "price": offers.price,
        "priceCurrency": offers.priceCurrency || "USD",
        "availability": offers.availability || "https://schema.org/InStock"
      }
    })
  };

  return <StructuredData data={serviceSchema} />;
};

// Organization Schema Component
export const OrganizationSchema: React.FC = () => {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "@id": "https://groundupdigital.com#organization",
    "name": "GroundUPDigital",
    "url": "https://groundupdigital.com",
    "logo": "https://groundupdigital.com/logo.png",
    "description": "Professional digital marketing agency specializing in web development, SEO, and lead generation for local service providers.",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "123 Digital Drive",
      "addressLocality": "Austin",
      "addressRegion": "TX",
      "postalCode": "78701",
      "addressCountry": "US"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-123-4567",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": "English"
    },
    "sameAs": [
      "https://www.facebook.com/groundupdigital",
      "https://www.linkedin.com/company/groundupdigital",
      "https://twitter.com/groundupdigital",
      "https://www.instagram.com/groundupdigital"
    ],
    "foundingDate": "2020-01-15",
    "numberOfEmployees": "10-50",
    "knowsAbout": [
      "Digital Marketing",
      "Web Development",
      "Search Engine Optimization",
      "Lead Generation",
      "Social Media Marketing",
      "Pay-Per-Click Advertising"
    ]
  };

  return <StructuredData data={organizationSchema} />;
};

// Website Schema Component
export const WebsiteSchema: React.FC = () => {
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "@id": "https://groundupdigital.com#website",
    "url": "https://groundupdigital.com",
    "name": "GroundUPDigital",
    "description": "Professional digital marketing services for local service providers",
    "publisher": {
      "@id": "https://groundupdigital.com#organization"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://groundupdigital.com/search?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    }
  };

  return <StructuredData data={websiteSchema} />;
};
