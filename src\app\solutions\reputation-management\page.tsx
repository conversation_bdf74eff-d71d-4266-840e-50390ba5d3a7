import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, Shield, Star, TrendingUp, MessageSquare, CheckCircle, Phone, AlertTriangle } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Reputation Management for Landscaping Businesses | GroundUP Digital',
  description: 'Protect and enhance your landscaping business reputation online. Generate more positive reviews and manage your online presence professionally.',
  keywords: 'landscaping reputation management, landscaping business reviews, online reputation landscaping, lawn care review management',
};

const reputationFeatures = [
  {
    icon: Star,
    title: 'Review Generation',
    description: 'Systematically generate more positive reviews from your satisfied landscaping customers.',
    benefits: ['Automated review requests', 'Multi-platform review generation', 'Customer follow-up sequences', 'Review incentive programs']
  },
  {
    icon: Shield,
    title: 'Reputation Protection',
    description: 'Monitor and protect your landscaping business reputation across all online platforms.',
    benefits: ['24/7 reputation monitoring', 'Negative review alerts', 'Crisis management protocols', 'Brand protection strategies']
  },
  {
    icon: MessageSquare,
    title: 'Professional Response Management',
    description: 'Expert responses to all reviews that maintain your professional image.',
    benefits: ['Professional review responses', 'Damage control strategies', 'Customer service recovery', 'Brand voice consistency']
  }
];

const platforms = [
  {
    name: 'Google Business Profile',
    description: 'The most important platform for local landscaping businesses',
    features: ['Review monitoring', 'Response management', 'Profile optimization', 'Local SEO benefits']
  },
  {
    name: 'Facebook Reviews',
    description: 'Social proof that influences customer decisions',
    features: ['Social media monitoring', 'Community management', 'Review generation', 'Engagement tracking']
  },
  {
    name: 'Angie\'s List / Angi',
    description: 'Trusted platform for home service recommendations',
    features: ['Profile management', 'Lead generation', 'Review optimization', 'Competitive analysis']
  },
  {
    name: 'Better Business Bureau',
    description: 'Credibility and trust building for your landscaping business',
    features: ['Accreditation support', 'Complaint resolution', 'Rating improvement', 'Trust building']
  }
];

const packages = [
  {
    name: "Reputation Foundation",
    price: "$497",
    period: "/month",
    description: "Essential reputation management for landscaping businesses starting their online presence",
    features: [
      "Google Business Profile management",
      "Basic review monitoring",
      "Professional review responses",
      "Monthly reputation reports",
      "Review generation system setup"
    ],
    popular: false
  },
  {
    name: "Reputation Builder",
    price: "$897",
    period: "/month", 
    description: "Comprehensive reputation management for growing landscaping companies",
    features: [
      "Multi-platform monitoring",
      "Advanced review generation",
      "Crisis management support",
      "Competitor reputation analysis",
      "Customer service recovery",
      "Quarterly strategy reviews"
    ],
    popular: true
  },
  {
    name: "Reputation Leader",
    price: "$1,497",
    period: "/month",
    description: "Premium reputation management for established landscaping businesses",
    features: [
      "Full reputation ecosystem management",
      "Proactive reputation building",
      "Executive reputation management",
      "Industry thought leadership",
      "Media relations support",
      "Dedicated reputation manager"
    ],
    popular: false
  }
];

const caseStudy = {
  company: "Elite Outdoor Solutions",
  location: "Miami, FL",
  results: [
    { metric: "Google Rating", improvement: "3.2 → 4.8 stars" },
    { metric: "Total Reviews", improvement: "+340%" },
    { metric: "Positive Reviews", improvement: "+420%" },
    { metric: "Review-Driven Leads", improvement: "+195%" }
  ]
};

const reputationStats = [
  { stat: "92%", description: "of customers read online reviews before hiring" },
  { stat: "4.0+", description: "star rating needed to be considered by most customers" },
  { stat: "68%", description: "more likely to hire with positive reviews" },
  { stat: "15%", description: "revenue increase per star rating improvement" }
];

export default function ReputationManagementPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Shield className="w-4 h-4" />
              <span className="text-sm font-semibold">Reputation Management for Landscaping Businesses</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Protect & Enhance Your{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscaping Business Reputation
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Professional reputation management that helps landscaping business owners build trust, 
              generate more positive reviews, and protect their online presence.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Star className="w-5 h-5 mr-2" />
                Get Reputation Audit
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                View Success Stories
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '4.8★', label: 'Avg Client Rating' },
                { number: '340%', label: 'Review Increase' },
                { number: '100+', label: 'Businesses Protected' },
                { number: '24/7', label: 'Monitoring' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Why Reputation Matters */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Why Online Reputation Matters for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              Your online reputation directly impacts your landscaping business success. 
              Customers research landscaping companies online before making hiring decisions.
            </p>
          </div>
          
          <div className="grid md:grid-cols-4 gap-6">
            {reputationStats.map((item, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-6">
                  <div className="text-3xl font-bold text-blue-600 mb-2">{item.stat}</div>
                  <p className="text-sm text-gray-600">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Comprehensive Reputation Management for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              We provide complete reputation management services specifically designed for landscaping business owners. 
              Protect your reputation and attract more customers with our proven strategies.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {reputationFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Platforms Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              We Monitor All the Platforms That Matter
            </h2>
            <p className="text-lg text-gray-600">
              Comprehensive monitoring and management across all major review platforms where your landscaping customers leave feedback.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {platforms.map((platform, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{platform.name}</h3>
                  <p className="text-gray-600 mb-4">{platform.description}</p>
                  <ul className="space-y-2">
                    {platform.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Real Reputation Recovery Success
              </h2>
              <p className="text-lg text-gray-600">
                See how we helped {caseStudy.company} recover from reputation damage and build a stellar online presence
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A landscaping business in {caseStudy.location} struggling with negative reviews and low ratings. 
                      Our comprehensive reputation management strategy helped them rebuild trust and attract quality customers.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Read Full Case Study
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-lg font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Reputation Management Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Professional reputation management services designed specifically for landscaping business owners. 
              Protect your reputation and build customer trust.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Protect Your Landscaping Business Reputation?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a free reputation audit and discover exactly how to build trust and attract more customers for your landscaping business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Star className="w-5 h-5 mr-2" />
                Get Free Reputation Audit
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule Consultation
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
