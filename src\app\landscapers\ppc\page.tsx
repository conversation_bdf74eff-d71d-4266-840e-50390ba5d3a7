import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'PPC Advertising for Landscaping Companies | Generate Immediate Leads | GroundUPDigital',
  description: 'Drive immediate leads with targeted PPC campaigns for landscaping companies. Get more customers with our proven Google Ads strategies for lawn care and landscape design.',
  keywords: 'landscaping PPC, lawn care advertising, landscape design ads, Google Ads for landscapers, landscaping lead generation',
  openGraph: {
    title: 'PPC Advertising for Landscaping Companies | Generate Immediate Leads | GroundUPDigital',
    description: 'Drive immediate leads with targeted PPC campaigns for landscaping companies. Get more customers with our proven Google Ads strategies for lawn care and landscape design.',
    type: 'website',
  },
};

export default function LandscapingPPCPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-blue-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              PPC Advertising for <span className="text-green-600">Landscaping Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Generate immediate leads and grow your landscaping business with our targeted PPC campaigns. 
              Get more customers for lawn care, landscape design, and tree services with proven Google Ads strategies.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Get Free PPC Audit
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View Campaign Results
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Landscaping PPC Results That Drive Business
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '$3.50', label: 'Average Cost Per Lead', icon: '💰' },
                { metric: '450%', label: 'Return on Ad Spend', icon: '📈' },
                { metric: '18%', label: 'Average Conversion Rate', icon: '🎯' },
                { metric: '24hrs', label: 'Time to First Lead', icon: '⏰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-green-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Campaign Types */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Landscaping PPC Campaign Types
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  campaign: 'Lawn Care Campaigns',
                  description: 'Target homeowners searching for regular lawn maintenance and care services.',
                  keywords: ['lawn care near me', 'lawn mowing service', 'lawn maintenance', 'grass cutting'],
                  icon: '🌱'
                },
                {
                  campaign: 'Landscape Design Campaigns',
                  description: 'Capture high-value customers looking for complete landscape transformations.',
                  keywords: ['landscape design', 'landscaping ideas', 'garden design', 'outdoor design'],
                  icon: '🎨'
                },
                {
                  campaign: 'Tree Service Campaigns',
                  description: 'Target urgent tree removal and trimming service searches.',
                  keywords: ['tree removal', 'tree trimming', 'tree cutting', 'arborist near me'],
                  icon: '🌳'
                },
                {
                  campaign: 'Hardscaping Campaigns',
                  description: 'Reach customers planning patios, walkways, and outdoor living spaces.',
                  keywords: ['patio installation', 'retaining wall', 'walkway design', 'outdoor kitchen'],
                  icon: '🪨'
                },
                {
                  campaign: 'Irrigation Campaigns',
                  description: 'Target property owners needing sprinkler systems and irrigation solutions.',
                  keywords: ['sprinkler installation', 'irrigation system', 'sprinkler repair', 'drip irrigation'],
                  icon: '💧'
                },
                {
                  campaign: 'Seasonal Campaigns',
                  description: 'Capitalize on seasonal demand for cleanup and preparation services.',
                  keywords: ['spring cleanup', 'fall cleanup', 'leaf removal', 'winter prep'],
                  icon: '🍂'
                }
              ].map((campaign, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{campaign.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{campaign.campaign}</h3>
                  <p className="text-gray-600 mb-4">{campaign.description}</p>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-700 mb-2">Target Keywords:</h4>
                    <ul className="space-y-1">
                      {campaign.keywords.map((keyword, keywordIndex) => (
                        <li key={keywordIndex} className="text-sm text-gray-600">
                          "{keyword}"
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Seasonal Strategy */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seasonal PPC Strategy for Landscapers
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  season: 'Spring',
                  focus: 'Cleanup & Design',
                  strategy: 'Target spring cleanup and new landscape design projects',
                  bidAdjustment: '+40%',
                  icon: '🌸'
                },
                {
                  season: 'Summer',
                  focus: 'Maintenance & Irrigation',
                  strategy: 'Focus on lawn care maintenance and irrigation installation',
                  bidAdjustment: '+20%',
                  icon: '☀️'
                },
                {
                  season: 'Fall',
                  focus: 'Cleanup & Preparation',
                  strategy: 'Promote fall cleanup and winter preparation services',
                  bidAdjustment: '+30%',
                  icon: '🍂'
                },
                {
                  season: 'Winter',
                  focus: 'Planning & Design',
                  strategy: 'Target planning for next year and indoor consultations',
                  bidAdjustment: '-20%',
                  icon: '❄️'
                }
              ].map((season, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                  <div className="text-4xl mb-4">{season.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{season.season}</h3>
                  <h4 className="text-lg font-medium text-green-600 mb-3">{season.focus}</h4>
                  <p className="text-gray-600 mb-4">{season.strategy}</p>
                  <div className="bg-gray-100 rounded-lg p-3">
                    <span className="text-sm font-semibold text-gray-700">Bid Adjustment: </span>
                    <span className="text-sm font-bold text-green-600">{season.bidAdjustment}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Landing Page Optimization */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              High-Converting Landing Pages for Landscapers
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Landing Page Elements That Convert:</h3>
                <ul className="space-y-4">
                  {[
                    'Clear headline matching the ad copy and search intent',
                    'High-quality before/after photos of your work',
                    'Prominent phone number and contact form',
                    'Service area map and coverage information',
                    'Customer testimonials and 5-star reviews',
                    'Free estimate or consultation offer',
                    'Trust signals: licenses, insurance, certifications',
                    'Mobile-optimized design for on-the-go searches'
                  ].map((element, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{element}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Conversion Optimization Results:</h3>
                <div className="space-y-6">
                  {[
                    { metric: '35%', description: 'Higher conversion rate with optimized landing pages', icon: '📈' },
                    { metric: '60%', description: 'More phone calls from mobile users', icon: '📞' },
                    { metric: '45%', description: 'Increase in form submissions', icon: '📋' },
                    { metric: '25%', description: 'Lower cost per conversion', icon: '💰' }
                  ].map((result, index) => (
                    <div key={index} className="flex items-center bg-white p-4 rounded-lg shadow-lg">
                      <div className="text-3xl mr-4">{result.icon}</div>
                      <div>
                        <div className="text-2xl font-bold text-green-600">{result.metric}</div>
                        <div className="text-gray-600">{result.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Local Targeting */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Precision Local Targeting for Landscapers
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  targeting: 'Geographic Targeting',
                  description: 'Target specific cities, zip codes, and radius around your service area.',
                  features: ['City-level targeting', 'Zip code precision', 'Radius targeting', 'Competitor exclusion']
                },
                {
                  targeting: 'Demographic Targeting',
                  description: 'Reach homeowners most likely to need landscaping services.',
                  features: ['Homeowner targeting', 'Income-based targeting', 'Age demographics', 'Household composition']
                },
                {
                  targeting: 'Behavioral Targeting',
                  description: 'Target users based on their online behavior and interests.',
                  features: ['Home improvement interests', 'Gardening enthusiasts', 'Property investors', 'Recent movers']
                }
              ].map((target, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{target.targeting}</h3>
                  <p className="text-gray-600 mb-6">{target.description}</p>
                  <ul className="space-y-3">
                    {target.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Landscaping PPC Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    GreenScape Landscaping: 500% ROI in 90 Days
                  </h3>
                  <p className="text-gray-600 mb-6">
                    GreenScape Landscaping was struggling with inconsistent lead flow and high customer acquisition costs. 
                    Our targeted PPC campaigns transformed their lead generation and business growth.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">500%</div>
                      <div className="text-sm text-gray-600">Return on Ad Spend</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">$2.80</div>
                      <div className="text-sm text-gray-600">Cost Per Lead</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Campaign Results:</h4>
                  <ul className="space-y-3">
                    {[
                      '150+ qualified leads per month',
                      '18% average conversion rate',
                      '$85,000 additional revenue in 6 months',
                      '65% reduction in cost per acquisition',
                      '4.8-star average customer rating',
                      '40% increase in repeat customers'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Generate Immediate Landscaping Leads?
            </h2>
            <p className="text-xl text-green-100 mb-8">
              Get a free PPC audit and discover how we can drive qualified leads to your landscaping business starting today.
            </p>
            <button className="bg-white text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Get Your Free Landscaping PPC Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
