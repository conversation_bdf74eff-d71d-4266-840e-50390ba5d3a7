'use client';

import React from 'react';
import { useHydration } from '@/hooks/useHydration';

interface ClientOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * ClientOnly component that only renders children after hydration
 * This prevents hydration mismatches for client-side only components
 */
export const ClientOnly: React.FC<ClientOnlyProps> = ({ 
  children, 
  fallback = null 
}) => {
  const isMounted = useHydration();

  if (!isMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};
