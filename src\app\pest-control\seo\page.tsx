import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'SEO for Pest Control Companies | Dominate Local Search | GroundUPDigital',
  description: 'Dominate local search results with our specialized SEO services for pest control companies. Rank #1 for exterminator, pest control, and termite treatment services.',
  keywords: 'pest control SEO, exterminator SEO, termite treatment SEO, pest management SEO, local pest control marketing',
  openGraph: {
    title: 'SEO for Pest Control Companies | Dominate Local Search | GroundUPDigital',
    description: 'Dominate local search results with our specialized SEO services for pest control companies. Rank #1 for exterminator, pest control, and termite treatment services.',
    type: 'website',
  },
};

export default function PestControlSEOPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-yellow-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              SEO for <span className="text-green-600">Pest Control Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Dominate local search results and attract more customers with our specialized SEO services 
              designed specifically for pest control, extermination, and pest management businesses.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Get Free SEO Audit
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View SEO Results
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Pest Control SEO Results That Drive Business
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '#1', label: 'Local Search Rankings', icon: '🏆' },
                { metric: '550%', label: 'Increase in Emergency Calls', icon: '📞' },
                { metric: '95%', label: 'More Qualified Leads', icon: '🎯' },
                { metric: '3 Months', label: 'Average Time to Page 1', icon: '⏰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-green-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Pest Control Keywords */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              High-Value Pest Control Keywords We Target
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  category: 'General Pest Control',
                  keywords: ['pest control near me', 'exterminator near me', 'pest control service', 'bug exterminator', 'pest removal'],
                  searchVolume: '45,000+ monthly searches'
                },
                {
                  category: 'Termite Services',
                  keywords: ['termite treatment', 'termite inspection', 'termite exterminator', 'termite control', 'termite removal'],
                  searchVolume: '28,000+ monthly searches'
                },
                {
                  category: 'Rodent Control',
                  keywords: ['mouse exterminator', 'rat control', 'rodent removal', 'mice control', 'rat exterminator'],
                  searchVolume: '32,000+ monthly searches'
                },
                {
                  category: 'Bed Bug Treatment',
                  keywords: ['bed bug exterminator', 'bed bug treatment', 'bed bug removal', 'bed bug control', 'bed bug inspection'],
                  searchVolume: '18,500+ monthly searches'
                },
                {
                  category: 'Ant Control',
                  keywords: ['ant exterminator', 'ant control', 'ant removal', 'carpenter ant treatment', 'fire ant control'],
                  searchVolume: '22,000+ monthly searches'
                },
                {
                  category: 'Wildlife Removal',
                  keywords: ['wildlife removal', 'animal control', 'raccoon removal', 'squirrel removal', 'bat removal'],
                  searchVolume: '15,800+ monthly searches'
                }
              ].map((category, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{category.category}</h3>
                  <p className="text-sm text-green-600 font-semibold mb-4">{category.searchVolume}</p>
                  <ul className="space-y-2">
                    {category.keywords.map((keyword, keywordIndex) => (
                      <li key={keywordIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        "{keyword}"
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Seasonal SEO Strategy */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seasonal Pest Control SEO Strategy
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  season: 'Spring',
                  pests: ['Ants', 'Termites', 'Wasps', 'Spiders'],
                  strategy: 'Prevention campaigns and early treatment promotions',
                  searchVolume: '+200% increase',
                  icon: '🌸'
                },
                {
                  season: 'Summer',
                  pests: ['Mosquitoes', 'Flies', 'Wasps', 'Ticks'],
                  strategy: 'Outdoor pest control and family safety messaging',
                  searchVolume: '+150% increase',
                  icon: '☀️'
                },
                {
                  season: 'Fall',
                  pests: ['Rodents', 'Stink Bugs', 'Spiders', 'Cluster Flies'],
                  strategy: 'Winter preparation and home sealing services',
                  searchVolume: '+180% increase',
                  icon: '🍂'
                },
                {
                  season: 'Winter',
                  pests: ['Mice', 'Rats', 'Cockroaches', 'Silverfish'],
                  strategy: 'Indoor pest control and maintenance contracts',
                  searchVolume: '+120% increase',
                  icon: '❄️'
                }
              ].map((season, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                  <div className="text-4xl mb-4">{season.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{season.season}</h3>
                  <p className="text-sm text-green-600 font-semibold mb-4">{season.searchVolume}</p>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-700 mb-2">Target Pests:</h4>
                    <ul className="text-sm text-gray-600">
                      {season.pests.map((pest, pestIndex) => (
                        <li key={pestIndex}>{pest}</li>
                      ))}
                    </ul>
                  </div>
                  <p className="text-sm text-gray-600">{season.strategy}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* SEO Strategy */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Comprehensive Pest Control SEO Strategy
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  strategy: 'Local SEO Domination',
                  description: 'Dominate local search results for pest control services in your service area.',
                  tactics: ['Google My Business optimization', 'Local citation building', 'Location-based content', 'Service area targeting'],
                  icon: '📍'
                },
                {
                  strategy: 'Emergency Service SEO',
                  description: 'Capture high-intent emergency pest control searches when customers need help most.',
                  tactics: ['24/7 service optimization', 'Emergency pest content', 'Urgent keyword targeting', 'Fast response messaging'],
                  icon: '🚨'
                },
                {
                  strategy: 'Pest-Specific Pages',
                  description: 'Dedicated pages for each type of pest and treatment service you offer.',
                  tactics: ['Individual pest pages', 'Treatment method content', 'Prevention guides', 'Identification resources'],
                  icon: '🐛'
                },
                {
                  strategy: 'Seasonal Optimization',
                  description: 'Optimize for seasonal pest trends and demand patterns throughout the year.',
                  tactics: ['Seasonal content calendars', 'Weather-triggered campaigns', 'Pest activity tracking', 'Prevention timing'],
                  icon: '🍂'
                },
                {
                  strategy: 'Educational Content',
                  description: 'Position yourself as the local pest control expert with valuable educational content.',
                  tactics: ['Pest identification guides', 'Prevention tips', 'Treatment explanations', 'Safety information'],
                  icon: '📝'
                },
                {
                  strategy: 'Review & Trust Building',
                  description: 'Build trust and credibility through positive reviews and safety certifications.',
                  tactics: ['Review generation campaigns', 'Safety certification display', 'Before/after showcases', 'Customer testimonials'],
                  icon: '⭐'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg border-l-4 border-green-600">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.strategy}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.tactics.map((tactic, tacticIndex) => (
                      <li key={tacticIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {tactic}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Emergency Pest SEO */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Emergency Pest Control SEO
              </h2>
              <p className="text-xl text-green-100">
                Be the first pest control company customers call when they have urgent pest problems.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Urgent Keyword Targeting',
                  description: 'Optimize for high-intent emergency pest control searches with immediate need.',
                  icon: '🎯'
                },
                {
                  feature: '24/7 Service Optimization',
                  description: 'Emphasize round-the-clock availability for emergency pest situations.',
                  icon: '🕐'
                },
                {
                  feature: 'Fast Response Messaging',
                  description: 'Highlight quick response times and same-day service availability.',
                  icon: '⚡'
                }
              ].map((feature, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{feature.feature}</h3>
                  <p className="text-green-100">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Pest Control SEO Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Guardian Pest Control: From Invisible to #1
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Guardian Pest Control was struggling to compete with national chains and was barely visible 
                    in local search results. Our comprehensive SEO strategy transformed their online presence.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">#1</div>
                      <div className="text-sm text-gray-600">Local Rankings</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">750%</div>
                      <div className="text-sm text-gray-600">Lead Increase</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Results After 3 Months:</h4>
                  <ul className="space-y-3">
                    {[
                      'Ranking #1 for "pest control [city]"',
                      'Ranking #1 for "exterminator near me"',
                      'Ranking #2 for "termite treatment"',
                      '650% increase in phone calls',
                      '85% increase in recurring customers',
                      '4.9-star average Google rating'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Local SEO Focus */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Local SEO for Pest Control Companies
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Why Local SEO is Critical:</h3>
                <ul className="space-y-4">
                  {[
                    '89% of pest control searches include location terms',
                    '76% of customers choose pest control companies within 10 miles',
                    'Local searches convert 5x higher than general searches',
                    '92% of customers read reviews before hiring pest control',
                    'Emergency pest searches are 90% local-focused'
                  ].map((stat, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{stat}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Our Local SEO Process:</h3>
                <div className="space-y-6">
                  {[
                    { step: '1', title: 'Local Market Analysis', description: 'Analyze local pest control competition and opportunities.' },
                    { step: '2', title: 'Google My Business Optimization', description: 'Optimize your GMB profile for maximum local visibility.' },
                    { step: '3', title: 'Local Citation Building', description: 'Build consistent NAP citations across pest control directories.' },
                    { step: '4', title: 'Location-Based Content', description: 'Create content targeting specific neighborhoods and pest issues.' },
                    { step: '5', title: 'Review Management', description: 'Generate and manage customer reviews for local authority.' }
                  ].map((process, index) => (
                    <div key={index} className="flex items-start">
                      <div className="bg-green-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-4 flex-shrink-0">
                        {process.step}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">{process.title}</h4>
                        <p className="text-gray-600 text-sm">{process.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Dominate Local Pest Control Search?
            </h2>
            <p className="text-xl text-green-100 mb-8">
              Get a free SEO audit and discover how we can get your pest control business ranking #1 in local search results.
            </p>
            <button className="bg-white text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Get Your Free Pest Control SEO Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
