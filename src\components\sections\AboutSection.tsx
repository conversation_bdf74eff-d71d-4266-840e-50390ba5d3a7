'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Award, Users, Clock, Target, CheckCircle, Star, TrendingUp, Shield } from 'lucide-react';
import { Section } from '@/components/ui/Section';
import { GlassCard, GradientCard } from '@/components/ui/GlassCard';
import { Counter } from '@/components/ui/InteractiveElements';

const certifications = [
  {
    name: "Google Partner",
    icon: "🏆",
    description: "Certified Google Ads and Analytics Partner"
  },
  {
    name: "Facebook Marketing Partner",
    icon: "📘",
    description: "Meta Business Partner for advertising excellence"
  },
  {
    name: "HubSpot Certified",
    icon: "🎯",
    description: "Inbound marketing and sales certification"
  },
  {
    name: "Bing Ads Accredited",
    icon: "🔍",
    description: "Microsoft Advertising certified professional"
  }
];

const teamMembers = [
  {
    name: "<PERSON>",
    role: "Founder & CEO",
    experience: "12+ years",
    specialization: "Digital Strategy & Business Growth",
    credentials: "MBA Marketing, Google Ads Certified",
    image: "/team/sarah-johnson.jpg"
  },
  {
    name: "<PERSON>",
    role: "Lead Developer",
    experience: "8+ years",
    specialization: "Web Development & Technical SEO",
    credentials: "Full-Stack Developer, AWS Certified",
    image: "/team/mike-chen.jpg"
  },
  {
    name: "<PERSON>",
    role: "SEO Director",
    experience: "10+ years",
    specialization: "Local SEO & Content Strategy",
    credentials: "SEMrush Certified, Moz Pro Certified",
    image: "/team/emily-rodriguez.jpg"
  },
  {
    name: "David Thompson",
    role: "PPC Specialist",
    experience: "6+ years",
    specialization: "Paid Advertising & Analytics",
    credentials: "Google Ads Expert, Facebook Blueprint",
    image: "/team/david-thompson.jpg"
  }
];

const companyStats = [
  {
    number: 500,
    suffix: "+",
    label: "Successful Projects",
    icon: Target
  },
  {
    number: 98,
    suffix: "%",
    label: "Client Satisfaction",
    icon: Star
  },
  {
    number: 250,
    suffix: "%",
    label: "Average ROI Increase",
    icon: TrendingUp
  },
  {
    number: 5,
    suffix: "",
    label: "Years of Excellence",
    icon: Award
  }
];

export const AboutSection: React.FC = () => {
  return (
    <Section id="about" className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            About <span className="gradient-text-blue">GroundUPDigital</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Founded in 2020, GroundUPDigital has become the trusted digital marketing partner for 
            local service providers across Texas. We specialize in helping landscapers, roofers, 
            and pest control companies dominate their local markets through proven digital strategies.
          </p>
        </motion.div>

        {/* Company Story */}
        <div className="grid lg:grid-cols-2 gap-12 mb-20">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <GradientCard gradient="blue" className="h-full">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <Shield className="mr-3 h-8 w-8 text-blue-600" />
                Our Mission
              </h3>
              <p className="text-gray-700 text-lg leading-relaxed mb-6">
                To empower local service providers with cutting-edge digital marketing solutions 
                that drive measurable growth. We believe every local business deserves to thrive 
                in the digital landscape, regardless of their size or technical expertise.
              </p>
              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-gray-700">Transparent, results-driven approach</p>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-gray-700">Industry-specific expertise and knowledge</p>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-gray-700">Dedicated support and partnership</p>
                </div>
              </div>
            </GradientCard>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <GradientCard gradient="green" className="h-full">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <Target className="mr-3 h-8 w-8 text-green-600" />
                Why Choose Us
              </h3>
              <p className="text-gray-700 text-lg leading-relaxed mb-6">
                Unlike generic marketing agencies, we focus exclusively on local service providers. 
                This specialization allows us to deliver highly targeted strategies that understand 
                your unique challenges and opportunities.
              </p>
              <div className="space-y-4">
                <div className="flex items-start">
                  <Award className="h-6 w-6 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-gray-700">Certified experts in Google, Facebook, and industry tools</p>
                </div>
                <div className="flex items-start">
                  <Users className="h-6 w-6 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-gray-700">Dedicated account management and support</p>
                </div>
                <div className="flex items-start">
                  <Clock className="h-6 w-6 text-blue-500 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-gray-700">Proven track record with 500+ successful projects</p>
                </div>
              </div>
            </GradientCard>
          </motion.div>
        </div>

        {/* Company Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-20"
        >
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {companyStats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <GlassCard className="text-center hover:scale-105 transition-transform duration-300">
                  <stat.icon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <div className="text-4xl font-bold text-gray-900 mb-2">
                    <Counter end={stat.number} suffix={stat.suffix} />
                  </div>
                  <p className="text-gray-600 font-medium">{stat.label}</p>
                </GlassCard>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Certifications */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="mb-20"
        >
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Our Certifications & Partnerships
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {certifications.map((cert, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <GlassCard className="text-center h-full hover:scale-105 transition-transform duration-300">
                  <div className="text-4xl mb-4">{cert.icon}</div>
                  <h4 className="text-lg font-bold text-gray-900 mb-2">{cert.name}</h4>
                  <p className="text-gray-600 text-sm">{cert.description}</p>
                </GlassCard>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Team Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Meet Our Expert Team
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <GlassCard className="text-center h-full hover:scale-105 transition-transform duration-300">
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-green-500 rounded-full mx-auto mb-4 flex items-center justify-center text-white text-2xl font-bold">
                    {member.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 mb-1">{member.name}</h4>
                  <p className="text-blue-600 font-medium mb-2">{member.role}</p>
                  <p className="text-sm text-gray-600 mb-2">{member.experience}</p>
                  <p className="text-sm text-gray-700 mb-3">{member.specialization}</p>
                  <p className="text-xs text-gray-500">{member.credentials}</p>
                </GlassCard>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </Section>
  );
};
