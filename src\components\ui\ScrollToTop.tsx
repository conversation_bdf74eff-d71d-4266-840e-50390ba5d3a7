'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useHydration } from '@/hooks/useHydration';
import { ChevronUp, ArrowUp, Rocket } from 'lucide-react';

interface ScrollToTopProps {
  variant?: 'default' | 'rocket' | 'arrow';
  showAfter?: number;
  smooth?: boolean;
}

export const ScrollToTop: React.FC<ScrollToTopProps> = ({
  variant = 'default',
  showAfter = 300,
  smooth = true
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const isMounted = useHydration();

  useEffect(() => {
    if (!isMounted) return;

    const toggleVisibility = () => {
      const scrolled = document.documentElement.scrollTop;
      const maxHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
      const progress = (scrolled / maxHeight) * 100;

      setScrollProgress(progress);
      setIsVisible(scrolled > showAfter);
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, [showAfter, isMounted]);

  const scrollToTop = () => {
    if (smooth) {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    } else {
      window.scrollTo(0, 0);
    }
  };

  const getIcon = () => {
    switch (variant) {
      case 'rocket':
        return <Rocket className="w-6 h-6" />;
      case 'arrow':
        return <ArrowUp className="w-6 h-6" />;
      default:
        return <ChevronUp className="w-6 h-6" />;
    }
  };

  const getButtonStyles = () => {
    const baseStyles = "p-4 rounded-full shadow-lg transition-all duration-300 cursor-pointer select-none scroll-glow touch-target";

    switch (variant) {
      case 'rocket':
        return `${baseStyles} bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white`;
      case 'arrow':
        return `${baseStyles} bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white`;
      default:
        return `${baseStyles} bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white`;
    }
  };

  // Don't render on server to avoid hydration mismatch
  if (!isMounted) {
    return null;
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0, y: 100 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0, y: 100 }}
          transition={{ 
            duration: 0.3,
            ease: 'easeOut'
          }}
          className="fixed bottom-6 xs:bottom-8 right-4 xs:right-8 fixed-scroll-to-top"
        >
          {/* Progress Ring */}
          <div className="relative">
            <svg
              className="absolute inset-0 w-16 h-16 transform -rotate-90"
              viewBox="0 0 64 64"
            >
              {/* Background circle */}
              <circle
                cx="32"
                cy="32"
                r="28"
                fill="none"
                stroke="rgba(255, 255, 255, 0.2)"
                strokeWidth="4"
              />
              {/* Progress circle */}
              <motion.circle
                cx="32"
                cy="32"
                r="28"
                fill="none"
                stroke="rgba(255, 255, 255, 0.8)"
                strokeWidth="4"
                strokeLinecap="round"
                strokeDasharray={`${2 * Math.PI * 28}`}
                strokeDashoffset={`${2 * Math.PI * 28 * (1 - scrollProgress / 100)}`}
                transition={{ duration: 0.1 }}
              />
            </svg>

            {/* Main Button */}
            <motion.button
              onClick={scrollToTop}
              className={getButtonStyles()}
              whileHover={{ 
                scale: 1.1,
                rotate: variant === 'rocket' ? -15 : 0
              }}
              whileTap={{ scale: 0.9 }}
              animate={{
                y: variant === 'rocket' ? [0, -5, 0] : 0
              }}
              transition={{
                y: {
                  duration: 2,
                  repeat: Infinity,
                  ease: 'easeInOut'
                }
              }}
            >
              {/* Glow effect */}
              <motion.div
                className="absolute inset-0 rounded-full bg-white/20"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 0, 0.5]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: 'easeInOut'
                }}
              />
              
              {/* Icon with animation */}
              <motion.div
                animate={{
                  y: variant === 'default' ? [0, -2, 0] : 0,
                  rotate: variant === 'arrow' ? [0, -10, 10, 0] : 0
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  ease: 'easeInOut'
                }}
              >
                {getIcon()}
              </motion.div>
            </motion.button>

            {/* Floating particles for rocket variant */}
            {variant === 'rocket' && (
              <div className="absolute inset-0 pointer-events-none">
                {[...Array(3)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute bottom-0 left-1/2 w-1 h-1 bg-orange-400 rounded-full"
                    animate={{
                      y: [0, 20, 40],
                      x: [0, (i % 2 === 0 ? 1 : -1) * (2 + i)],
                      opacity: [1, 0.5, 0],
                      scale: [1, 0.5, 0]
                    }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      delay: i * 0.2,
                      ease: 'easeOut'
                    }}
                  />
                ))}
              </div>
            )}

            {/* Tooltip */}
            <motion.div
              initial={{ opacity: 0, x: 10 }}
              whileHover={{ opacity: 1, x: 0 }}
              className="absolute right-full mr-4 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap shadow-lg pointer-events-none"
            >
              Back to Top
              <div className="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
            </motion.div>
          </div>

          {/* Progress percentage */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs font-bold text-gray-600 bg-white px-2 py-1 rounded-full shadow-md"
          >
            {Math.round(scrollProgress)}%
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Multiple variants for different use cases
export const RocketScrollToTop: React.FC<Omit<ScrollToTopProps, 'variant'>> = (props) => (
  <ScrollToTop {...props} variant="rocket" />
);

export const ArrowScrollToTop: React.FC<Omit<ScrollToTopProps, 'variant'>> = (props) => (
  <ScrollToTop {...props} variant="arrow" />
);

export const DefaultScrollToTop: React.FC<Omit<ScrollToTopProps, 'variant'>> = (props) => (
  <ScrollToTop {...props} variant="default" />
);
