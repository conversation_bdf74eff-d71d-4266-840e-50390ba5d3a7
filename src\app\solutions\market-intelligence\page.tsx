import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, BarChart3, MapPin, Target, TrendingUp, Star, CheckCircle, Phone } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Market Intelligence & Analytics for Landscaping Businesses | GroundUP Digital',
  description: 'Advanced market intelligence and geospatial analytics that help landscaping businesses identify opportunities and dominate their markets.',
  keywords: 'landscaping market analysis, geospatial analytics landscaping, landscaping business intelligence, market research landscaping, competitive analysis landscaping',
};

const intelligenceFeatures = [
  {
    icon: MapPin,
    title: 'Geospatial Market Analysis',
    description: 'Advanced mapping and location intelligence to identify the best opportunities for your landscaping business.',
    benefits: ['Property value mapping', 'Demographic analysis', 'Service area optimization', 'Competitor location tracking']
  },
  {
    icon: BarChart3,
    title: 'Competitive Intelligence',
    description: 'Comprehensive analysis of your landscaping market and competitor strategies.',
    benefits: ['Competitor pricing analysis', 'Market share insights', 'Service gap identification', 'Opportunity mapping']
  },
  {
    icon: Target,
    title: 'Customer Segmentation',
    description: 'Advanced analytics to identify and target your most profitable customer segments.',
    benefits: ['Customer lifetime value', 'Behavioral segmentation', 'Predictive modeling', 'Targeting optimization']
  }
];

const packages = [
  {
    name: "Market Insights",
    price: "$797",
    period: "/month",
    description: "Essential market intelligence for landscaping businesses ready to make data-driven decisions",
    features: [
      "Market analysis reports",
      "Competitor monitoring",
      "Customer segmentation",
      "Monthly insights",
      "Basic analytics dashboard"
    ],
    popular: false
  },
  {
    name: "Intelligence Pro",
    price: "$1,497",
    period: "/month", 
    description: "Advanced market intelligence for landscaping companies ready to dominate their market",
    features: [
      "Advanced geospatial analysis",
      "Predictive market modeling",
      "Real-time competitor tracking",
      "Custom market research",
      "Dedicated analyst support",
      "Quarterly strategy sessions"
    ],
    popular: true
  },
  {
    name: "Strategic Command",
    price: "$2,997",
    period: "/month",
    description: "Enterprise market intelligence for large landscaping businesses seeking market leadership",
    features: [
      "Custom intelligence platform",
      "Advanced predictive analytics",
      "Market expansion planning",
      "Acquisition target analysis",
      "Executive briefings",
      "Strategic consulting"
    ],
    popular: false
  }
];

export default function MarketIntelligencePage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <BarChart3 className="w-4 h-4" />
              <span className="text-sm font-semibold">Market Intelligence for Landscaping</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Data-Driven Market Domination for{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscaping Businesses
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Advanced market intelligence and geospatial analytics that help landscaping business owners 
              identify opportunities, outmaneuver competitors, and dominate their markets.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <BarChart3 className="w-5 h-5 mr-2" />
                See Market Analysis
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                Get Market Report
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '340%', label: 'ROI Improvement' },
                { number: '75%', label: 'Market Share Gains' },
                { number: '90%', label: 'Accuracy Rate' },
                { number: '50+', label: 'Markets Analyzed' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Advanced Market Intelligence for Landscaping Business Success
            </h2>
            <p className="text-lg text-gray-600">
              Comprehensive market analysis and competitive intelligence designed specifically for landscaping business owners. 
              Make data-driven decisions that drive growth and market dominance.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {intelligenceFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Market Intelligence Packages for Landscaping Businesses
            </h2>
            <p className="text-lg text-gray-600">
              Advanced market intelligence designed specifically for landscaping business owners. 
              Choose the intelligence package that gives you the competitive edge.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Dominate Your Landscaping Market with Data?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a free market analysis and discover the opportunities and competitive advantages 
              waiting in your landscaping market.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <BarChart3 className="w-5 h-5 mr-2" />
                Get Free Market Analysis
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule Consultation
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
