import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'IoT & Smart Device Integration for Local Service Businesses | GroundUPDigital',
  description: 'Modernize your local service business with IoT and smart device integration. Monitor, automate, and optimize operations for landscapers, roofers, and pest control companies.',
  keywords: 'IoT integration, smart devices, automation, remote monitoring, smart business solutions',
  openGraph: {
    title: 'IoT & Smart Device Integration for Local Service Businesses | GroundUPDigital',
    description: 'Modernize your local service business with IoT and smart device integration. Monitor, automate, and optimize operations for landscapers, roofers, and pest control companies.',
    type: 'website',
  },
};

export default function IoTIntegrationPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-green-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              IoT & Smart Device <span className="text-blue-600">Integration</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Transform your local service business with cutting-edge IoT technology. Monitor equipment, 
              automate processes, and provide superior service with smart device integration.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Explore IoT Solutions
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                See Smart Integrations
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Why IoT Integration Matters
            </h2>
            <div className="grid md:grid-cols-3 gap-8 mb-16">
              {[
                { 
                  benefit: '40%', 
                  description: 'Reduction in operational costs',
                  icon: '💰'
                },
                { 
                  benefit: '60%', 
                  description: 'Improvement in service efficiency',
                  icon: '⚡'
                },
                { 
                  benefit: '24/7', 
                  description: 'Remote monitoring and alerts',
                  icon: '📡'
                }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-4">{stat.benefit}</div>
                  <p className="text-gray-600">{stat.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* IoT Solutions */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Smart IoT Solutions for Service Businesses
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  solution: 'Equipment Monitoring',
                  description: 'Real-time monitoring of equipment health, usage, and maintenance needs.',
                  features: ['Equipment status tracking', 'Predictive maintenance', 'Usage analytics', 'Failure prevention'],
                  icon: '🔧'
                },
                {
                  solution: 'Environmental Sensors',
                  description: 'Monitor environmental conditions that affect your services and operations.',
                  features: ['Weather monitoring', 'Soil moisture sensors', 'Temperature tracking', 'Air quality monitoring'],
                  icon: '🌡️'
                },
                {
                  solution: 'Fleet Management',
                  description: 'Track vehicles, optimize routes, and monitor driver behavior in real-time.',
                  features: ['GPS tracking', 'Route optimization', 'Fuel monitoring', 'Driver behavior analysis'],
                  icon: '🚛'
                },
                {
                  solution: 'Smart Irrigation',
                  description: 'Automated irrigation systems that respond to weather and soil conditions.',
                  features: ['Automated scheduling', 'Weather integration', 'Water usage optimization', 'Remote control'],
                  icon: '💧'
                },
                {
                  solution: 'Security Systems',
                  description: 'Protect your equipment and job sites with smart security solutions.',
                  features: ['Motion detection', 'Camera monitoring', 'Access control', 'Theft prevention'],
                  icon: '🔒'
                },
                {
                  solution: 'Customer Portals',
                  description: 'Give customers real-time access to service status and system monitoring.',
                  features: ['Service dashboards', 'Real-time updates', 'Historical data', 'Mobile access'],
                  icon: '📱'
                }
              ].map((solution, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{solution.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{solution.solution}</h3>
                  <p className="text-gray-600 mb-4">{solution.description}</p>
                  <ul className="space-y-2">
                    {solution.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Industry-Specific IoT */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Industry-Specific IoT Applications
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  industry: 'Landscaping',
                  applications: [
                    'Smart irrigation systems with weather integration',
                    'Soil moisture and nutrient monitoring',
                    'Equipment tracking and maintenance alerts',
                    'Automated lighting control systems',
                    'Plant health monitoring sensors',
                    'Water usage optimization and reporting'
                  ],
                  color: 'green'
                },
                {
                  industry: 'Roofing',
                  applications: [
                    'Roof condition monitoring sensors',
                    'Weather damage detection systems',
                    'Equipment safety monitoring',
                    'Material inventory tracking',
                    'Job site security cameras',
                    'Worker safety monitoring devices'
                  ],
                  color: 'blue'
                },
                {
                  industry: 'Pest Control',
                  applications: [
                    'Smart pest monitoring traps',
                    'Chemical usage tracking systems',
                    'Environmental condition monitoring',
                    'Treatment effectiveness sensors',
                    'Automated reporting systems',
                    'Customer notification automation'
                  ],
                  color: 'red'
                }
              ].map((industry, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg border-l-4 border-blue-600">
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">{industry.industry} IoT</h3>
                  <ul className="space-y-3">
                    {industry.applications.map((application, appIndex) => (
                      <li key={appIndex} className="flex items-start text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span className="text-sm">{application}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Implementation Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              IoT Implementation Process
            </h2>
            <div className="grid lg:grid-cols-5 gap-8">
              {[
                {
                  step: '1',
                  title: 'Assessment',
                  description: 'Evaluate your current operations and identify IoT opportunities.',
                  icon: '🔍'
                },
                {
                  step: '2',
                  title: 'Design',
                  description: 'Create custom IoT solutions tailored to your specific needs.',
                  icon: '📐'
                },
                {
                  step: '3',
                  title: 'Installation',
                  description: 'Professional installation and configuration of IoT devices.',
                  icon: '🔧'
                },
                {
                  step: '4',
                  title: 'Integration',
                  description: 'Connect IoT systems with your existing business processes.',
                  icon: '🔗'
                },
                {
                  step: '5',
                  title: 'Optimization',
                  description: 'Continuous monitoring and optimization for maximum efficiency.',
                  icon: '📈'
                }
              ].map((process, index) => (
                <div key={index} className="text-center">
                  <div className="bg-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    {process.step}
                  </div>
                  <div className="text-4xl mb-4">{process.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{process.title}</h3>
                  <p className="text-gray-600 text-sm">{process.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Technology Partners */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Technology Partners & Platforms
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { platform: 'AWS IoT', category: 'Cloud Platform', icon: '☁️' },
                { platform: 'Microsoft Azure', category: 'IoT Hub', icon: '🔷' },
                { platform: 'Google Cloud IoT', category: 'Data Analytics', icon: '📊' },
                { platform: 'Arduino', category: 'Hardware', icon: '🔧' },
                { platform: 'Raspberry Pi', category: 'Edge Computing', icon: '💻' },
                { platform: 'LoRaWAN', category: 'Connectivity', icon: '📡' },
                { platform: 'Zigbee', category: 'Mesh Networks', icon: '🕸️' },
                { platform: 'Cellular IoT', category: 'Wide Area Networks', icon: '📱' }
              ].map((partner, index) => (
                <div key={index} className="text-center bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{partner.icon}</div>
                  <h3 className="font-semibold text-gray-900 mb-2">{partner.platform}</h3>
                  <p className="text-sm text-gray-600">{partner.category}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              IoT Integration Results
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                { metric: '45%', label: 'Cost Reduction', icon: '💰' },
                { metric: '70%', label: 'Efficiency Improvement', icon: '⚡' },
                { metric: '90%', label: 'Uptime Increase', icon: '📈' },
                { metric: '24/7', label: 'Monitoring Coverage', icon: '👁️' }
              ].map((result, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{result.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{result.metric}</div>
                  <div className="text-gray-600">{result.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Modernize with IoT?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Discover how IoT integration can transform your operations and give you a competitive advantage.
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Explore IoT Solutions
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
