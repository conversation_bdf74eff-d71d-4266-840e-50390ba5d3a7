'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  ChevronRight,
  Target,
  TrendingUp,
  Users,
  Award,
  Clock,
  Shield,
  Lightbulb,
  BarChart3,
  CheckCircle,
  Star,
  ArrowRight,
  Phone
} from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

const reasons = [
  {
    icon: Target,
    title: 'Industry Specialization',
    description: 'We exclusively focus on local service businesses like landscaping, roofing, and pest control. This specialization means we understand your unique challenges, seasonal patterns, and customer behavior.',
    benefits: [
      'Industry-specific strategies that work',
      'Deep understanding of your target market',
      'Proven track record in your sector'
    ]
  },
  {
    icon: TrendingUp,
    title: 'Proven Results',
    description: 'Our clients see an average of 300% increase in qualified leads within 6 months. We\'ve generated over $50M in revenue for local service providers.',
    benefits: [
      '300% average lead increase',
      '$50M+ revenue generated',
      '95% client retention rate'
    ]
  },
  {
    icon: Users,
    title: 'Dedicated Team',
    description: 'Every client gets a dedicated account manager and access to our full team of specialists including SEO experts, PPC managers, designers, and developers.',
    benefits: [
      'Personal account manager',
      'Team of certified specialists',
      'Direct communication channels'
    ]
  },
  {
    icon: Lightbulb,
    title: 'Innovative Strategies',
    description: 'We stay ahead of digital marketing trends and continuously test new strategies to ensure your business maintains a competitive edge.',
    benefits: [
      'Latest marketing technologies',
      'Continuous strategy optimization',
      'Competitive advantage'
    ]
  },
  {
    icon: BarChart3,
    title: 'Data-Driven Approach',
    description: 'Every decision is backed by data. We provide detailed analytics, transparent reporting, and clear ROI measurements for all campaigns.',
    benefits: [
      'Comprehensive analytics',
      'Transparent reporting',
      'Clear ROI tracking'
    ]
  },
  {
    icon: Clock,
    title: 'Fast Implementation',
    description: 'We can launch your campaigns quickly without sacrificing quality. Emergency campaigns can be live within 24-48 hours.',
    benefits: [
      'Quick campaign launches',
      'Emergency response capability',
      'Efficient onboarding process'
    ]
  }
];

const stats = [
  { number: '500+', label: 'Successful Projects', icon: Award },
  { number: '300%', label: 'Average Lead Increase', icon: TrendingUp },
  { number: '95%', label: 'Client Retention Rate', icon: Users },
  { number: '$50M+', label: 'Revenue Generated', icon: BarChart3 }
];

const testimonials = [
  {
    name: 'Mike Johnson',
    company: 'Green Valley Landscaping',
    role: 'Owner',
    quote: 'GroundUP Digital transformed our business. We went from struggling to find customers to being booked out 3 months in advance.',
    rating: 5,
    results: '400% increase in leads'
  },
  {
    name: 'Sarah Chen',
    company: 'Apex Roofing Solutions',
    role: 'Marketing Director',
    quote: 'Their understanding of the roofing industry is unmatched. They know exactly how to reach homeowners who need our services.',
    rating: 5,
    results: '250% ROI improvement'
  },
  {
    name: 'David Rodriguez',
    company: 'Pest-Away Services',
    role: 'Founder',
    quote: 'The emergency campaign they set up during pest season was incredible. We couldn\'t keep up with all the calls coming in.',
    rating: 5,
    results: '500% seasonal boost'
  }
];

const certifications = [
  'Google Ads Certified',
  'Google Analytics Certified',
  'Facebook Blueprint Certified',
  'HubSpot Certified',
  'Bing Ads Certified',
  'SEMrush Certified'
];

export const WhyChooseContent: React.FC = () => {
  return (
    <>
      {/* Breadcrumb */}
      <Section background="gray" padding="sm">
        <Container>
          <nav className="text-sm">
            <ol className="flex items-center space-x-2 text-gray-600">
              <li>
                <Link href="/" className="hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li><ChevronRight className="h-4 w-4" /></li>
              <li>
                <Link href="/about" className="hover:text-blue-600 transition-colors">
                  About
                </Link>
              </li>
              <li><ChevronRight className="h-4 w-4" /></li>
              <li className="text-gray-900 font-medium">Why Choose Us</li>
            </ol>
          </nav>
        </Container>
      </Section>

      {/* Hero Section */}
      <Section background="gradient" padding="lg">
        <Container>
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Award className="h-4 w-4" />
                Why Choose GroundUP Digital
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                The Smart Choice for{' '}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600">
                  Local Service Businesses
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 mb-8">
                We're not just another digital marketing agency. We're specialists who understand 
                your industry, your challenges, and what it takes to grow a successful local service business.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact">
                  <Button variant="primary" size="lg">
                    Start Your Growth Journey
                  </Button>
                </Link>
                <Link href="/case-studies">
                  <Button variant="outline" size="lg">
                    View Success Stories
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </Container>
      </Section>

      {/* Stats Section */}
      <Section background="white" padding="lg">
        <Container>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                  <stat.icon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">{stat.number}</div>
                <div className="text-gray-600">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </Container>
      </Section>

      {/* Main Reasons Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              6 Reasons to Choose GroundUP Digital
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We've built our agency specifically to serve local service businesses. 
              Here's what sets us apart from generic marketing agencies.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {reasons.map((reason, index) => (
              <motion.div
                key={reason.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full p-8">
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <reason.icon className="h-6 w-6 text-blue-600" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-900 mb-3">
                        {reason.title}
                      </h3>
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {reason.description}
                      </p>
                      <ul className="space-y-2">
                        {reason.benefits.map((benefit, benefitIndex) => (
                          <li key={benefitIndex} className="flex items-center gap-2 text-sm text-gray-700">
                            <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </Container>
      </Section>

      {/* Testimonials Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              What Our Clients Say
            </h2>
            <p className="text-xl text-gray-600">
              Don't just take our word for it. Here's what local service business owners say about working with us.
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full p-6">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  
                  <blockquote className="text-gray-700 mb-6 leading-relaxed">
                    "{testimonial.quote}"
                  </blockquote>
                  
                  <div className="border-t border-gray-100 pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-semibold text-gray-900">{testimonial.name}</div>
                        <div className="text-sm text-gray-600">{testimonial.role}</div>
                        <div className="text-sm text-gray-600">{testimonial.company}</div>
                      </div>
                      <Badge variant="primary" size="sm">
                        {testimonial.results}
                      </Badge>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </Container>
      </Section>

      {/* Certifications Section */}
      <Section background="gray" padding="lg">
        <Container>
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Certified Experts
            </h2>
            <p className="text-xl text-gray-600">
              Our team holds industry certifications from leading platforms
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {certifications.map((cert, index) => (
              <motion.div
                key={cert}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card className="p-4 text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Shield className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="text-sm font-medium text-gray-900">{cert}</div>
                </Card>
              </motion.div>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="lg">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Ready to Experience the Difference?
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Join hundreds of successful local service businesses who trust GroundUP Digital 
                to grow their online presence and generate more leads.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact">
                  <Button variant="primary" size="lg" className="group">
                    Get Your Free Strategy Session
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
                
                <Button variant="outline" size="lg" className="group">
                  <Phone className="mr-2 h-5 w-5" />
                  Call (*************
                </Button>
              </div>
              
              <div className="mt-6 text-sm text-gray-600">
                <p>No obligation • Free consultation • Immediate insights</p>
              </div>
            </motion.div>
          </div>
        </Container>
      </Section>
    </>
  );
};
