'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  ChevronRight,
  Bug,
  Search,
  MousePointer,
  Globe,
  BarChart3,
  Users,
  Award,
  CheckCircle,
  ArrowRight,
  Phone,
  Star,
  TrendingUp,
  Target,
  Zap,
  Shield,
  AlertCircle
} from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { ModernCard, ServiceCard, StatsCard, FeatureCard, CTACard } from '@/components/ui/ModernCard';
import { StaggerContainer, StaggerItem, Reveal, AnimatedCounter, GradientText } from '@/components/ui/AnimatedElements';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

const pestControlServices = [
  {
    icon: <Search className="w-6 h-6 text-blue-600" />,
    title: "Pest Control SEO",
    description: "Dominate local search results for pest control, extermination, and emergency pest removal services in your area.",
    features: [
      "Local SEO for 'exterminator near me' searches",
      "Google Business Profile optimization",
      "Emergency pest control keyword targeting",
      "Seasonal pest campaign optimization",
      "Local citation building for pest directories"
    ],
    price: "Starting at $1,997"
  },
  {
    icon: <AlertCircle className="w-6 h-6 text-red-600" />,
    title: "Emergency Pest Marketing",
    description: "Rapid-response marketing campaigns that capture urgent pest control leads when customers need help immediately.",
    features: [
      "24/7 emergency campaign monitoring",
      "Urgent pest problem targeting",
      "Same-day service promotion",
      "Emergency landing page optimization",
      "Real-time lead routing system"
    ],
    price: "Starting at $2,997",
    popular: true
  },
  {
    icon: <MousePointer className="w-6 h-6 text-blue-600" />,
    title: "Pest Control Google Ads",
    description: "High-converting Google Ads campaigns that generate qualified leads for all types of pest control services.",
    features: [
      "Termite and bed bug campaigns",
      "Seasonal pest targeting (ants, wasps, etc.)",
      "Commercial pest control ads",
      "Competitor conquest campaigns",
      "Call tracking and conversion optimization"
    ],
    price: "Starting at $2,497"
  },
  {
    icon: <Globe className="w-6 h-6 text-blue-600" />,
    title: "Pest Control Web Design",
    description: "Professional, mobile-responsive websites that build trust and convert visitors into pest control customers.",
    features: [
      "Mobile-first responsive design",
      "Before/after treatment galleries",
      "Service area pages for local SEO",
      "Emergency contact forms",
      "Customer testimonial sections"
    ],
    price: "Starting at $3,997"
  }
];

const pestControlStats = [
  {
    number: "350%",
    label: "Emergency Call Increase",
    icon: <TrendingUp className="w-6 h-6" />,
    trend: "up" as const,
    trendValue: "+55% this quarter"
  },
  {
    number: "95+",
    label: "Pest Control Clients",
    icon: <Users className="w-6 h-6" />,
    trend: "up" as const,
    trendValue: "Growing monthly"
  },
  {
    number: "$18M+",
    label: "Revenue Generated",
    icon: <Award className="w-6 h-6" />,
    trend: "up" as const,
    trendValue: "For pest control clients"
  },
  {
    number: "4.9/5",
    label: "Client Satisfaction",
    icon: <Star className="w-6 h-6" />,
    trend: "up" as const,
    trendValue: "76 reviews"
  }
];

const pestControlFeatures = [
  {
    icon: <Shield className="w-6 h-6" />,
    title: "Emergency Response Marketing",
    description: "We specialize in capturing urgent pest control leads when customers have immediate problems that need solving.",
    benefits: [
      "24/7 emergency campaign monitoring",
      "Urgent keyword targeting and optimization",
      "Same-day service promotion strategies",
      "Real-time lead routing and tracking"
    ]
  },
  {
    icon: <Target className="w-6 h-6" />,
    title: "Pest Control Industry Focus",
    description: "Exclusive focus on pest control means we understand seasonal patterns, customer urgency, and treatment cycles.",
    benefits: [
      "Deep knowledge of pest control customer journey",
      "Seasonal pest campaign optimization",
      "Emergency vs. preventive service targeting",
      "Treatment follow-up marketing automation"
    ]
  },
  {
    icon: <Zap className="w-6 h-6" />,
    title: "Rapid Lead Generation",
    description: "Our proven strategies generate qualified pest control leads within hours, helping you respond faster than competitors.",
    benefits: [
      "Emergency lead capture systems",
      "High-urgency keyword targeting",
      "Conversion-optimized landing pages",
      "Immediate lead notification systems"
    ]
  }
];

const testimonials = [
  {
    name: "Carlos Martinez",
    company: "Pest-Away Services",
    location: "Phoenix, AZ",
    quote: "GroundUP Digital's emergency campaigns are game-changing. During scorpion season, we generated 63 qualified leads in one week. Our revenue increased 280% that quarter.",
    rating: 5,
    results: "280% revenue increase"
  },
  {
    name: "Lisa Chen",
    company: "Elite Exterminators",
    location: "Houston, TX",
    quote: "They understand the pest control business perfectly. Their seasonal campaigns for termites and bed bugs have made us the top choice in our market.",
    rating: 5,
    results: "63 leads in one week"
  },
  {
    name: "Robert Johnson",
    company: "Pro Pest Solutions",
    location: "Miami, FL",
    quote: "The ROI is incredible. Every emergency call we get through their marketing turns into $500+ in revenue. Best investment we've made for our pest control business.",
    rating: 5,
    results: "750% ROI"
  }
];

export const PestControlServicesContent: React.FC = () => {
  return (
    <>
      {/* Breadcrumb */}
      <Section background="gray" padding="sm">
        <Container>
          <nav className="text-sm">
            <ol className="flex items-center space-x-2 text-gray-600">
              <li>
                <Link href="/" className="hover:text-blue-600 transition-colors">
                  Home
                </Link>
              </li>
              <li><ChevronRight className="h-4 w-4" /></li>
              <li>
                <Link href="/services" className="hover:text-blue-600 transition-colors">
                  Services
                </Link>
              </li>
              <li><ChevronRight className="h-4 w-4" /></li>
              <li className="text-gray-900 font-medium">Pest Control Marketing</li>
            </ol>
          </nav>
        </Container>
      </Section>

      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center">
            <Reveal>
              <div className="inline-flex items-center gap-2 bg-orange-100 text-orange-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Bug className="h-4 w-4" />
                #1 Pest Control Marketing Agency
              </div>
              
              <h1 className="text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 xs:mb-6 leading-tight">
                Capture More{' '}
                <GradientText colors={['from-orange-600', 'via-red-600', 'to-pink-600']}>
                  Emergency Pest Calls
                </GradientText>
                {' '}with Expert Marketing
              </h1>
              
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                The only digital marketing agency that exclusively serves pest control companies. 
                We've helped 95+ pest control businesses generate over $18M in revenue with our emergency-response marketing strategies.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button variant="primary" size="lg" className="group">
                  Get Your Free Pest Control Marketing Audit
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button variant="outline" size="lg" className="group">
                  <Phone className="mr-2 h-5 w-5" />
                  Emergency: (*************
                </Button>
              </div>
              
              <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>24/7 emergency campaigns</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>350% emergency call increase</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Seasonal optimization</span>
                </div>
              </div>
            </Reveal>
          </div>
        </Container>
      </Section>

      {/* Stats Section */}
      <Section background="white" padding="lg">
        <Container>
          <StaggerContainer className="grid grid-cols-2 sm:grid-cols-4 gap-3 xs:gap-4 sm:gap-6">
            {pestControlStats.map((stat, index) => (
              <StaggerItem key={stat.label}>
                <StatsCard {...stat} />
              </StaggerItem>
            ))}
          </StaggerContainer>
        </Container>
      </Section>

      {/* Services Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="text-center mb-16">
            <Reveal>
              <h2 className="text-xl xs:text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Complete Digital Marketing Solutions for Pest Control Companies
              </h2>
              <p className="text-base xs:text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
                From emergency response campaigns to seasonal optimization, we provide everything your pest control business needs to dominate your local market.
              </p>
            </Reveal>
          </div>

          <StaggerContainer className="grid grid-cols-1 lg:grid-cols-2 gap-4 xs:gap-6 sm:gap-8">
            {pestControlServices.map((service, index) => (
              <StaggerItem key={service.title}>
                <ServiceCard {...service} />
              </StaggerItem>
            ))}
          </StaggerContainer>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="text-center mb-16">
            <Reveal>
              <h2 className="text-xl xs:text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Why Pest Control Companies Choose GroundUP Digital
              </h2>
              <p className="text-base xs:text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
                We're not just another marketing agency. We're pest control industry specialists who understand customer urgency, seasonal patterns, and emergency response marketing.
              </p>
            </Reveal>
          </div>

          <StaggerContainer className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 xs:gap-6 sm:gap-8">
            {pestControlFeatures.map((feature, index) => (
              <StaggerItem key={feature.title}>
                <FeatureCard {...feature} />
              </StaggerItem>
            ))}
          </StaggerContainer>
        </Container>
      </Section>

      {/* Testimonials Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="text-center mb-16">
            <Reveal>
              <h2 className="text-xl xs:text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Success Stories from Pest Control Business Owners
              </h2>
              <p className="text-base xs:text-lg sm:text-xl text-gray-600">
                See how we've helped pest control companies across the country capture more emergency calls and grow their businesses.
              </p>
            </Reveal>
          </div>

          <StaggerContainer className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 xs:gap-6 sm:gap-8">
            {testimonials.map((testimonial, index) => (
              <StaggerItem key={testimonial.name}>
                <ModernCard variant="elevated" size="lg" className="h-full">
                  <div className="flex items-center gap-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  
                  <blockquote className="text-gray-700 mb-6 leading-relaxed">
                    "{testimonial.quote}"
                  </blockquote>
                  
                  <div className="border-t border-gray-100 pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-semibold text-gray-900">{testimonial.name}</div>
                        <div className="text-sm text-gray-600">{testimonial.company}</div>
                        <div className="text-sm text-gray-500">{testimonial.location}</div>
                      </div>
                      <Badge variant="primary" size="sm">
                        {testimonial.results}
                      </Badge>
                    </div>
                  </div>
                </ModernCard>
              </StaggerItem>
            ))}
          </StaggerContainer>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <Reveal>
              <CTACard
                title="Ready to Capture More Emergency Pest Calls?"
                description="Join 95+ successful pest control companies who trust GroundUP Digital to generate consistent, high-quality emergency and service leads."
                buttonText="Get Your Free Emergency Marketing Strategy"
                variant="primary"
              />
            </Reveal>
          </div>
        </Container>
      </Section>
    </>
  );
};
