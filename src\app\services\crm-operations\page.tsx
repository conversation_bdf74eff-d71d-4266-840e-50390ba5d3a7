import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Custom CRM & Operations Solutions | GroundUPDigital',
  description: 'Streamline your local service business with custom CRM and operations solutions designed for landscapers, roofers, and pest control companies.',
  keywords: 'CRM solutions, business operations, customer management, local service CRM, business automation',
  openGraph: {
    title: 'Custom CRM & Operations Solutions | GroundUPDigital',
    description: 'Streamline your local service business with custom CRM and operations solutions designed for landscapers, roofers, and pest control companies.',
    type: 'website',
  },
};

export default function CRMOperationsPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-green-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Custom CRM & Operations <span className="text-blue-600">Solutions</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Streamline your business operations and improve customer relationships with our custom CRM solutions 
              designed specifically for landscaping, roofing, and pest control businesses.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get CRM Consultation
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                See CRM Demo
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Transform Your Business Operations
            </h2>
            <div className="grid md:grid-cols-3 gap-8 mb-16">
              {[
                { 
                  benefit: '40%', 
                  description: 'Increase in operational efficiency',
                  icon: '⚡'
                },
                { 
                  benefit: '60%', 
                  description: 'Reduction in administrative time',
                  icon: '⏰'
                },
                { 
                  benefit: '25%', 
                  description: 'Improvement in customer satisfaction',
                  icon: '😊'
                }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-4">{stat.benefit}</div>
                  <p className="text-gray-600">{stat.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CRM Features */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Comprehensive CRM Features
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: 'Customer Management',
                  description: 'Complete customer profiles with service history, preferences, and communication logs.',
                  features: ['Contact management', 'Service history', 'Customer preferences', 'Communication tracking'],
                  icon: '👥'
                },
                {
                  title: 'Lead Tracking',
                  description: 'Track leads from initial contact through conversion with automated follow-ups.',
                  features: ['Lead scoring', 'Pipeline management', 'Automated follow-ups', 'Conversion tracking'],
                  icon: '🎯'
                },
                {
                  title: 'Scheduling & Dispatch',
                  description: 'Efficient scheduling and dispatch system with real-time updates and GPS tracking.',
                  features: ['Calendar integration', 'Route optimization', 'Real-time updates', 'GPS tracking'],
                  icon: '📅'
                },
                {
                  title: 'Invoicing & Payments',
                  description: 'Automated invoicing and payment processing with multiple payment options.',
                  features: ['Automated invoicing', 'Payment processing', 'Recurring billing', 'Financial reporting'],
                  icon: '💳'
                },
                {
                  title: 'Inventory Management',
                  description: 'Track materials, equipment, and supplies with automated reorder alerts.',
                  features: ['Stock tracking', 'Reorder alerts', 'Supplier management', 'Cost tracking'],
                  icon: '📦'
                },
                {
                  title: 'Reporting & Analytics',
                  description: 'Comprehensive reporting and analytics to track business performance and growth.',
                  features: ['Performance dashboards', 'Custom reports', 'KPI tracking', 'Trend analysis'],
                  icon: '📊'
                }
              ].map((feature, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.features.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Industry-Specific Solutions */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Industry-Specific CRM Solutions
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  industry: 'Landscaping',
                  features: [
                    'Seasonal service scheduling',
                    'Property mapping and zones',
                    'Equipment maintenance tracking',
                    'Weather integration',
                    'Plant and material databases',
                    'Irrigation system management'
                  ],
                  color: 'green'
                },
                {
                  industry: 'Roofing',
                  features: [
                    'Inspection report generation',
                    'Photo documentation system',
                    'Insurance claim tracking',
                    'Material calculation tools',
                    'Warranty management',
                    'Safety compliance tracking'
                  ],
                  color: 'blue'
                },
                {
                  industry: 'Pest Control',
                  features: [
                    'Treatment history tracking',
                    'Chemical inventory management',
                    'Recurring service automation',
                    'Compliance reporting',
                    'Pest identification database',
                    'Safety protocol management'
                  ],
                  color: 'red'
                }
              ].map((solution, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg border-l-4 border-blue-600">
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">{solution.industry} CRM</h3>
                  <ul className="space-y-3">
                    {solution.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Integration Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seamless Integrations
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { tool: 'QuickBooks', category: 'Accounting', icon: '💰' },
                { tool: 'Google Workspace', category: 'Productivity', icon: '📧' },
                { tool: 'Stripe/PayPal', category: 'Payments', icon: '💳' },
                { tool: 'Mailchimp', category: 'Email Marketing', icon: '📬' },
                { tool: 'Zapier', category: 'Automation', icon: '⚡' },
                { tool: 'Google Maps', category: 'Navigation', icon: '🗺️' },
                { tool: 'Twilio', category: 'Communications', icon: '📱' },
                { tool: 'Dropbox', category: 'File Storage', icon: '☁️' }
              ].map((integration, index) => (
                <div key={index} className="text-center bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{integration.icon}</div>
                  <h3 className="font-semibold text-gray-900 mb-2">{integration.tool}</h3>
                  <p className="text-sm text-gray-600">{integration.category}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Implementation Process */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Our Implementation Process
            </h2>
            <div className="grid lg:grid-cols-5 gap-8">
              {[
                {
                  step: '1',
                  title: 'Discovery',
                  description: 'Analyze your current processes and identify improvement opportunities.',
                  icon: '🔍'
                },
                {
                  step: '2',
                  title: 'Design',
                  description: 'Create custom CRM solution tailored to your specific business needs.',
                  icon: '🎨'
                },
                {
                  step: '3',
                  title: 'Development',
                  description: 'Build and configure your custom CRM with all required features.',
                  icon: '⚙️'
                },
                {
                  step: '4',
                  title: 'Training',
                  description: 'Comprehensive training for your team on the new CRM system.',
                  icon: '🎓'
                },
                {
                  step: '5',
                  title: 'Support',
                  description: 'Ongoing support and optimization to ensure continued success.',
                  icon: '🤝'
                }
              ].map((process, index) => (
                <div key={index} className="text-center">
                  <div className="bg-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    {process.step}
                  </div>
                  <div className="text-4xl mb-4">{process.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{process.title}</h3>
                  <p className="text-gray-600 text-sm">{process.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Streamline Your Operations?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Get a free consultation and discover how our custom CRM solution can transform your business operations.
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Schedule Your CRM Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
