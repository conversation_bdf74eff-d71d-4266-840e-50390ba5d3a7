import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Social Media Marketing for Local Service Businesses | GroundUPDigital',
  description: 'Build your brand and attract customers with strategic social media marketing for landscapers, roofers, and pest control companies. Increase engagement and drive leads.',
  keywords: 'social media marketing, Facebook marketing, Instagram marketing, local service social media, landscaping social media',
  openGraph: {
    title: 'Social Media Marketing for Local Service Businesses | GroundUPDigital',
    description: 'Build your brand and attract customers with strategic social media marketing for landscapers, roofers, and pest control companies. Increase engagement and drive leads.',
    type: 'website',
  },
};

export default function SocialMediaPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-green-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Social Media Marketing That <span className="text-blue-600">Builds Your Brand</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Connect with your local community and showcase your expertise through strategic social media marketing 
              designed for landscapers, roofers, and pest control businesses.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Social Media Strategy
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                View Our Work
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Platforms Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              We Manage All Major Platforms
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  platform: 'Facebook',
                  description: 'Build community and showcase your work with engaging posts and targeted advertising.',
                  icon: '📘',
                  features: ['Business page optimization', 'Community engagement', 'Local advertising', 'Event promotion']
                },
                {
                  platform: 'Instagram',
                  description: 'Visual storytelling that highlights your craftsmanship and builds trust with potential customers.',
                  icon: '📷',
                  features: ['Visual content creation', 'Stories and Reels', 'Hashtag strategy', 'Influencer partnerships']
                },
                {
                  platform: 'Google My Business',
                  description: 'Optimize your local presence and encourage customer reviews and engagement.',
                  icon: '🗺️',
                  features: ['Profile optimization', 'Review management', 'Post updates', 'Q&A management']
                },
                {
                  platform: 'LinkedIn',
                  description: 'Professional networking and B2B opportunities for commercial service providers.',
                  icon: '💼',
                  features: ['Professional content', 'Industry networking', 'Thought leadership', 'B2B lead generation']
                }
              ].map((platform, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg border border-gray-100">
                  <div className="text-4xl mb-4">{platform.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{platform.platform}</h3>
                  <p className="text-gray-600 mb-4">{platform.description}</p>
                  <ul className="space-y-2">
                    {platform.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Complete Social Media Management
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: 'Content Creation',
                  description: 'Professional content that showcases your work and engages your audience.',
                  icon: '✍️'
                },
                {
                  title: 'Community Management',
                  description: 'Active engagement with your followers and prompt response to inquiries.',
                  icon: '💬'
                },
                {
                  title: 'Social Media Advertising',
                  description: 'Targeted ads to reach potential customers in your service area.',
                  icon: '🎯'
                },
                {
                  title: 'Analytics & Reporting',
                  description: 'Detailed insights into your social media performance and ROI.',
                  icon: '📊'
                },
                {
                  title: 'Reputation Management',
                  description: 'Monitor and manage your online reputation across all platforms.',
                  icon: '⭐'
                },
                {
                  title: 'Influencer Partnerships',
                  description: 'Connect with local influencers to expand your reach and credibility.',
                  icon: '🤝'
                }
              ].map((service, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600">{service.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Content Strategy Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Content That Converts
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  type: 'Before & After Photos',
                  description: 'Showcase your transformative work with compelling visual content that demonstrates your expertise.',
                  examples: ['Landscape transformations', 'Roof repairs', 'Pest problem solutions']
                },
                {
                  type: 'Educational Content',
                  description: 'Position yourself as an expert by sharing valuable tips and industry knowledge.',
                  examples: ['Maintenance tips', 'Seasonal advice', 'Problem prevention']
                },
                {
                  type: 'Customer Stories',
                  description: 'Build trust and credibility through authentic customer testimonials and success stories.',
                  examples: ['Video testimonials', 'Written reviews', 'Case studies']
                }
              ].map((content, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg border-l-4 border-blue-600">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">{content.type}</h3>
                  <p className="text-gray-600 mb-6">{content.description}</p>
                  <ul className="space-y-3">
                    {content.examples.map((example, exampleIndex) => (
                      <li key={exampleIndex} className="flex items-center text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                        {example}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Social Media Success Metrics
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                { metric: '250%', label: 'Increase in Followers', icon: '👥' },
                { metric: '400%', label: 'Boost in Engagement', icon: '❤️' },
                { metric: '180%', label: 'More Website Traffic', icon: '🌐' },
                { metric: '320%', label: 'Lead Generation Growth', icon: '📞' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-3xl mb-3">{stat.icon}</div>
                  <div className="text-3xl font-bold text-blue-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600 text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Grow Your Social Media Presence?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Let us help you build a strong social media presence that attracts customers and grows your business.
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Start Your Social Media Strategy
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
