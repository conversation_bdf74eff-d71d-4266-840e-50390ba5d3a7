import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Data Analytics for Roofing Companies | Business Intelligence | GroundUPDigital',
  description: 'Transform your roofing business with data analytics and business intelligence. Track performance, optimize operations, and make data-driven decisions for growth.',
  keywords: 'roofing data analytics, roofing business intelligence, roofing KPIs, roofing performance metrics',
  openGraph: {
    title: 'Data Analytics for Roofing Companies | Business Intelligence | GroundUPDigital',
    description: 'Transform your roofing business with data analytics and business intelligence. Track performance, optimize operations, and make data-driven decisions for growth.',
    type: 'website',
  },
};

export default function RoofingDataAnalyticsPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Data Analytics for <span className="text-blue-600">Roofing Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Transform your roofing business with powerful data analytics and business intelligence. 
              Track performance, optimize operations, and make data-driven decisions that drive growth and profitability.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Analytics Demo
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                View Dashboard Examples
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Analytics Impact */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Data-Driven Results for Roofing Businesses
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '35%', label: 'Increase in Profit Margins', icon: '📈' },
                { metric: '50%', label: 'Better Resource Allocation', icon: '⚡' },
                { metric: '70%', label: 'Improvement in Decision Speed', icon: '🎯' },
                { metric: '300%', label: 'ROI on Analytics Investment', icon: '💰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Key Analytics Areas */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Comprehensive Analytics for Roofing Operations
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  area: 'Financial Performance',
                  description: 'Track revenue, costs, profit margins, and financial health across all projects.',
                  metrics: ['Revenue per project', 'Profit margins', 'Cost analysis', 'Cash flow tracking', 'ROI by service type'],
                  icon: '💰'
                },
                {
                  area: 'Operational Efficiency',
                  description: 'Monitor crew productivity, project timelines, and resource utilization.',
                  metrics: ['Project completion times', 'Crew productivity', 'Equipment utilization', 'Material waste', 'Schedule adherence'],
                  icon: '⚙️'
                },
                {
                  area: 'Customer Analytics',
                  description: 'Understand customer behavior, satisfaction, and lifetime value.',
                  metrics: ['Customer acquisition cost', 'Lifetime value', 'Satisfaction scores', 'Repeat business rate', 'Referral tracking'],
                  icon: '👥'
                },
                {
                  area: 'Sales Performance',
                  description: 'Track lead generation, conversion rates, and sales team effectiveness.',
                  metrics: ['Lead conversion rates', 'Sales cycle length', 'Win/loss ratios', 'Pipeline value', 'Quote accuracy'],
                  icon: '📊'
                },
                {
                  area: 'Quality Metrics',
                  description: 'Monitor work quality, safety incidents, and customer complaints.',
                  metrics: ['Quality scores', 'Rework rates', 'Safety incidents', 'Customer complaints', 'Warranty claims'],
                  icon: '🏆'
                },
                {
                  area: 'Market Intelligence',
                  description: 'Analyze market trends, competitor performance, and growth opportunities.',
                  metrics: ['Market share', 'Competitive analysis', 'Pricing trends', 'Demand forecasting', 'Growth opportunities'],
                  icon: '🌍'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.area}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.metrics.map((metric, metricIndex) => (
                      <li key={metricIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {metric}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Emergency Analytics */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Emergency Response Analytics
              </h2>
              <p className="text-xl text-blue-100">
                Specialized analytics for emergency roofing operations and storm response.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  metric: 'Response Time Analytics',
                  description: 'Track and optimize emergency response times across different scenarios.',
                  insights: ['Average response time', 'Response time by urgency', 'Geographic response patterns', 'Crew availability impact', 'Weather correlation'],
                  icon: '⏱️'
                },
                {
                  metric: 'Storm Damage Analytics',
                  description: 'Analyze storm damage patterns and business impact during weather events.',
                  insights: ['Damage severity mapping', 'Storm impact correlation', 'Resource allocation patterns', 'Revenue during storms', 'Recovery time analysis'],
                  icon: '⛈️'
                },
                {
                  metric: 'Emergency Capacity Planning',
                  description: 'Optimize crew and resource allocation for emergency situations.',
                  insights: ['Capacity utilization', 'Surge demand patterns', 'Resource bottlenecks', 'Scalability metrics', 'Cost per emergency call'],
                  icon: '📋'
                }
              ].map((metric, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{metric.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{metric.metric}</h3>
                  <p className="text-blue-100 mb-4">{metric.description}</p>
                  <ul className="space-y-2">
                    {metric.insights.map((insight, insightIndex) => (
                      <li key={insightIndex} className="text-sm text-blue-100">
                        • {insight}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Dashboard Examples */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Interactive Dashboard Examples
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Executive Dashboard:</h3>
                <div className="bg-gray-100 rounded-lg p-6 mb-6">
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="bg-white p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-green-600">$2.4M</div>
                      <div className="text-sm text-gray-600">Monthly Revenue</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-blue-600">23%</div>
                      <div className="text-sm text-gray-600">Profit Margin</div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-white p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-purple-600">156</div>
                      <div className="text-sm text-gray-600">Active Projects</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-orange-600">4.8★</div>
                      <div className="text-sm text-gray-600">Customer Rating</div>
                    </div>
                  </div>
                </div>
                <ul className="space-y-3">
                  {[
                    'Real-time financial performance',
                    'Project pipeline overview',
                    'Customer satisfaction metrics',
                    'Team performance indicators',
                    'Market trend analysis'
                  ].map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-600">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Operations Dashboard:</h3>
                <div className="bg-gray-100 rounded-lg p-6 mb-6">
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="bg-white p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-green-600">15min</div>
                      <div className="text-sm text-gray-600">Avg Response Time</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-blue-600">94%</div>
                      <div className="text-sm text-gray-600">Crew Utilization</div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-white p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-purple-600">12</div>
                      <div className="text-sm text-gray-600">Emergency Calls</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg text-center">
                      <div className="text-2xl font-bold text-orange-600">89%</div>
                      <div className="text-sm text-gray-600">On-Time Completion</div>
                    </div>
                  </div>
                </div>
                <ul className="space-y-3">
                  {[
                    'Live crew tracking and status',
                    'Emergency call prioritization',
                    'Resource allocation optimization',
                    'Schedule adherence monitoring',
                    'Quality control metrics'
                  ].map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-600">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Predictive Analytics */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Predictive Analytics for Roofing Businesses
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  prediction: 'Demand Forecasting',
                  description: 'Predict seasonal demand and plan resources accordingly.',
                  benefits: ['Seasonal planning', 'Resource optimization', 'Inventory management', 'Crew scheduling'],
                  accuracy: '85%',
                  icon: '📈'
                },
                {
                  prediction: 'Weather Impact Analysis',
                  description: 'Forecast weather-related demand and emergency call volume.',
                  benefits: ['Storm preparation', 'Emergency staffing', 'Equipment positioning', 'Revenue forecasting'],
                  accuracy: '78%',
                  icon: '🌤️'
                },
                {
                  prediction: 'Customer Churn Prevention',
                  description: 'Identify customers at risk of switching to competitors.',
                  benefits: ['Retention strategies', 'Proactive outreach', 'Service improvements', 'Loyalty programs'],
                  accuracy: '82%',
                  icon: '🎯'
                },
                {
                  prediction: 'Project Cost Estimation',
                  description: 'Predict project costs and identify potential overruns.',
                  benefits: ['Accurate bidding', 'Cost control', 'Margin protection', 'Risk mitigation'],
                  accuracy: '90%',
                  icon: '💰'
                }
              ].map((prediction, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                  <div className="text-4xl mb-4">{prediction.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{prediction.prediction}</h3>
                  <p className="text-gray-600 mb-4 text-sm">{prediction.description}</p>
                  <div className="mb-4">
                    <span className="text-2xl font-bold text-blue-600">{prediction.accuracy}</span>
                    <div className="text-xs text-gray-500">Accuracy Rate</div>
                  </div>
                  <ul className="space-y-2">
                    {prediction.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="text-xs text-gray-600">
                        • {benefit}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Implementation Process */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Analytics Implementation Process
            </h2>
            <div className="grid md:grid-cols-5 gap-8">
              {[
                { step: '1', title: 'Data Assessment', description: 'Evaluate current data sources and quality', duration: '1 week' },
                { step: '2', title: 'Dashboard Design', description: 'Create custom dashboards for your needs', duration: '2 weeks' },
                { step: '3', title: 'Integration Setup', description: 'Connect data sources and systems', duration: '2-3 weeks' },
                { step: '4', title: 'Training & Launch', description: 'Train team and launch analytics platform', duration: '1 week' },
                { step: '5', title: 'Optimization', description: 'Ongoing optimization and support', duration: 'Ongoing' }
              ].map((phase, index) => (
                <div key={index} className="text-center">
                  <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                    {phase.step}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{phase.title}</h3>
                  <p className="text-gray-600 mb-2">{phase.description}</p>
                  <span className="text-sm text-blue-600 font-semibold">{phase.duration}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Data Analytics Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Premier Roofing: 40% Profit Increase with Analytics
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Premier Roofing was struggling with thin margins and inefficient operations. 
                    Our comprehensive analytics platform helped them identify opportunities and optimize performance.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">40%</div>
                      <div className="text-sm text-gray-600">Profit Increase</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">25%</div>
                      <div className="text-sm text-gray-600">Cost Reduction</div>
                    </div>
                  </div>
                  <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Analytics Implementation Results:</h4>
                  <ul className="space-y-3">
                    {[
                      '40% increase in profit margins',
                      '25% reduction in operational costs',
                      '60% improvement in resource allocation',
                      '50% faster decision-making process',
                      '30% increase in customer satisfaction',
                      '85% improvement in project forecasting'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Transform Your Roofing Business with Data?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Get a free analytics consultation and discover how data-driven insights can 
              optimize your operations and increase profitability.
            </p>
            <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Get Your Free Analytics Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
