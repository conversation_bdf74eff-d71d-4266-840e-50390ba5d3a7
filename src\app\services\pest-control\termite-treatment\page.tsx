import React from 'react';
import { Metadata } from 'next';
import { TermiteTreatmentMarketingContent } from '@/components/services/pest-control/TermiteTreatmentMarketingContent';

export const metadata: Metadata = {
  title: 'Termite Treatment Marketing | GroundUP Digital - Termite Inspection & Extermination Lead Generation',
  description: 'Generate high-value termite treatment leads with our specialized marketing campaigns. 400% more termite inspection requests and treatment bookings. Expert pest control marketing.',
  keywords: 'termite marketing, termite treatment marketing, termite inspection leads, termite extermination marketing, subterranean termite marketing, drywood termite marketing',
  openGraph: {
    title: 'Termite Treatment Marketing | GroundUP Digital',
    description: 'Generate high-value termite treatment leads with our specialized marketing campaigns. 400% more termite inspection requests and treatment bookings.',
    type: 'website',
    images: [
      {
        url: '/og-termite-marketing.jpg',
        width: 1200,
        height: 630,
        alt: 'Termite Treatment Marketing Services'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Termite Treatment Marketing | GroundUP Digital',
    description: 'Generate high-value termite treatment leads with our specialized marketing campaigns.',
    images: ['/og-termite-marketing.jpg']
  },
  alternates: {
    canonical: 'https://groundupdigital.com/services/pest-control/termite-treatment'
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

// Structured Data for Termite Treatment Marketing
const termiteTreatmentStructuredData = {
  "@context": "https://schema.org",
  "@type": "Service",
  "name": "Termite Treatment Marketing Services",
  "description": "Specialized digital marketing campaigns for termite inspection, treatment, and prevention services",
  "provider": {
    "@type": "Organization",
    "name": "GroundUP Digital",
    "url": "https://groundupdigital.com",
    "logo": "https://groundupdigital.com/logo.png"
  },
  "serviceType": "Digital Marketing",
  "areaServed": {
    "@type": "Country",
    "name": "United States"
  },
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Termite Marketing Services",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Termite Inspection Marketing",
          "description": "Lead generation campaigns for termite inspections and assessments"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Termite Treatment Campaigns",
          "description": "Marketing campaigns for termite extermination and treatment services"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Termite Prevention Marketing",
          "description": "Preventive termite control service marketing and maintenance contracts"
        }
      }
    ]
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.9",
    "reviewCount": "54",
    "bestRating": "5"
  }
};

export default function TermiteTreatmentMarketingPage() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(termiteTreatmentStructuredData) }}
      />
      <TermiteTreatmentMarketingContent />
    </>
  );
}
