import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Web Design for Roofing Companies | Convert Visitors to Customers | GroundUPDigital',
  description: 'Professional web design for roofing companies that builds trust and converts visitors into customers. Emergency-focused, mobile-optimized roofing websites that drive leads.',
  keywords: 'roofing web design, roofing company websites, emergency roofing websites, roofing contractor web design',
  openGraph: {
    title: 'Web Design for Roofing Companies | Convert Visitors to Customers | GroundUPDigital',
    description: 'Professional web design for roofing companies that builds trust and converts visitors into customers. Emergency-focused, mobile-optimized roofing websites that drive leads.',
    type: 'website',
  },
};

export default function RoofingWebDesignPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Web Design for <span className="text-blue-600">Roofing Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Build trust and convert visitors into customers with professional roofing websites. 
              Our emergency-focused web design drives leads and establishes your credibility in the roofing industry.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                View Website Examples
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                Get Free Design Consultation
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Trust-Building Features */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Trust-Building Features for Roofing Websites
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Emergency Contact Prominence',
                  description: 'Prominent emergency contact information and 24/7 availability messaging.',
                  benefits: ['Click-to-call buttons', '24/7 emergency messaging', 'Fast response guarantees', 'Emergency contact forms'],
                  icon: '🚨'
                },
                {
                  feature: 'Credentials & Certifications',
                  description: 'Display licenses, insurance, and industry certifications prominently.',
                  benefits: ['License displays', 'Insurance verification', 'Industry certifications', 'BBB ratings'],
                  icon: '🏆'
                },
                {
                  feature: 'Before/After Galleries',
                  description: 'Showcase your roofing work with high-quality project galleries.',
                  benefits: ['High-resolution photos', 'Project categorization', 'Before/after sliders', 'Mobile-optimized viewing'],
                  icon: '📸'
                },
                {
                  feature: 'Customer Reviews',
                  description: 'Prominent display of customer testimonials and Google reviews.',
                  benefits: ['Google review integration', 'Video testimonials', 'Star rating displays', 'Review widgets'],
                  icon: '⭐'
                },
                {
                  feature: 'Insurance Claim Assistance',
                  description: 'Dedicated sections for insurance claim help and storm damage.',
                  benefits: ['Insurance claim guides', 'Storm damage info', 'Claim assistance forms', 'Insurance partnerships'],
                  icon: '📋'
                },
                {
                  feature: 'Service Area Mapping',
                  description: 'Clear service area maps and coverage information.',
                  benefits: ['Interactive maps', 'Coverage areas', 'Response time info', 'Local presence'],
                  icon: '🗺️'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg border border-gray-100">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.feature}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Emergency-Focused Design */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Emergency-Focused Website Design
              </h2>
              <p className="text-xl text-blue-100">
                Capture emergency roofing leads with websites designed for urgent situations.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Immediate Contact Options',
                  description: 'Multiple ways for customers to reach you instantly during emergencies.',
                  elements: ['Large phone buttons', 'Emergency contact forms', 'Live chat integration', 'Text messaging options'],
                  icon: '📞'
                },
                {
                  feature: 'Storm Response Pages',
                  description: 'Dedicated pages for storm damage and emergency roof repairs.',
                  elements: ['Storm damage assessment', 'Emergency tarping services', 'Insurance claim help', 'Rapid response messaging'],
                  icon: '⛈️'
                },
                {
                  feature: 'Trust & Urgency Messaging',
                  description: 'Build trust while conveying urgency for emergency situations.',
                  elements: ['24/7 availability', 'Licensed & insured badges', 'Fast response guarantees', 'Emergency testimonials'],
                  icon: '⚡'
                }
              ].map((feature, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{feature.feature}</h3>
                  <p className="text-blue-100 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.elements.map((element, elementIndex) => (
                      <li key={elementIndex} className="text-sm text-blue-100">
                        • {element}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Website Types */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Roofing Website Types & Examples
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  type: 'Emergency Roofing Specialist',
                  features: ['24/7 emergency messaging', 'Storm damage focus', 'Rapid response guarantees', 'Insurance claim assistance'],
                  description: 'Perfect for roofers specializing in emergency repairs and storm damage response.',
                  bestFor: 'Emergency & storm damage specialists'
                },
                {
                  type: 'Full-Service Roofing Company',
                  features: ['Complete service offerings', 'Residential & commercial', 'Project galleries', 'Multiple contact options'],
                  description: 'Ideal for established roofing companies offering comprehensive roofing services.',
                  bestFor: 'Established roofing contractors'
                },
                {
                  type: 'Premium Roofing Contractor',
                  features: ['High-end project showcases', 'Premium material focus', 'Detailed process explanations', 'Luxury home testimonials'],
                  description: 'Designed for roofers targeting high-end residential and commercial projects.',
                  bestFor: 'Premium roofing specialists'
                }
              ].map((type, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{type.type}</h3>
                  <p className="text-gray-600 mb-6">{type.description}</p>
                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-700 mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {type.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                          <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-4">
                    <span className="text-sm font-semibold text-blue-800">Best For: </span>
                    <span className="text-sm text-blue-700">{type.bestFor}</span>
                  </div>
                  <button className="w-full mt-4 bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    View Example
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Conversion Optimization */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Conversion-Optimized for Roofing Businesses
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">What Makes Our Roofing Websites Convert:</h3>
                <ul className="space-y-4">
                  {[
                    'Emergency contact buttons visible on every page',
                    'Trust signals prominently displayed (licenses, insurance, BBB)',
                    'Before/after galleries showcasing quality workmanship',
                    'Customer testimonials with photos and star ratings',
                    'Clear service descriptions with transparent pricing',
                    'Insurance claim assistance and storm damage info',
                    'Fast loading speeds optimized for mobile users',
                    'Local SEO optimization for "roofing near me" searches'
                  ].map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Typical Results:</h3>
                <div className="space-y-6">
                  {[
                    { metric: '400%', description: 'Increase in emergency calls', icon: '📞' },
                    { metric: '75%', description: 'Improvement in conversion rate', icon: '🎯' },
                    { metric: '60%', description: 'More insurance claim leads', icon: '📋' },
                    { metric: '90%', description: 'Increase in quote requests', icon: '💰' }
                  ].map((result, index) => (
                    <div key={index} className="flex items-center bg-white p-4 rounded-lg shadow-lg">
                      <div className="text-3xl mr-4">{result.icon}</div>
                      <div>
                        <div className="text-2xl font-bold text-blue-600">{result.metric}</div>
                        <div className="text-gray-600">{result.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mobile Optimization */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Mobile-First Design for Emergency Situations
            </h2>
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Why Mobile Matters for Roofing:</h3>
                <ul className="space-y-4 mb-8">
                  {[
                    '78% of emergency roofing searches happen on mobile devices',
                    '92% of customers call directly from mobile search results',
                    'Emergency situations require immediate mobile access',
                    'Google prioritizes mobile-friendly websites in search results',
                    'Storm damage searches spike 500% on mobile during weather events'
                  ].map((stat, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{stat}</span>
                    </li>
                  ))}
                </ul>
                <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                  Test Your Current Website
                </button>
              </div>
              <div className="bg-gray-100 rounded-lg p-8">
                <h4 className="font-semibold text-gray-900 mb-4">Mobile Optimization Features:</h4>
                <ul className="space-y-3">
                  {[
                    'Large, touch-friendly emergency contact buttons',
                    'Fast loading on mobile networks (under 3 seconds)',
                    'Easy-to-read text without zooming required',
                    'Optimized images for mobile viewing',
                    'One-tap calling for emergency situations',
                    'Mobile-friendly contact and estimate forms',
                    'GPS integration for directions to your location',
                    'Swipe-friendly photo galleries'
                  ].map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-gray-600">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Roofing Website Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Elite Roofing: 500% Increase in Emergency Calls
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Elite Roofing was losing emergency calls to competitors with outdated website design. 
                    Our emergency-focused website redesign transformed their lead generation.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">500%</div>
                      <div className="text-sm text-gray-600">Emergency Call Increase</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">85%</div>
                      <div className="text-sm text-gray-600">Mobile Conversion Rate</div>
                    </div>
                  </div>
                  <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Website Results:</h4>
                  <ul className="space-y-3">
                    {[
                      '500% increase in emergency phone calls',
                      '85% mobile conversion rate improvement',
                      '300% more insurance claim leads',
                      '60% increase in quote requests',
                      '4.9-star average customer rating',
                      '40% faster page loading speeds'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready for a Website That Converts Emergency Calls?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Get a free website consultation and see how our roofing-focused web design can increase your emergency leads and revenue.
            </p>
            <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Get Your Free Roofing Web Design Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
