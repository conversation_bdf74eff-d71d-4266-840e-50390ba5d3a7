'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Star, 
  Award, 
  Users, 
  TrendingUp, 
  Shield, 
  CheckCircle,
  Clock,
  Target
} from 'lucide-react';

const trustMetrics = [
  {
    icon: Users,
    number: '200+',
    label: 'Landscaping Businesses Served',
    description: 'Trusted exclusively by landscaping business owners nationwide'
  },
  {
    icon: TrendingUp,
    number: '247%',
    label: 'Average Lead Increase',
    description: 'Our landscaping clients see dramatic growth in qualified leads within 90 days'
  },
  {
    icon: Star,
    number: '4.9/5',
    label: 'Client Satisfaction',
    description: 'Consistently rated as the top digital growth partner for landscaping businesses'
  },
  {
    icon: Clock,
    number: '30-60',
    label: 'Days to Results',
    description: 'Most landscaping businesses see measurable improvements within the first month'
  }
];

const certifications = [
  {
    name: 'Google Partner',
    icon: '🏆',
    description: 'Certified Google Ads specialists'
  },
  {
    name: 'Facebook Blueprint',
    icon: '📘',
    description: 'Advanced social media marketing'
  },
  {
    name: 'HubSpot Certified',
    icon: '🎯',
    description: 'Inbound marketing experts'
  },
  {
    name: 'Local SEO Pro',
    icon: '📍',
    description: 'Local search optimization'
  }
];

const guarantees = [
  {
    icon: Shield,
    title: '30-Day Money Back',
    description: 'Not satisfied? Get your money back, no questions asked.'
  },
  {
    icon: Target,
    title: 'Lead Guarantee',
    description: 'We guarantee increased leads or we work for free until you get them.'
  },
  {
    icon: CheckCircle,
    title: 'No Long-Term Contracts',
    description: 'Month-to-month service. Cancel anytime with 30 days notice.'
  }
];

export const TrustSection: React.FC = () => {
  return (
    <section className="section-padding bg-white border-b border-gray-100">
      <div className="container">
        {/* Trust Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="max-w-6xl mx-auto mb-12 xs:mb-16 sm:mb-20"
        >
          <div className="text-center mb-8 xs:mb-10 sm:mb-12">
            <h2 className="heading-2 mb-3 xs:mb-4">
              Trusted by <span className="text-blue-600">200+ Landscaping Business Owners</span>
            </h2>
            <p className="text-body text-gray-600 max-w-4xl mx-auto">
              Join the growing community of successful landscaping businesses that have transformed their growth with our proven strategies.
            </p>
          </div>

          <div className="grid grid-cols-1 xs:grid-cols-2 lg:grid-cols-4 gap-4 xs:gap-6 sm:gap-8">
            {trustMetrics.map((metric, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center group"
              >
                <div className="bg-blue-50 w-12 h-12 xs:w-14 xs:h-14 sm:w-16 sm:h-16 rounded-full flex items-center justify-center mx-auto mb-3 xs:mb-4 group-hover:bg-blue-100 transition-colors">
                  <metric.icon className="w-6 h-6 xs:w-7 xs:h-7 sm:w-8 sm:h-8 text-blue-600" />
                </div>
                <div className="text-2xl xs:text-3xl sm:text-4xl font-bold text-gray-900 mb-1 xs:mb-2">{metric.number}</div>
                <div className="text-sm xs:text-base sm:text-lg font-semibold text-gray-700 mb-1 xs:mb-2">{metric.label}</div>
                <div className="text-xs xs:text-sm text-gray-600 leading-relaxed">{metric.description}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Certifications & Guarantees */}
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 xs:gap-12 sm:gap-16">
            {/* Certifications */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <div className="text-center lg:text-left mb-6 xs:mb-8">
                <div className="inline-flex items-center gap-1 xs:gap-2 bg-green-50 rounded-full px-3 xs:px-4 py-1.5 xs:py-2 mb-3 xs:mb-4">
                  <Award className="w-4 h-4 xs:w-5 xs:h-5 text-green-600" />
                  <span className="text-xs xs:text-sm font-semibold text-green-700">Certified Experts</span>
                </div>
                <h3 className="heading-3 mb-3 xs:mb-4">
                  Industry-Leading Certifications
                </h3>
                <p className="text-small text-gray-600">
                  Our team holds the highest certifications in digital marketing, ensuring you get expert-level service.
                </p>
              </div>

              <div className="grid grid-cols-1 xs:grid-cols-2 gap-3 xs:gap-4">
                {certifications.map((cert, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-gray-50 rounded-lg p-3 xs:p-4 text-center hover:bg-gray-100 transition-colors"
                  >
                    <div className="text-xl xs:text-2xl mb-1 xs:mb-2">{cert.icon}</div>
                    <div className="font-semibold text-gray-900 mb-1 text-sm xs:text-base">{cert.name}</div>
                    <div className="text-xs xs:text-sm text-gray-600">{cert.description}</div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Guarantees */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <div className="text-center lg:text-left mb-6 xs:mb-8">
                <div className="inline-flex items-center gap-1 xs:gap-2 bg-blue-50 rounded-full px-3 xs:px-4 py-1.5 xs:py-2 mb-3 xs:mb-4">
                  <Shield className="w-4 h-4 xs:w-5 xs:h-5 text-blue-600" />
                  <span className="text-xs xs:text-sm font-semibold text-blue-700">Risk-Free Service</span>
                </div>
                <h3 className="heading-3 mb-3 xs:mb-4">
                  Our Ironclad Guarantees
                </h3>
                <p className="text-small text-gray-600">
                  We're so confident in our results, we back our services with industry-leading guarantees.
                </p>
              </div>

              <div className="space-y-4 xs:space-y-6">
                {guarantees.map((guarantee, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="flex items-start gap-3 xs:gap-4 p-3 xs:p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
                  >
                    <div className="bg-blue-100 w-10 h-10 xs:w-12 xs:h-12 rounded-lg flex items-center justify-center flex-shrink-0">
                      <guarantee.icon className="w-5 h-5 xs:w-6 xs:h-6 text-blue-600" />
                    </div>
                    <div className="min-w-0">
                      <h4 className="font-semibold text-gray-900 mb-1 xs:mb-2 text-sm xs:text-base">{guarantee.title}</h4>
                      <p className="text-gray-600 text-xs xs:text-sm leading-relaxed">{guarantee.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>

        {/* Client Logos */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="mt-12 xs:mt-16 sm:mt-20 text-center"
        >
          <p className="text-xs xs:text-sm text-gray-500 mb-6 xs:mb-8">Trusted by leading local service businesses</p>
          <div className="grid grid-cols-2 xs:grid-cols-3 sm:grid-cols-6 gap-4 xs:gap-6 sm:gap-8 opacity-60 max-w-4xl mx-auto">
            {/* Placeholder for client logos */}
            {[...Array(6)].map((_, index) => (
              <div
                key={index}
                className="w-full h-12 xs:h-14 sm:h-16 bg-gray-200 rounded-lg flex items-center justify-center"
              >
                <span className="text-gray-400 text-xs xs:text-sm font-medium">Client Logo</span>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};
