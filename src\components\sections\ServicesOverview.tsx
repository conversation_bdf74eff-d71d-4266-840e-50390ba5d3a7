'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Globe, 
  Smartphone, 
  TrendingUp, 
  Search, 
  Share2, 
  MousePointer, 
  Shield,
  ArrowRight
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Container, Section } from '@/components/ui/Container';

const services = [
  {
    icon: Globe,
    title: 'Business-Focused Websites',
    description: 'Professional landscaping business websites that showcase your services and convert visitors into paying customers.',
    features: ['Lead generation forms', 'Service showcases', 'Customer testimonials', 'Mobile-optimized'],
    color: 'blue'
  },
  {
    icon: Search,
    title: 'Local SEO for Landscapers',
    description: 'Help your landscaping business dominate local search results and attract more customers in your service area.',
    features: ['Local business SEO', 'Google Business optimization', 'Industry keywords', 'Local competitor analysis'],
    color: 'green'
  },
  {
    icon: TrendingUp,
    title: 'Business Lead Generation',
    description: 'Advanced lead generation systems that help landscaping businesses attract and qualify high-value customers.',
    features: ['Customer targeting', 'Lead qualification', 'Automated follow-up', 'ROI tracking'],
    color: 'purple'
  },
  {
    icon: Smartphone,
    title: 'Sales Presentation Tools',
    description: 'Advanced presentation technology that helps landscaping businesses close more deals and impress clients.',
    features: ['Visual proposals', 'Interactive presentations', 'Client engagement tools', 'Sales enablement'],
    color: 'orange'
  },
  {
    icon: MousePointer,
    title: 'Precision Paid Advertising',
    description: 'High-intent PPC campaigns targeting customers ready for landscaping services.',
    features: ['Google Local Service Ads', 'Landscaping keywords', 'Seasonal campaigns', 'ROI optimization'],
    color: 'pink'
  },
  {
    icon: Share2,
    title: 'Smart Landscaping & IoT',
    description: 'Connected systems that create ongoing service opportunities and client engagement.',
    features: ['Smart irrigation integration', 'Soil monitoring', 'Maintenance alerts', 'Data-driven services'],
    color: 'indigo'
  },
  {
    icon: Shield,
    title: 'Strategic Content Marketing',
    description: 'Authority-building content that educates clients and showcases your expertise.',
    features: ['Landscaping guides', 'Project showcases', 'Seasonal content', 'Thought leadership'],
    color: 'teal'
  }
];

const colorClasses = {
  blue: 'text-blue-600 bg-blue-100',
  green: 'text-green-600 bg-green-100',
  purple: 'text-purple-600 bg-purple-100',
  orange: 'text-orange-600 bg-orange-100',
  pink: 'text-pink-600 bg-pink-100',
  indigo: 'text-indigo-600 bg-indigo-100',
  teal: 'text-teal-600 bg-teal-100'
};

export const ServicesOverview: React.FC = () => {
  return (
    <Section background="white" padding="xl">
      <Container>
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center max-w-4xl mx-auto mb-10 xs:mb-12 sm:mb-16"
        >
          <div className="inline-flex items-center gap-1 xs:gap-2 bg-blue-50 rounded-full px-3 xs:px-4 py-1.5 xs:py-2 mb-4 xs:mb-6 max-w-full">
            <div className="w-1.5 h-1.5 xs:w-2 xs:h-2 bg-blue-600 rounded-full animate-pulse flex-shrink-0"></div>
            <span className="text-xs xs:text-sm font-semibold text-blue-700 truncate">
              <span className="hidden sm:inline">Premium Solutions • 300+ Landscaping Projects</span>
              <span className="sm:hidden">300+ Landscaping Projects</span>
            </span>
          </div>

          <h2 className="heading-2 mb-4 xs:mb-6 leading-tight">
            <span className="block xs:inline">Complete Digital Marketing Solutions for</span>{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600 block xs:inline mt-1 xs:mt-0">
              Landscaping Business Owners
            </span>
          </h2>

          <p className="text-body text-gray-600 mb-6 xs:mb-8">
            We help landscaping business owners grow their companies with comprehensive digital marketing solutions.
            From lead generation to customer acquisition—everything you need to scale your landscaping business.
          </p>

          {/* Results Stats */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 xs:gap-4 sm:gap-6 max-w-3xl mx-auto">
            {[
              { number: '247%', label: 'Average Lead Increase', shortLabel: 'Lead Increase' },
              { number: '200+', label: 'Landscaping Clients', shortLabel: 'Clients' },
              { number: '156%', label: 'Revenue Growth', shortLabel: 'Revenue Growth' },
              { number: '89%', label: 'Client Retention', shortLabel: 'Retention' }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-xl xs:text-2xl sm:text-3xl font-bold text-blue-600 mb-1">{stat.number}</div>
                <div className="text-xs xs:text-sm text-gray-600">
                  <span className="hidden sm:inline">{stat.label}</span>
                  <span className="sm:hidden">{stat.shortLabel}</span>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 xs:gap-6 sm:gap-8 mb-8 xs:mb-10 sm:mb-12">
          {services.map((service, index) => {
            const Icon = service.icon;
            return (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full group cursor-pointer">
                  <CardContent className="space-y-4 xs:space-y-6">
                    {/* Icon */}
                    <div className={`w-10 h-10 xs:w-12 xs:h-12 rounded-lg ${colorClasses[service.color as keyof typeof colorClasses]} flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}>
                      <Icon className="h-5 w-5 xs:h-6 xs:w-6" />
                    </div>

                    {/* Content */}
                    <div className="space-y-3 xs:space-y-4">
                      <h3 className="text-lg xs:text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                        {service.title}
                      </h3>
                      <p className="text-sm xs:text-base text-gray-600 leading-relaxed">
                        {service.description}
                      </p>
                    </div>

                    {/* Features */}
                    <ul className="space-y-1.5 xs:space-y-2">
                      {service.features.map((feature) => (
                        <li key={feature} className="flex items-center text-xs xs:text-sm text-gray-600">
                          <div className="w-1 h-1 xs:w-1.5 xs:h-1.5 bg-blue-500 rounded-full mr-2 xs:mr-3 flex-shrink-0" />
                          <span className="leading-relaxed">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    {/* Learn More Link */}
                    <div className="pt-3 xs:pt-4 border-t border-gray-100">
                      <button className="flex items-center text-blue-600 hover:text-blue-700 font-medium text-xs xs:text-sm group-hover:translate-x-1 transition-all duration-200 touch-target">
                        Learn More
                        <ArrowRight className="ml-1 h-3 w-3 xs:h-4 xs:w-4" />
                      </button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative bg-gradient-to-r from-blue-600 to-green-600 rounded-xl xs:rounded-2xl p-4 xs:p-6 sm:p-8 md:p-12 text-white overflow-hidden"
        >
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-white/5 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[length:15px_15px] xs:bg-[length:20px_20px]"></div>

          <div className="relative z-10">
            <div className="max-w-3xl mx-auto text-center">
              <div className="inline-flex items-center gap-1 xs:gap-2 bg-white/20 backdrop-blur-sm rounded-full px-3 xs:px-4 py-1.5 xs:py-2 mb-4 xs:mb-6 max-w-full">
                <Shield className="w-3 h-3 xs:w-4 xs:h-4 flex-shrink-0" />
                <span className="text-xs xs:text-sm font-semibold truncate">Risk-Free 30-Day Guarantee</span>
              </div>

              <h3 className="text-xl xs:text-2xl sm:text-3xl md:text-4xl font-bold mb-3 xs:mb-4">
                Ready to Grow Your Landscaping Business?
              </h3>

              <p className="text-sm xs:text-base sm:text-lg md:text-xl text-blue-100 mb-6 xs:mb-8 leading-relaxed">
                Get a comprehensive digital marketing strategy designed specifically to help landscaping business owners attract more customers and increase revenue.
                <span className="font-semibold text-white block xs:inline">
                  <span className="xs:hidden"><br /></span>
                  Join 200+ landscaping business owners
                </span>
                <span className="hidden sm:inline"> who have grown their companies with our proven marketing solutions.</span>
                <span className="sm:hidden block mt-1">who have grown their companies.</span>
              </p>

              {/* Value Props */}
              <div className="grid grid-cols-1 xs:grid-cols-3 gap-3 xs:gap-4 sm:gap-6 mb-6 xs:mb-8">
                {[
                  { icon: TrendingUp, text: 'Guaranteed Growth Results', shortText: 'Growth Results' },
                  { icon: Shield, text: 'Premium Partnership', shortText: 'Premium Partner' },
                  { icon: MousePointer, text: 'Free Growth Strategy', shortText: 'Free Strategy' }
                ].map((prop, index) => (
                  <div key={index} className="flex items-center justify-center gap-1 xs:gap-2 text-white/90">
                    <prop.icon className="w-4 h-4 xs:w-5 xs:h-5 flex-shrink-0" />
                    <span className="font-medium text-xs xs:text-sm sm:text-base">
                      <span className="hidden sm:inline">{prop.text}</span>
                      <span className="sm:hidden">{prop.shortText}</span>
                    </span>
                  </div>
                ))}
              </div>

              <div className="flex flex-col sm:flex-row gap-3 xs:gap-4 justify-center">
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} className="w-full sm:w-auto">
                  <Button
                    variant="secondary"
                    size="lg"
                    className="bg-white text-blue-600 hover:bg-gray-50 shadow-xl w-full sm:w-auto"
                  >
                    <span className="hidden xs:inline">Get Free Growth Strategy</span>
                    <span className="xs:hidden">Free Growth Strategy</span>
                    <ArrowRight className="ml-1 xs:ml-2 w-4 h-4 xs:w-5 xs:h-5" />
                  </Button>
                </motion.div>

                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }} className="w-full sm:w-auto">
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm w-full sm:w-auto"
                  >
                    <span className="hidden xs:inline">View Success Stories</span>
                    <span className="xs:hidden">Success Stories</span>
                  </Button>
                </motion.div>
              </div>

              {/* Urgency Element */}
              <div className="mt-4 xs:mt-6 text-xs xs:text-sm text-blue-100 leading-relaxed">
                <span className="font-semibold">Exclusive Offer:</span>
                <span className="hidden sm:inline"> Free digital growth strategy for the first 25 landscaping businesses this month</span>
                <span className="sm:hidden"> Free strategy for first 25 businesses</span>
              </div>
            </div>
          </div>
        </motion.div>
      </Container>
    </Section>
  );
};
