import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'App Development for Pest Control Companies | Mobile Solutions | GroundUPDigital',
  description: 'Custom mobile app development for pest control businesses. Build apps for technicians, customers, and operations management with specialized pest control features.',
  keywords: 'pest control app development, exterminator mobile app, pest management app, pest control software',
  openGraph: {
    title: 'App Development for Pest Control Companies | Mobile Solutions | GroundUPDigital',
    description: 'Custom mobile app development for pest control businesses. Build apps for technicians, customers, and operations management with specialized pest control features.',
    type: 'website',
  },
};

export default function PestControlAppDevelopmentPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-yellow-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              App Development for <span className="text-green-600">Pest Control Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Build custom mobile applications for your pest control business. Streamline operations, 
              improve customer service, and empower your technicians with specialized pest control apps.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Discuss Your App Idea
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View App Examples
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* App Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Mobile Apps Transform Pest Control Operations
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '60%', label: 'Faster Service Documentation', icon: '📱' },
                { metric: '45%', label: 'Improvement in Customer Satisfaction', icon: '😊' },
                { metric: '80%', label: 'Reduction in Paperwork', icon: '📄' },
                { metric: '350%', label: 'ROI Within First Year', icon: '💰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-green-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* App Types */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Specialized Mobile Apps for Pest Control
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  app: 'Technician Field App',
                  description: 'Comprehensive mobile app for pest control technicians in the field.',
                  features: ['Service documentation', 'Photo capture', 'Treatment tracking', 'Customer signatures', 'Offline capabilities'],
                  icon: '👨‍🔧'
                },
                {
                  app: 'Customer Portal App',
                  description: 'Self-service app for customers to manage their pest control services.',
                  features: ['Service scheduling', 'Treatment history', 'Payment processing', 'Communication tools', 'Pest identification'],
                  icon: '📱'
                },
                {
                  app: 'Pest Identification App',
                  description: 'AI-powered app for identifying pests and recommending treatments.',
                  features: ['Photo recognition', 'Pest database', 'Treatment recommendations', 'Severity assessment', 'Prevention tips'],
                  icon: '🔍'
                },
                {
                  app: 'Route Optimization App',
                  description: 'Smart routing app for efficient service route planning and navigation.',
                  features: ['GPS navigation', 'Route optimization', 'Traffic integration', 'Schedule management', 'Customer notifications'],
                  icon: '🗺️'
                },
                {
                  app: 'Inspection & Reporting App',
                  description: 'Detailed inspection app with comprehensive reporting capabilities.',
                  features: ['Digital inspections', 'Report generation', 'Photo documentation', 'Compliance tracking', 'Client portals'],
                  icon: '📋'
                },
                {
                  app: 'Emergency Response App',
                  description: 'Rapid response app for emergency pest control situations.',
                  features: ['Emergency dispatch', 'Priority routing', 'Real-time updates', 'Customer communication', 'Service tracking'],
                  icon: '🚨'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.app}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Technician App Features */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Technician Field App Features
              </h2>
              <p className="text-xl text-green-100">
                Empower your technicians with comprehensive mobile tools for efficient service delivery.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Service Documentation',
                  description: 'Complete service documentation with photos, notes, and treatment details.',
                  capabilities: ['Treatment logging', 'Photo documentation', 'Service notes', 'Chemical usage tracking', 'Time tracking'],
                  icon: '📝'
                },
                {
                  feature: 'Customer Interaction',
                  description: 'Seamless customer communication and service confirmation tools.',
                  capabilities: ['Digital signatures', 'Customer communication', 'Service confirmations', 'Feedback collection', 'Follow-up scheduling'],
                  icon: '🤝'
                },
                {
                  feature: 'Offline Capabilities',
                  description: 'Full functionality even in areas with poor internet connectivity.',
                  capabilities: ['Offline data entry', 'Photo storage', 'Sync when connected', 'Local data backup', 'Emergency protocols'],
                  icon: '📶'
                }
              ].map((feature, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{feature.feature}</h3>
                  <p className="text-green-100 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.capabilities.map((capability, capIndex) => (
                      <li key={capIndex} className="text-sm text-green-100">
                        • {capability}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Customer App Features */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Customer Portal App Features
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  feature: 'Service Scheduling',
                  description: 'Easy appointment booking and rescheduling',
                  benefits: ['Online booking', 'Calendar integration', 'Automatic reminders', 'Flexible rescheduling'],
                  icon: '📅'
                },
                {
                  feature: 'Treatment History',
                  description: 'Complete history of all pest control treatments',
                  benefits: ['Service records', 'Treatment details', 'Photo documentation', 'Progress tracking'],
                  icon: '📊'
                },
                {
                  feature: 'Pest Identification',
                  description: 'Help customers identify pests and get advice',
                  benefits: ['Photo recognition', 'Pest database', 'Treatment info', 'Prevention tips'],
                  icon: '🔍'
                },
                {
                  feature: 'Communication Hub',
                  description: 'Direct communication with service team',
                  benefits: ['In-app messaging', 'Service updates', 'Emergency contact', 'Feedback system'],
                  icon: '💬'
                }
              ].map((feature, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{feature.feature}</h3>
                  <p className="text-gray-600 mb-4 text-sm">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, benefitIndex) => (
                      <li key={benefitIndex} className="text-xs text-gray-600">
                        • {benefit}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* AI-Powered Features */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              AI-Powered Pest Control Features
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Intelligent Pest Identification:</h3>
                <ul className="space-y-4">
                  {[
                    'Photo-based pest recognition with 95% accuracy',
                    'Comprehensive pest database with treatment options',
                    'Severity assessment and urgency recommendations',
                    'Seasonal pest prediction and prevention tips',
                    'Integration with treatment protocols and chemicals',
                    'Real-time updates from pest control experts',
                    'Machine learning improvements over time',
                    'Multi-language support for diverse markets'
                  ].map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Smart Recommendations:</h3>
                <div className="space-y-6">
                  {[
                    { feature: 'Treatment Optimization', description: 'AI suggests optimal treatments based on pest type and severity', icon: '🎯' },
                    { feature: 'Scheduling Intelligence', description: 'Smart scheduling based on pest lifecycle and weather', icon: '📅' },
                    { feature: 'Chemical Selection', description: 'Recommend safest and most effective chemicals', icon: '🧪' },
                    { feature: 'Prevention Strategies', description: 'Proactive recommendations to prevent future infestations', icon: '🛡️' }
                  ].map((feature, index) => (
                    <div key={index} className="flex items-center bg-white p-4 rounded-lg shadow-lg">
                      <div className="text-3xl mr-4">{feature.icon}</div>
                      <div>
                        <div className="text-lg font-semibold text-gray-900">{feature.feature}</div>
                        <div className="text-gray-600">{feature.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Development Process */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Mobile App Development Process
            </h2>
            <div className="grid md:grid-cols-6 gap-8">
              {[
                { step: '1', title: 'Discovery', description: 'Understand your pest control needs', duration: '1-2 weeks' },
                { step: '2', title: 'Design', description: 'Create user-friendly app designs', duration: '2-3 weeks' },
                { step: '3', title: 'Development', description: 'Build native iOS and Android apps', duration: '8-12 weeks' },
                { step: '4', title: 'Testing', description: 'Comprehensive testing and QA', duration: '2-3 weeks' },
                { step: '5', title: 'Launch', description: 'App store deployment and launch', duration: '1-2 weeks' },
                { step: '6', title: 'Support', description: 'Ongoing maintenance and updates', duration: 'Ongoing' }
              ].map((phase, index) => (
                <div key={index} className="text-center">
                  <div className="bg-green-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold mx-auto mb-4">
                    {phase.step}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{phase.title}</h3>
                  <p className="text-gray-600 mb-2 text-sm">{phase.description}</p>
                  <span className="text-xs text-green-600 font-semibold">{phase.duration}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Technology Stack */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Modern Mobile App Technology Stack
            </h2>
            <div className="grid lg:grid-cols-4 gap-8">
              {[
                {
                  category: 'Native Development',
                  technologies: ['Swift (iOS)', 'Kotlin (Android)', 'React Native', 'Flutter', 'Xamarin'],
                  description: 'High-performance native apps for iOS and Android platforms.',
                  icon: '📱'
                },
                {
                  category: 'Backend Services',
                  technologies: ['Node.js', 'Python/Django', 'AWS/Firebase', 'Real-time sync', 'Push notifications'],
                  description: 'Robust backend infrastructure for data management and sync.',
                  icon: '⚙️'
                },
                {
                  category: 'AI & Machine Learning',
                  technologies: ['TensorFlow', 'Core ML', 'Computer Vision', 'Natural Language', 'Predictive Analytics'],
                  description: 'AI-powered features for pest identification and recommendations.',
                  icon: '🤖'
                },
                {
                  category: 'Integration & APIs',
                  technologies: ['REST APIs', 'GraphQL', 'Third-party integrations', 'Payment gateways', 'Mapping services'],
                  description: 'Seamless integration with existing systems and services.',
                  icon: '🔗'
                }
              ].map((stack, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4 text-center">{stack.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">{stack.category}</h3>
                  <p className="text-gray-600 mb-4 text-sm">{stack.description}</p>
                  <ul className="space-y-2">
                    {stack.technologies.map((tech, techIndex) => (
                      <li key={techIndex} className="text-sm text-gray-600 text-center">
                        {tech}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Pest Control App Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    BugBusters Pro: 300% Efficiency Increase with Custom App
                  </h3>
                  <p className="text-gray-600 mb-6">
                    BugBusters Pro needed to modernize their operations and improve customer service. 
                    Our custom mobile app solution transformed their business operations and customer satisfaction.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">300%</div>
                      <div className="text-sm text-gray-600">Efficiency Increase</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">95%</div>
                      <div className="text-sm text-gray-600">Customer Satisfaction</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">App Implementation Results:</h4>
                  <ul className="space-y-3">
                    {[
                      '60% reduction in service documentation time',
                      '45% improvement in customer satisfaction',
                      '80% reduction in paperwork and errors',
                      '300% increase in operational efficiency',
                      '50% faster service completion times',
                      '90% technician adoption rate'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Build Your Pest Control App?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Let's discuss your mobile app needs and create a custom solution that transforms 
              your pest control operations and customer experience.
            </p>
            <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
              Schedule Your App Development Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
