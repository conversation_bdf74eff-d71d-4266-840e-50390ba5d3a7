import React from 'react';
import Head from 'next/head';

interface TechnicalSEOProps {
  title: string;
  description: string;
  keywords?: string;
  canonicalUrl?: string;
  ogImage?: string;
  structuredData?: any[];
  breadcrumbs?: Array<{ name: string; url: string }>;
  alternateLanguages?: { [key: string]: string };
  noIndex?: boolean;
  priority?: number;
  changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
}

export const TechnicalSEO: React.FC<TechnicalSEOProps> = ({
  title,
  description,
  keywords,
  canonicalUrl,
  ogImage = '/og-default.jpg',
  structuredData = [],
  breadcrumbs,
  alternateLanguages,
  noIndex = false,
  priority = 0.8,
  changeFrequency = 'weekly'
}) => {
  const baseUrl = 'https://groundupdigital.com';
  const fullCanonicalUrl = canonicalUrl || `${baseUrl}${typeof window !== 'undefined' ? window.location.pathname : ''}`;
  const fullOgImage = `${baseUrl}${ogImage}`;

  // Core Web Vitals optimization
  const coreWebVitalsOptimization = {
    // Largest Contentful Paint (LCP) optimization
    preloadCriticalResources: [
      '/fonts/inter-var.woff2',
      '/images/hero-bg.webp',
      '/images/logo.webp'
    ],
    // First Input Delay (FID) optimization
    deferNonCriticalJS: true,
    // Cumulative Layout Shift (CLS) optimization
    dimensionAttributes: true
  };

  // Breadcrumb structured data
  const breadcrumbStructuredData = breadcrumbs ? {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": `${baseUrl}${crumb.url}`
    }))
  } : null;

  // FAQ structured data for featured snippets
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "What makes GroundUP Digital different from other marketing agencies?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "GroundUP Digital is the pioneer and only agency exclusively focused on landscaping, roofing, and pest control businesses. We provide enterprise-grade solutions at affordable prices, with 365+ successful clients and $78M+ revenue generated."
        }
      },
      {
        "@type": "Question",
        "name": "How quickly can I see results from digital marketing?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Most clients see initial improvements within 30 days, with significant results typically achieved within 90 days. Our proven strategies have helped businesses achieve 300-400% increases in leads and revenue."
        }
      },
      {
        "@type": "Question",
        "name": "Do you only work with landscaping, roofing, and pest control businesses?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, we exclusively serve landscaping, roofing, and pest control businesses. This specialization allows us to deliver superior results because we understand your industry, seasonal patterns, and customer behavior."
        }
      }
    ]
  };

  // How-to structured data for featured snippets
  const howToStructuredData = {
    "@context": "https://schema.org",
    "@type": "HowTo",
    "name": "How to Choose the Right Digital Marketing Agency for Your Local Service Business",
    "description": "A comprehensive guide to selecting a digital marketing agency that specializes in your industry",
    "step": [
      {
        "@type": "HowToStep",
        "name": "Verify Industry Specialization",
        "text": "Choose an agency that exclusively serves your industry (landscaping, roofing, or pest control) for better results."
      },
      {
        "@type": "HowToStep",
        "name": "Check Track Record",
        "text": "Look for proven results with 300%+ lead increases and substantial revenue generation for similar businesses."
      },
      {
        "@type": "HowToStep",
        "name": "Evaluate Pricing Structure",
        "text": "Ensure you're getting enterprise-grade solutions at affordable prices, not overpriced generic services."
      }
    ]
  };

  return (
    <Head>
      {/* Core Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      {keywords && <meta name="keywords" content={keywords} />}
      
      {/* Canonical URL */}
      <link rel="canonical" href={fullCanonicalUrl} />
      
      {/* Robots Meta */}
      <meta 
        name="robots" 
        content={noIndex ? "noindex,nofollow" : "index,follow,max-image-preview:large,max-snippet:-1,max-video-preview:-1"} 
      />
      <meta 
        name="googlebot" 
        content={noIndex ? "noindex,nofollow" : "index,follow,max-image-preview:large,max-snippet:-1,max-video-preview:-1"} 
      />
      
      {/* Open Graph */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={fullCanonicalUrl} />
      <meta property="og:image" content={fullOgImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={title} />
      <meta property="og:site_name" content="GroundUP Digital" />
      <meta property="og:locale" content="en_US" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullOgImage} />
      <meta name="twitter:image:alt" content={title} />
      <meta name="twitter:site" content="@groundupdigital" />
      <meta name="twitter:creator" content="@groundupdigital" />
      
      {/* Additional SEO Meta Tags */}
      <meta name="author" content="GroundUP Digital" />
      <meta name="publisher" content="GroundUP Digital" />
      <meta name="copyright" content="GroundUP Digital" />
      <meta name="language" content="English" />
      <meta name="revisit-after" content="7 days" />
      <meta name="distribution" content="global" />
      <meta name="rating" content="general" />
      
      {/* Geo Tags */}
      <meta name="geo.region" content="US" />
      <meta name="geo.placename" content="United States" />
      <meta name="ICBM" content="39.8283, -98.5795" />
      
      {/* Mobile Optimization */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
      <meta name="format-detection" content="telephone=yes" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="GroundUP Digital" />
      
      {/* Performance Optimization */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* Core Web Vitals Optimization */}
      {coreWebVitalsOptimization.preloadCriticalResources.map((resource, index) => (
        <link 
          key={index}
          rel="preload" 
          href={resource} 
          as={resource.includes('.woff2') ? 'font' : 'image'} 
          type={resource.includes('.woff2') ? 'font/woff2' : undefined}
          crossOrigin={resource.includes('.woff2') ? 'anonymous' : undefined}
        />
      ))}
      
      {/* Alternate Languages */}
      {alternateLanguages && Object.entries(alternateLanguages).map(([lang, url]) => (
        <link key={lang} rel="alternate" hrefLang={lang} href={url} />
      ))}
      
      {/* Structured Data */}
      {breadcrumbStructuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(breadcrumbStructuredData)
          }}
        />
      )}
      
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqStructuredData)
        }}
      />
      
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(howToStructuredData)
        }}
      />
      
      {structuredData.map((data, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(data)
          }}
        />
      ))}
      
      {/* Security Headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
      
      {/* Theme and Branding */}
      <meta name="theme-color" content="#2563eb" />
      <meta name="msapplication-TileColor" content="#2563eb" />
      <meta name="msapplication-config" content="/browserconfig.xml" />
      
      {/* Favicon and Icons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Sitemap */}
      <link rel="sitemap" type="application/xml" href="/sitemap.xml" />
      
      {/* RSS Feed */}
      <link rel="alternate" type="application/rss+xml" title="GroundUP Digital Blog" href="/feed.xml" />
      
      {/* Preload Critical CSS */}
      <link rel="preload" href="/css/critical.css" as="style" onLoad="this.onload=null;this.rel='stylesheet'" />
      <noscript><link rel="stylesheet" href="/css/critical.css" /></noscript>
    </Head>
  );
};
