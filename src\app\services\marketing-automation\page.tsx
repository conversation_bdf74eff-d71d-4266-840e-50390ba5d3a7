import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Marketing Automation for Local Service Businesses | GroundUPDigital',
  description: 'Automate your marketing and nurture leads 24/7 with our marketing automation solutions for landscapers, roofers, and pest control companies.',
  keywords: 'marketing automation, email automation, lead nurturing, local service automation, CRM automation',
  openGraph: {
    title: 'Marketing Automation for Local Service Businesses | GroundUPDigital',
    description: 'Automate your marketing and nurture leads 24/7 with our marketing automation solutions for landscapers, roofers, and pest control companies.',
    type: 'website',
  },
};

export default function MarketingAutomationPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-green-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Marketing Automation That <span className="text-blue-600">Works 24/7</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Stop losing leads and start converting them automatically. Our marketing automation systems 
              nurture prospects, follow up with customers, and drive repeat business while you focus on delivering great service.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Automation Strategy
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                See Automation Examples
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Why Marketing Automation is Essential
            </h2>
            <div className="grid md:grid-cols-3 gap-8 mb-16">
              {[
                { 
                  benefit: '60%', 
                  description: 'Reduction in manual follow-up time',
                  icon: '⏰'
                },
                { 
                  benefit: '45%', 
                  description: 'Increase in lead conversion rates',
                  icon: '📈'
                },
                { 
                  benefit: '24/7', 
                  description: 'Lead nurturing and customer engagement',
                  icon: '🔄'
                }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-4">{stat.benefit}</div>
                  <p className="text-gray-600">{stat.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Automation Types */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Complete Marketing Automation Solutions
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: 'Lead Nurturing Sequences',
                  description: 'Automated email sequences that educate prospects and guide them toward hiring your services.',
                  features: ['Welcome series', 'Educational content', 'Service explanations', 'Testimonial sharing'],
                  icon: '🎯'
                },
                {
                  title: 'Follow-up Automation',
                  description: 'Never miss a follow-up again with automated sequences for quotes, appointments, and proposals.',
                  features: ['Quote follow-ups', 'Appointment reminders', 'Proposal tracking', 'Decision deadlines'],
                  icon: '📞'
                },
                {
                  title: 'Customer Retention',
                  description: 'Keep customers coming back with automated retention campaigns and service reminders.',
                  features: ['Service reminders', 'Seasonal campaigns', 'Loyalty programs', 'Referral requests'],
                  icon: '🔄'
                },
                {
                  title: 'Review Generation',
                  description: 'Automatically request reviews from satisfied customers to build your online reputation.',
                  features: ['Post-service surveys', 'Review requests', 'Review monitoring', 'Response automation'],
                  icon: '⭐'
                },
                {
                  title: 'Seasonal Campaigns',
                  description: 'Automated campaigns that promote relevant services based on seasons and weather.',
                  features: ['Weather triggers', 'Seasonal services', 'Maintenance reminders', 'Emergency alerts'],
                  icon: '🌦️'
                },
                {
                  title: 'Abandoned Quote Recovery',
                  description: 'Recover lost opportunities with automated sequences for abandoned quotes and estimates.',
                  features: ['Quote reminders', 'Incentive offers', 'Objection handling', 'Alternative solutions'],
                  icon: '💰'
                }
              ].map((automation, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{automation.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{automation.title}</h3>
                  <p className="text-gray-600 mb-4">{automation.description}</p>
                  <ul className="space-y-2">
                    {automation.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Industry-Specific Automation */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Industry-Specific Automation Workflows
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  industry: 'Landscaping',
                  workflows: [
                    'Seasonal service reminders (spring cleanup, fall cleanup)',
                    'Weather-triggered campaigns (storm damage, drought alerts)',
                    'Maintenance schedule automation',
                    'Design consultation follow-ups',
                    'Irrigation system check reminders'
                  ],
                  color: 'green'
                },
                {
                  industry: 'Roofing',
                  workflows: [
                    'Storm damage response automation',
                    'Insurance claim assistance sequences',
                    'Annual inspection reminders',
                    'Warranty follow-up campaigns',
                    'Emergency service availability alerts'
                  ],
                  color: 'blue'
                },
                {
                  industry: 'Pest Control',
                  workflows: [
                    'Recurring service reminders',
                    'Seasonal pest prevention campaigns',
                    'Treatment effectiveness follow-ups',
                    'Emergency pest response automation',
                    'Preventive maintenance scheduling'
                  ],
                  color: 'red'
                }
              ].map((industry, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg border-l-4 border-blue-600">
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">{industry.industry} Automation</h3>
                  <ul className="space-y-3">
                    {industry.workflows.map((workflow, workflowIndex) => (
                      <li key={workflowIndex} className="flex items-start text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        <span className="text-sm">{workflow}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Automation Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Our Automation Implementation Process
            </h2>
            <div className="grid lg:grid-cols-4 gap-8">
              {[
                {
                  step: '1',
                  title: 'Strategy & Planning',
                  description: 'Analyze your customer journey and identify automation opportunities.',
                  icon: '📋'
                },
                {
                  step: '2',
                  title: 'Workflow Design',
                  description: 'Create custom automation workflows tailored to your business processes.',
                  icon: '⚙️'
                },
                {
                  step: '3',
                  title: 'Content Creation',
                  description: 'Develop compelling email sequences and marketing materials.',
                  icon: '✍️'
                },
                {
                  step: '4',
                  title: 'Testing & Optimization',
                  description: 'Launch, monitor, and continuously optimize your automation campaigns.',
                  icon: '📊'
                }
              ].map((process, index) => (
                <div key={index} className="text-center">
                  <div className="bg-blue-600 text-white w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    {process.step}
                  </div>
                  <div className="text-4xl mb-4">{process.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{process.title}</h3>
                  <p className="text-gray-600">{process.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Marketing Automation Results
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                { metric: '85%', label: 'Time Saved on Follow-ups', icon: '⏱️' },
                { metric: '40%', label: 'Increase in Lead Conversion', icon: '📈' },
                { metric: '65%', label: 'More Repeat Customers', icon: '🔄' },
                { metric: '90%', label: 'Client Satisfaction Rate', icon: '😊' }
              ].map((result, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{result.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{result.metric}</div>
                  <div className="text-gray-600">{result.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Automate Your Marketing?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Let us set up marketing automation that works 24/7 to nurture leads and grow your business.
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Get Your Automation Strategy
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
