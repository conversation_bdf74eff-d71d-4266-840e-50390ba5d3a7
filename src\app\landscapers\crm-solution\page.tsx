import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'CRM Solutions for Landscaping Companies | Streamline Operations | GroundUPDigital',
  description: 'Streamline your landscaping business with custom CRM solutions. Manage customers, schedule services, track projects, and automate operations for maximum efficiency.',
  keywords: 'landscaping CRM, lawn care CRM, landscape design CRM, landscaping software, customer management',
  openGraph: {
    title: 'CRM Solutions for Landscaping Companies | Streamline Operations | GroundUPDigital',
    description: 'Streamline your landscaping business with custom CRM solutions. Manage customers, schedule services, track projects, and automate operations for maximum efficiency.',
    type: 'website',
  },
};

export default function LandscapingCRMPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-blue-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              CRM Solutions for <span className="text-green-600">Landscaping Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Streamline your landscaping business operations with custom CRM solutions. Manage customers, 
              schedule services, track projects, and automate workflows for maximum efficiency and growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Get Free CRM Demo
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View CRM Features
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* CRM Benefits */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Transform Your Landscaping Business Operations
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '40%', label: 'Time Saved on Admin Tasks', icon: '⏰' },
                { metric: '60%', label: 'Increase in Customer Retention', icon: '🤝' },
                { metric: '85%', label: 'Improvement in Scheduling Efficiency', icon: '📅' },
                { metric: '300%', label: 'ROI Within First Year', icon: '💰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-green-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Core CRM Features */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Comprehensive CRM Features for Landscapers
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Customer Management',
                  description: 'Complete customer profiles with service history, preferences, and communication logs.',
                  capabilities: ['Contact information', 'Service history', 'Property details', 'Communication logs', 'Billing information'],
                  icon: '👥'
                },
                {
                  feature: 'Scheduling & Dispatch',
                  description: 'Intelligent scheduling system with route optimization and crew management.',
                  capabilities: ['Automated scheduling', 'Route optimization', 'Crew assignments', 'Real-time updates', 'Mobile access'],
                  icon: '📅'
                },
                {
                  feature: 'Project Management',
                  description: 'Track landscaping projects from estimate to completion with milestone tracking.',
                  capabilities: ['Project timelines', 'Milestone tracking', 'Resource allocation', 'Progress photos', 'Client updates'],
                  icon: '🏗️'
                },
                {
                  feature: 'Estimating & Invoicing',
                  description: 'Professional estimates and automated invoicing with payment processing.',
                  capabilities: ['Digital estimates', 'Automated invoicing', 'Payment processing', 'Recurring billing', 'Financial reporting'],
                  icon: '💳'
                },
                {
                  feature: 'Inventory Management',
                  description: 'Track equipment, materials, and supplies with automated reorder alerts.',
                  capabilities: ['Equipment tracking', 'Material inventory', 'Reorder alerts', 'Vendor management', 'Cost tracking'],
                  icon: '📦'
                },
                {
                  feature: 'Reporting & Analytics',
                  description: 'Comprehensive business insights with customizable reports and dashboards.',
                  capabilities: ['Revenue reports', 'Customer analytics', 'Performance metrics', 'Custom dashboards', 'Trend analysis'],
                  icon: '📊'
                }
              ].map((item, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{item.feature}</h3>
                  <p className="text-gray-600 mb-4">{item.description}</p>
                  <ul className="space-y-2">
                    {item.capabilities.map((capability, capIndex) => (
                      <li key={capIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {capability}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Seasonal Workflow Management */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seasonal Workflow Management
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  season: 'Spring',
                  workflows: ['Spring cleanup scheduling', 'Mulching appointments', 'Planting project management', 'Irrigation system startups'],
                  automation: 'Automated spring service reminders and scheduling',
                  icon: '🌸'
                },
                {
                  season: 'Summer',
                  workflows: ['Weekly mowing schedules', 'Watering system monitoring', 'Pest treatment tracking', 'Landscape maintenance'],
                  automation: 'Recurring service automation and weather-based adjustments',
                  icon: '☀️'
                },
                {
                  season: 'Fall',
                  workflows: ['Leaf removal scheduling', 'Winterization services', 'Equipment maintenance', 'End-of-season cleanups'],
                  automation: 'Fall preparation workflows and equipment tracking',
                  icon: '🍂'
                },
                {
                  season: 'Winter',
                  workflows: ['Snow removal dispatch', 'Equipment servicing', 'Planning next season', 'Customer retention'],
                  automation: 'Weather-triggered snow removal and planning tools',
                  icon: '❄️'
                }
              ].map((season, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4 text-center">{season.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">{season.season}</h3>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-700 mb-2">Key Workflows:</h4>
                    <ul className="space-y-1">
                      {season.workflows.map((workflow, workflowIndex) => (
                        <li key={workflowIndex} className="text-sm text-gray-600">
                          • {workflow}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="bg-green-50 rounded-lg p-3">
                    <h5 className="font-semibold text-green-800 text-sm mb-1">Automation:</h5>
                    <p className="text-green-700 text-xs">{season.automation}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Mobile CRM Features */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Mobile CRM for Field Teams
              </h2>
              <p className="text-xl text-green-100">
                Keep your crews connected and productive with full mobile CRM access.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  feature: 'Field Data Collection',
                  description: 'Capture job details, photos, and customer signatures directly from the field.',
                  capabilities: ['Photo documentation', 'Digital signatures', 'Time tracking', 'GPS location', 'Offline access'],
                  icon: '📱'
                },
                {
                  feature: 'Real-Time Updates',
                  description: 'Instant communication between office and field teams with live updates.',
                  capabilities: ['Job status updates', 'Schedule changes', 'Customer notifications', 'Team messaging', 'Emergency alerts'],
                  icon: '🔄'
                },
                {
                  feature: 'Route Optimization',
                  description: 'Intelligent routing to minimize travel time and maximize productivity.',
                  capabilities: ['GPS navigation', 'Traffic optimization', 'Multi-stop routing', 'Fuel tracking', 'Time estimation'],
                  icon: '🗺️'
                }
              ].map((feature, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{feature.feature}</h3>
                  <p className="text-green-100 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.capabilities.map((capability, capIndex) => (
                      <li key={capIndex} className="text-sm text-green-100">
                        • {capability}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Integration Capabilities */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Seamless Integrations for Complete Business Management
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Popular Integrations:</h3>
                <div className="grid grid-cols-2 gap-4">
                  {[
                    { name: 'QuickBooks', category: 'Accounting', icon: '💼' },
                    { name: 'Google Calendar', category: 'Scheduling', icon: '📅' },
                    { name: 'Mailchimp', category: 'Marketing', icon: '📧' },
                    { name: 'Stripe', category: 'Payments', icon: '💳' },
                    { name: 'Weather API', category: 'Weather Data', icon: '🌤️' },
                    { name: 'Google Maps', category: 'Navigation', icon: '🗺️' }
                  ].map((integration, index) => (
                    <div key={index} className="bg-white p-4 rounded-lg shadow-lg text-center">
                      <div className="text-2xl mb-2">{integration.icon}</div>
                      <h4 className="font-semibold text-gray-900">{integration.name}</h4>
                      <p className="text-sm text-gray-600">{integration.category}</p>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Integration Benefits:</h3>
                <ul className="space-y-4">
                  {[
                    'Eliminate double data entry across systems',
                    'Sync financial data with accounting software',
                    'Automate marketing campaigns based on service history',
                    'Process payments directly within the CRM',
                    'Weather-based scheduling adjustments',
                    'Real-time GPS tracking and route optimization'
                  ].map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{benefit}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-8 bg-green-50 rounded-lg p-6">
                  <h4 className="font-semibold text-green-800 mb-2">Custom Integrations Available</h4>
                  <p className="text-green-700 text-sm">
                    Need integration with specific software? We can build custom integrations 
                    to connect your CRM with any business system you use.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Landscaping CRM Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    GreenScape Pro: 300% Business Growth with Custom CRM
                  </h3>
                  <p className="text-gray-600 mb-6">
                    GreenScape Pro was struggling with manual processes, missed appointments, and poor customer communication. 
                    Our custom CRM solution transformed their operations and accelerated growth.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">300%</div>
                      <div className="text-sm text-gray-600">Revenue Growth</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">95%</div>
                      <div className="text-sm text-gray-600">Customer Retention</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">CRM Implementation Results:</h4>
                  <ul className="space-y-3">
                    {[
                      '40% reduction in administrative time',
                      '85% improvement in scheduling efficiency',
                      '60% increase in customer retention',
                      '200% growth in recurring revenue',
                      '50% faster invoice processing',
                      '90% reduction in missed appointments'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-green-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Implementation Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              CRM Implementation Process
            </h2>
            <div className="grid md:grid-cols-5 gap-8">
              {[
                { step: '1', title: 'Discovery', description: 'Analyze your current processes and identify improvement opportunities', duration: '1 week' },
                { step: '2', title: 'Design', description: 'Create custom CRM solution tailored to your landscaping business needs', duration: '2 weeks' },
                { step: '3', title: 'Development', description: 'Build and configure your CRM system with all required features', duration: '4-6 weeks' },
                { step: '4', title: 'Training', description: 'Comprehensive training for your team on the new CRM system', duration: '1 week' },
                { step: '5', title: 'Launch', description: 'Go live with ongoing support and optimization', duration: 'Ongoing' }
              ].map((phase, index) => (
                <div key={index} className="text-center">
                  <div className="bg-green-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                    {phase.step}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{phase.title}</h3>
                  <p className="text-gray-600 mb-2">{phase.description}</p>
                  <span className="text-sm text-green-600 font-semibold">{phase.duration}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Streamline Your Landscaping Operations?
            </h2>
            <p className="text-xl text-green-100 mb-8">
              Get a free CRM consultation and discover how we can help you manage customers, 
              automate workflows, and grow your landscaping business.
            </p>
            <button className="bg-white text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Get Your Free Landscaping CRM Consultation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
