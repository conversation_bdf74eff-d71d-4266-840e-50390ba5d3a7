'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Search, 
  MapPin, 
  TrendingUp, 
  Star, 
  CheckCircle,
  ArrowRight,
  Target,
  BarChart3,
  Users
} from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

const seoServices = [
  {
    icon: MapPin,
    title: 'Local SEO',
    description: 'Dominate local search results and get found by customers in your service area.',
    features: ['Google My Business optimization', 'Local citation building', 'Location-based keywords', 'Local schema markup']
  },
  {
    icon: Search,
    title: 'Keyword Research',
    description: 'Target the right keywords that your potential customers are actually searching for.',
    features: ['Industry-specific keywords', 'Competitor analysis', 'Search volume analysis', 'Long-tail opportunities']
  },
  {
    icon: TrendingUp,
    title: 'Technical SEO',
    description: 'Optimize your website\'s technical foundation for better search engine visibility.',
    features: ['Site speed optimization', 'Mobile optimization', 'Schema markup', 'XML sitemaps']
  },
  {
    icon: Star,
    title: 'Content Optimization',
    description: 'Create and optimize content that ranks well and converts visitors into customers.',
    features: ['Service page optimization', 'Blog content strategy', 'Meta tag optimization', 'Internal linking']
  }
];

const results = [
  { metric: '300%', label: 'Average Traffic Increase', icon: TrendingUp },
  { metric: '#1', label: 'Local Rankings Achieved', icon: Target },
  { metric: '85%', label: 'More Qualified Leads', icon: Users },
  { metric: '4.8★', label: 'Average Review Rating', icon: Star }
];

const industryStrategies = [
  {
    industry: 'Landscaping',
    keywords: ['landscaping near me', 'lawn care services', 'landscape design'],
    strategies: ['Seasonal content calendar', 'Project showcase optimization', 'Service area targeting'],
    color: 'green'
  },
  {
    industry: 'Roofing',
    keywords: ['roof repair near me', 'roofing contractor', 'roof replacement'],
    strategies: ['Emergency service optimization', 'Insurance claim content', 'Material-specific pages'],
    color: 'blue'
  },
  {
    industry: 'Pest Control',
    keywords: ['pest control near me', 'exterminator', 'bed bug treatment'],
    strategies: ['Pest-specific landing pages', 'Emergency response SEO', 'Treatment guarantee content'],
    color: 'orange'
  }
];

export default function SEOPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-8"
            >
              <Badge variant="primary" size="lg">
                SEO Services
              </Badge>
              
              <h1 className="heading-1 text-gray-900">
                Dominate Local Search &{' '}
                <span className="gradient-text">Get Found</span>{' '}
                by More Customers
              </h1>
              
              <p className="text-large text-gray-600">
                Our proven SEO strategies help local service businesses rank #1 in Google searches. 
                Get more visibility, more leads, and more customers with our specialized local SEO services.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-gray-700">Rank #1 for local searches</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-gray-700">300% average traffic increase</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-gray-700">More qualified leads</span>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Button variant="primary" size="lg">
                  Get Free SEO Audit
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button variant="outline" size="lg">
                  View Case Studies
                </Button>
              </div>
            </motion.div>
            
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="bg-white rounded-2xl shadow-2xl p-6 border border-gray-100">
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold text-gray-900">SEO Performance</h3>
                    <Search className="h-5 w-5 text-blue-600" />
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Local Rankings</span>
                      <span className="font-bold text-green-600">#1</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Organic Traffic</span>
                      <span className="font-bold text-blue-600">+285%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Lead Quality</span>
                      <span className="font-bold text-purple-600">+150%</span>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-end space-x-2 h-16">
                      {[20, 35, 45, 60, 80, 95].map((height, index) => (
                        <motion.div
                          key={index}
                          initial={{ height: 0 }}
                          animate={{ height: `${height}%` }}
                          transition={{ delay: 1 + index * 0.1, duration: 0.6 }}
                          className="bg-blue-500 rounded-t flex-1"
                        />
                      ))}
                    </div>
                    <div className="text-sm text-gray-600 mt-2">Traffic Growth Over 6 Months</div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </Container>
      </Section>

      {/* Services Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="heading-2 text-gray-900 mb-6">
              Comprehensive SEO Services
            </h2>
            <p className="text-large text-gray-600">
              Everything you need to dominate local search results and attract more customers.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {seoServices.map((service, index) => {
              const Icon = service.icon;
              return (
                <motion.div
                  key={service.title}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Card className="h-full">
                    <CardContent className="space-y-6">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Icon className="h-6 w-6" />
                        </div>
                        <div className="space-y-2">
                          <h3 className="text-xl font-bold text-gray-900">
                            {service.title}
                          </h3>
                          <p className="text-gray-600">
                            {service.description}
                          </p>
                        </div>
                      </div>
                      
                      <ul className="space-y-2">
                        {service.features.map((feature) => (
                          <li key={feature} className="flex items-center">
                            <CheckCircle className="h-4 w-4 text-green-600 mr-3 flex-shrink-0" />
                            <span className="text-gray-700 text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </Container>
      </Section>

      {/* Results Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="heading-2 text-gray-900 mb-6">
              Proven Results for Local Businesses
            </h2>
            <p className="text-large text-gray-600">
              Our SEO strategies deliver measurable results that impact your bottom line.
            </p>
          </div>
          
          <div className="grid md:grid-cols-4 gap-8">
            {results.map((result, index) => {
              const Icon = result.icon;
              return (
                <motion.div
                  key={result.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center"
                >
                  <Card>
                    <CardContent className="space-y-4">
                      <div className="w-12 h-12 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center mx-auto">
                        <Icon className="h-6 w-6" />
                      </div>
                      <div className="text-3xl font-bold text-gray-900">
                        {result.metric}
                      </div>
                      <div className="text-gray-600">
                        {result.label}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </Container>
      </Section>

      {/* Industry Strategies */}
      <Section background="white" padding="xl">
        <Container>
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="heading-2 text-gray-900 mb-6">
              Industry-Specific SEO Strategies
            </h2>
            <p className="text-large text-gray-600">
              We understand the unique search patterns and competition in your industry.
            </p>
          </div>
          
          <div className="grid lg:grid-cols-3 gap-8">
            {industryStrategies.map((strategy, index) => (
              <motion.div
                key={strategy.industry}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
              >
                <Card>
                  <CardContent className="space-y-6">
                    <div className="text-center">
                      <h3 className="text-2xl font-bold text-gray-900 mb-2">
                        {strategy.industry}
                      </h3>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">Target Keywords:</h4>
                      <div className="flex flex-wrap gap-2">
                        {strategy.keywords.map((keyword) => (
                          <Badge key={keyword} variant="secondary" size="sm">
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-3">SEO Strategies:</h4>
                      <ul className="space-y-2">
                        {strategy.strategies.map((item) => (
                          <li key={item} className="flex items-center">
                            <CheckCircle className="h-4 w-4 text-green-600 mr-3 flex-shrink-0" />
                            <span className="text-gray-700 text-sm">{item}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="heading-2 text-gray-900 mb-6">
              Ready to Dominate Local Search?
            </h2>
            <p className="text-large text-gray-600 mb-8">
              Get a free SEO audit and discover how we can help you rank #1 in your local market.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="primary" size="lg">
                Get Free SEO Audit
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button variant="outline" size="lg">
                Schedule Consultation
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
