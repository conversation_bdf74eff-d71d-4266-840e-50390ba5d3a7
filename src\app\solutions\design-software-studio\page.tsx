import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { <PERSON>R<PERSON>, Palette, Monitor, Layers, TreePine, Star, CheckCircle, Phone, Zap } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Landscape Design Software & Virtual Studios | GroundUP Digital',
  description: 'Professional landscape design software solutions and virtual design studios for landscaping businesses. 3D design tools, plant libraries, and client presentation systems.',
  keywords: 'landscape design software, landscaping design tools, 3D landscape design, virtual design studio, landscape CAD software, design presentation tools',
};

const designFeatures = [
  {
    icon: Palette,
    title: 'Professional 3D Design Tools',
    description: 'Industry-leading 3D landscape design software with comprehensive plant and hardscape libraries.',
    benefits: ['Extensive plant databases', 'Hardscape element libraries', 'Terrain modeling tools', 'Seasonal visualization']
  },
  {
    icon: Monitor,
    title: 'Virtual Design Studios',
    description: 'Cloud-based design studios that enable collaboration and remote design capabilities.',
    benefits: ['Cloud-based access', 'Team collaboration tools', 'Version control', 'Remote client presentations']
  },
  {
    icon: Layers,
    title: 'Client Presentation Systems',
    description: 'Professional presentation tools that help close more design deals with stunning visuals.',
    benefits: ['Interactive 3D walkthroughs', 'Before/after comparisons', 'Material selection tools', 'Cost estimation integration']
  }
];

const softwareTypes = [
  {
    name: 'Professional CAD Suite',
    description: 'Complete landscape design software with advanced CAD capabilities',
    features: ['2D/3D design tools', 'Site analysis features', 'Grading & drainage tools', 'Construction documentation', 'Plant scheduling', 'Irrigation design']
  },
  {
    name: 'Virtual Reality Studio',
    description: 'Immersive VR design environment for client presentations',
    features: ['VR headset integration', 'Real-time design modifications', 'Immersive client experiences', 'Seasonal change previews', 'Material texture visualization', 'Lighting simulation']
  },
  {
    name: 'Mobile Design Platform',
    description: 'Tablet-based design tools for on-site consultations and quick concepts',
    features: ['Touch-based design interface', 'Photo integration', 'Quick sketch tools', 'Plant identification', 'Instant estimates', 'Client approval workflow']
  },
  {
    name: 'Collaborative Design Hub',
    description: 'Team-based design platform for large landscaping companies',
    features: ['Multi-user collaboration', 'Project management integration', 'Design approval workflows', 'Client portal access', 'Team communication tools', 'Resource sharing']
  }
];

const packages = [
  {
    name: "Design Essentials",
    price: "$1,997",
    period: "/month",
    description: "Professional design tools for small to medium landscape design businesses",
    features: [
      "Professional 3D design software",
      "Basic plant & hardscape libraries",
      "Client presentation tools",
      "Training & certification",
      "Technical support",
      "Monthly software updates"
    ],
    popular: false
  },
  {
    name: "Studio Professional",
    price: "$3,997",
    period: "/month", 
    description: "Complete design studio solution for growing landscape design companies",
    features: [
      "Advanced design software suite",
      "Virtual reality capabilities",
      "Extensive design libraries",
      "Cloud collaboration platform",
      "Custom training program",
      "Dedicated design specialist"
    ],
    popular: true
  },
  {
    name: "Enterprise Design Hub",
    price: "$7,997",
    period: "/month",
    description: "Custom design ecosystem for large landscape architecture firms",
    features: [
      "Custom software development",
      "Multi-location studio setup",
      "Advanced collaboration tools",
      "Custom plant libraries",
      "White-label client solutions",
      "Ongoing development support"
    ],
    popular: false
  }
];

const caseStudy = {
  company: "Artisan Landscape Design",
  location: "San Diego, CA",
  results: [
    { metric: "Design Efficiency", improvement: "+180%" },
    { metric: "Client Approval Rate", improvement: "+95%" },
    { metric: "Project Value", improvement: "+140%" },
    { metric: "Design Revisions", improvement: "-60%" }
  ]
};

const designCapabilities = [
  { capability: "3D Terrain Modeling", description: "Advanced topographical design and grading tools" },
  { capability: "Plant Growth Simulation", description: "Visualize landscape maturity over time" },
  { capability: "Irrigation System Design", description: "Integrated sprinkler and drip system planning" },
  { capability: "Hardscape Integration", description: "Patios, walkways, retaining walls, and structures" },
  { capability: "Lighting Design", description: "Landscape lighting placement and effects" },
  { capability: "Cost Estimation", description: "Real-time project cost calculations" }
];

export default function DesignSoftwareStudioPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Palette className="w-4 h-4" />
              <span className="text-sm font-semibold">Design Software & Virtual Studios</span>
            </div>
            
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Professional Design Tools for{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                Landscape Professionals
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              Advanced landscape design software and virtual studios that help landscaping business owners create stunning designs, 
              impress clients, and close more high-value projects.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Monitor className="w-5 h-5 mr-2" />
                See Design Demo
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                View Design Gallery
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '180%', label: 'Design Efficiency' },
                { number: '95%', label: 'Client Approval Rate' },
                { number: '140%', label: 'Project Value Increase' },
                { number: '60+', label: 'Design Studios Built' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Features Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Professional Design Solutions for Landscaping Business Success
            </h2>
            <p className="text-lg text-gray-600">
              Our design software and virtual studios are specifically created for landscaping business owners who want to 
              deliver professional presentations and win more high-value design projects.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {designFeatures.map((feature, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 mb-4">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Design Capabilities */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Comprehensive Design Capabilities
            </h2>
            <p className="text-lg text-gray-600">
              Everything landscaping professionals need to create detailed, professional designs that win projects.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-6">
            {designCapabilities.map((item, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="flex items-center mb-3">
                    <TreePine className="w-5 h-5 text-green-600 mr-2" />
                    <h3 className="text-lg font-bold text-gray-900">{item.capability}</h3>
                  </div>
                  <p className="text-gray-600 text-sm">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Software Types Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Design Solutions for Every Landscaping Business Type
            </h2>
            <p className="text-lg text-gray-600">
              From solo designers to large landscape architecture firms, we have design solutions that fit your business needs.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {softwareTypes.map((software, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{software.name}</h3>
                  <p className="text-gray-600 mb-4">{software.description}</p>
                  <ul className="space-y-2">
                    {software.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Case Study Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Design Software Success Story: Transforming Presentations
              </h2>
              <p className="text-lg text-gray-600">
                See how {caseStudy.company} used our design software to revolutionize their client presentations and win more projects
              </p>
            </div>
            
            <Card className="bg-white shadow-xl">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{caseStudy.company}</h3>
                    <p className="text-gray-600 mb-6">
                      A premium landscape design firm in {caseStudy.location} needed professional design tools to compete with larger firms. 
                      Our design software and virtual studio transformed their capabilities and client presentations.
                    </p>
                    <Button className="bg-blue-600 text-white hover:bg-blue-700">
                      Read Full Case Study
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {caseStudy.results.map((result, index) => (
                      <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600 mb-1">{result.improvement}</div>
                        <div className="text-sm text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Container>
      </Section>

      {/* Pricing Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              Design Software Packages for Landscaping Professionals
            </h2>
            <p className="text-lg text-gray-600">
              Professional design software solutions designed specifically for landscaping business owners. 
              Choose the package that matches your design capabilities and business goals.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {packages.map((pkg, index) => (
              <Card key={index} className={`relative h-full ${pkg.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                    <div className="text-3xl font-bold text-gray-900 mb-1">
                      {pkg.price}<span className="text-lg text-gray-600">{pkg.period}</span>
                    </div>
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                  </div>
                  
                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${pkg.popular ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-100 text-gray-900 hover:bg-gray-200'}`}
                  >
                    Get Started
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* FAQ Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Frequently Asked Questions About Design Software for Landscaping
              </h2>
              <p className="text-lg text-gray-600">
                Common questions landscaping business owners have about landscape design software and digital design tools.
              </p>
            </div>

            <div className="space-y-6">
              {[
                {
                  question: "Do I need design software if I'm not a landscape designer?",
                  answer: "Yes! Even basic maintenance companies benefit from design software for property planning, client visualization, project proposals, and upselling design services. It helps communicate ideas clearly and increases project values by 30-50%."
                },
                {
                  question: "How long does it take to learn landscaping design software?",
                  answer: "Basic proficiency takes 2-4 weeks with our training program. Advanced features require 2-3 months of practice. We provide comprehensive training, templates, and ongoing support to accelerate your learning curve."
                },
                {
                  question: "Can design software help me win more landscaping bids?",
                  answer: "Absolutely! Professional 3D designs and detailed proposals win 60-80% more bids than basic estimates. Clients can visualize the final result, understand the value, and feel confident in your professionalism and expertise."
                },
                {
                  question: "What's the difference between basic and professional design software?",
                  answer: "Professional software offers 3D rendering, plant databases, irrigation planning, lighting design, cost estimation, seasonal visualization, and client presentation tools. Basic software typically only handles 2D layouts and simple plant placement."
                },
                {
                  question: "Can design software integrate with my landscaping business operations?",
                  answer: "Yes! Our design software connects with estimating tools, project management systems, material ordering platforms, and customer portals, creating seamless workflows from initial design to project completion and maintenance."
                }
              ].map((faq, index) => (
                <div key={index} className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-3">{faq.question}</h3>
                  <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Elevate Your Landscape Design Capabilities?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Get a free design software consultation and discover how professional design tools can help your landscaping business 
              win more high-value projects.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Monitor className="w-5 h-5 mr-2" />
                Get Design Consultation
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/partnership" className="flex items-center">
                  Schedule Demo
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
