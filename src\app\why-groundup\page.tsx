import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { ArrowRight, Target, Users, Zap, Award, CheckCircle, Phone } from 'lucide-react';
import { Container, Section } from '@/components/ui/Container';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const metadata: Metadata = {
  title: 'Why Choose GroundUP Digital | Exclusive Landscaping Business Partner',
  description: 'Discover why 200+ landscaping business owners choose GroundUP Digital as their exclusive digital growth partner. Premium solutions, proven results.',
  keywords: 'why choose GroundUP Digital, landscaping marketing agency, best landscaping digital marketing, landscaping business growth partner',
};

const whyChooseUs = [
  {
    icon: Target,
    title: 'Exclusive Landscaping Focus',
    description: 'We serve ONLY landscaping businesses. Every strategy, every tool, every innovation is designed specifically for your industry.',
    benefits: ['100% landscaping specialization', 'Industry-specific expertise', 'Landscaping-only case studies', 'Deep market understanding']
  },
  {
    icon: Zap,
    title: 'Proven Track Record',
    description: 'Our landscaping clients see average lead increases of 247% and revenue growth of 156% within the first year.',
    benefits: ['247% average lead increase', '156% revenue growth', '89% client retention rate', '200+ successful partnerships']
  },
  {
    icon: Users,
    title: 'Premium Partnership Approach',
    description: 'We don\'t just provide services—we become your dedicated digital growth partner with white-glove support.',
    benefits: ['Dedicated account management', 'Strategic growth planning', 'Ongoing optimization', 'Executive-level support']
  }
];

const differentiators = [
  {
    title: 'Industry Specialization',
    description: 'While other agencies spread across multiple industries, we focus exclusively on landscaping businesses.',
    advantage: 'Deep expertise in landscaping customer behavior, seasonal trends, and industry-specific challenges.'
  },
  {
    title: 'Advanced Technology Stack',
    description: 'We leverage cutting-edge AI, AR/VR, and IoT technology specifically for landscaping applications.',
    advantage: 'Access to technology solutions that most landscaping businesses can\'t develop in-house.'
  },
  {
    title: 'Comprehensive Ecosystem',
    description: 'We don\'t just do marketing—we architect complete digital ecosystems for business transformation.',
    advantage: 'Integrated solutions that work together to maximize your business growth and efficiency.'
  },
  {
    title: 'Results-Driven Partnership',
    description: 'Our success is directly tied to your business growth. We measure success by your revenue increases.',
    advantage: 'Aligned incentives ensure we\'re always working toward your business success, not just campaign metrics.'
  }
];

const testimonials = [
  {
    name: "Mike Rodriguez",
    company: "Verde Landscapes",
    location: "Austin, TX",
    quote: "GroundUP Digital transformed our business. Their landscaping expertise and advanced technology helped us grow from $800K to $2.1M in revenue in just 18 months.",
    results: "+163% Revenue Growth"
  },
  {
    name: "Sarah Chen",
    company: "Premier Outdoor Solutions",
    location: "Denver, CO", 
    quote: "Finally, a marketing partner that truly understands landscaping. Their industry focus and premium approach delivered results we never thought possible.",
    results: "+290% Lead Increase"
  }
];

export default function WhyGroundUPPage() {
  return (
    <>
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-4xl mx-auto text-center text-white">
            <h1 className="text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight">
              Why 200+ Landscaping Business Owners Choose{' '}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-blue-300">
                GroundUP Digital
              </span>
            </h1>
            
            <p className="text-lg xs:text-xl sm:text-2xl mb-8 text-blue-100 leading-relaxed">
              We're not just another marketing agency. We're the exclusive digital growth partner for landscaping businesses, 
              delivering premium solutions that transform companies from the ground up.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Phone className="w-5 h-5 mr-2" />
                Schedule Partnership Call
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                View Success Stories
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[
                { number: '200+', label: 'Landscaping Partners' },
                { number: '247%', label: 'Avg Lead Increase' },
                { number: '89%', label: 'Client Retention' },
                { number: '100%', label: 'Landscaping Focus' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-blue-200">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </Section>

      {/* Why Choose Us Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              The GroundUP Digital Advantage
            </h2>
            <p className="text-lg text-gray-600">
              Discover what makes us the preferred digital growth partner for landscaping business owners across the country.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {whyChooseUs.map((reason, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <reason.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{reason.title}</h3>
                  <p className="text-gray-600 mb-4">{reason.description}</p>
                  <ul className="space-y-2">
                    {reason.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Differentiators Section */}
      <Section background="gray" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              How We're Different from Other Agencies
            </h2>
            <p className="text-lg text-gray-600">
              While other agencies treat landscaping as just another vertical, we've built our entire company around serving landscaping business owners exclusively.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {differentiators.map((diff, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{diff.title}</h3>
                  <p className="text-gray-600 mb-4">{diff.description}</p>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-sm text-green-800 font-medium">
                      <strong>Your Advantage:</strong> {diff.advantage}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* Testimonials Section */}
      <Section background="white" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
              What Landscaping Business Owners Say
            </h2>
            <p className="text-lg text-gray-600">
              Hear directly from landscaping business owners who have transformed their companies with GroundUP Digital.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="h-full">
                <CardContent className="p-6">
                  <div className="mb-4">
                    <div className="flex items-center gap-1 mb-2">
                      {[...Array(5)].map((_, i) => (
                        <Award key={i} className="w-4 h-4 text-yellow-500 fill-current" />
                      ))}
                    </div>
                    <p className="text-gray-600 italic mb-4">"{testimonial.quote}"</p>
                  </div>
                  
                  <div className="border-t pt-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-semibold text-gray-900">{testimonial.name}</p>
                        <p className="text-sm text-gray-600">{testimonial.company}</p>
                        <p className="text-sm text-gray-500">{testimonial.location}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold text-green-600">{testimonial.results}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </Container>
      </Section>

      {/* CTA Section */}
      <Section background="gradient" padding="xl">
        <Container>
          <div className="max-w-3xl mx-auto text-center text-white">
            <h2 className="text-3xl sm:text-4xl font-bold mb-6">
              Ready to Partner with the Landscaping Digital Growth Experts?
            </h2>
            <p className="text-lg text-blue-100 mb-8">
              Join 200+ landscaping business owners who have chosen GroundUP Digital as their exclusive digital growth partner. 
              Let's discuss how we can transform your business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-50">
                <Phone className="w-5 h-5 mr-2" />
                Schedule Partnership Call
              </Button>
              <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10">
                <Link href="/client-success" className="flex items-center">
                  View Success Stories
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </Container>
      </Section>
    </>
  );
}
