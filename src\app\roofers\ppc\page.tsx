import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'PPC Advertising for Roofing Companies | Emergency Roof Repair Leads | GroundUPDigital',
  description: 'Drive immediate leads with targeted PPC campaigns for roofing companies. Get more customers for emergency roof repairs, replacements, and storm damage services.',
  keywords: 'roofing PPC, roof repair advertising, emergency roofing ads, Google Ads for roofers, roofing lead generation',
  openGraph: {
    title: 'PPC Advertising for Roofing Companies | Emergency Roof Repair Leads | GroundUPDigital',
    description: 'Drive immediate leads with targeted PPC campaigns for roofing companies. Get more customers for emergency roof repairs, replacements, and storm damage services.',
    type: 'website',
  },
};

export default function RoofingPPCPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              PPC Advertising for <span className="text-blue-600">Roofing Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Generate immediate leads and emergency calls with targeted PPC campaigns for roofing companies. 
              Get more customers for roof repairs, replacements, and storm damage services with proven Google Ads strategies.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                Get Free PPC Audit
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors">
                View Campaign Results
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Roofing PPC Results That Drive Emergency Calls
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '$4.20', label: 'Average Cost Per Lead', icon: '💰' },
                { metric: '650%', label: 'Return on Ad Spend', icon: '📈' },
                { metric: '22%', label: 'Average Conversion Rate', icon: '🎯' },
                { metric: '15min', label: 'Average Response Time', icon: '⏰' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Emergency PPC Strategy */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center text-white mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Emergency Roofing PPC Strategy
              </h2>
              <p className="text-xl text-blue-100">
                Capture high-intent emergency roofing leads when customers need help most.
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  strategy: 'Emergency Keywords',
                  description: 'Target urgent roofing searches with high-intent keywords and immediate response messaging.',
                  keywords: ['emergency roof repair', 'roof leak repair', '24/7 roofer', 'storm damage repair'],
                  icon: '🚨'
                },
                {
                  strategy: 'Weather-Triggered Campaigns',
                  description: 'Automatically increase bids and activate campaigns during severe weather events.',
                  keywords: ['storm damage', 'hail damage repair', 'wind damage roof', 'emergency tarping'],
                  icon: '⛈️'
                },
                {
                  strategy: 'Local Emergency Targeting',
                  description: 'Hyper-local targeting for immediate service area coverage during emergencies.',
                  keywords: ['roofer near me', 'local roof repair', 'emergency roofer [city]', 'roof repair [zip]'],
                  icon: '📍'
                }
              ].map((strategy, index) => (
                <div key={index} className="text-center text-white">
                  <div className="text-4xl mb-4">{strategy.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{strategy.strategy}</h3>
                  <p className="text-blue-100 mb-4">{strategy.description}</p>
                  <div className="bg-blue-700 rounded-lg p-4">
                    <h4 className="font-semibold mb-2">Key Keywords:</h4>
                    <ul className="space-y-1">
                      {strategy.keywords.map((keyword, keywordIndex) => (
                        <li key={keywordIndex} className="text-sm text-blue-100">
                          "{keyword}"
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Campaign Types */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Roofing PPC Campaign Types
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  campaign: 'Emergency Repair Campaigns',
                  description: 'Target urgent roof repair needs with immediate response messaging.',
                  keywords: ['emergency roof repair', 'roof leak repair', '24/7 roofer', 'urgent roof fix'],
                  bidStrategy: 'High bids for immediate visibility',
                  icon: '🚨'
                },
                {
                  campaign: 'Storm Damage Campaigns',
                  description: 'Weather-triggered campaigns for storm and hail damage repairs.',
                  keywords: ['storm damage repair', 'hail damage roof', 'wind damage repair', 'insurance claims'],
                  bidStrategy: 'Weather-based bid adjustments',
                  icon: '⛈️'
                },
                {
                  campaign: 'Roof Replacement Campaigns',
                  description: 'Target homeowners planning roof replacements and upgrades.',
                  keywords: ['roof replacement', 'new roof cost', 'roof installation', 'roofing contractor'],
                  bidStrategy: 'Moderate bids for qualified leads',
                  icon: '🏠'
                },
                {
                  campaign: 'Commercial Roofing Campaigns',
                  description: 'Target commercial property owners and facility managers.',
                  keywords: ['commercial roofing', 'flat roof repair', 'industrial roofing', 'warehouse roof'],
                  bidStrategy: 'High-value commercial targeting',
                  icon: '🏢'
                },
                {
                  campaign: 'Insurance Claim Campaigns',
                  description: 'Help customers navigate insurance claims for roof damage.',
                  keywords: ['insurance roof claim', 'roof damage assessment', 'insurance adjuster', 'claim assistance'],
                  bidStrategy: 'Educational content focus',
                  icon: '📋'
                },
                {
                  campaign: 'Preventive Maintenance Campaigns',
                  description: 'Target proactive homeowners interested in roof maintenance.',
                  keywords: ['roof inspection', 'roof maintenance', 'gutter cleaning', 'preventive care'],
                  bidStrategy: 'Lower cost, higher volume',
                  icon: '🔧'
                }
              ].map((campaign, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{campaign.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{campaign.campaign}</h3>
                  <p className="text-gray-600 mb-4">{campaign.description}</p>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-700 mb-2">Target Keywords:</h4>
                    <ul className="space-y-1">
                      {campaign.keywords.map((keyword, keywordIndex) => (
                        <li key={keywordIndex} className="text-sm text-gray-600">
                          "{keyword}"
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-3">
                    <h5 className="font-semibold text-blue-800 text-sm mb-1">Bid Strategy:</h5>
                    <p className="text-blue-700 text-xs">{campaign.bidStrategy}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Weather-Based Automation */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Weather-Based Campaign Automation
            </h2>
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  weather: 'Severe Storms',
                  automation: 'Increase bids by 200%, activate emergency campaigns',
                  keywords: ['storm damage', 'emergency repair', 'tarping service'],
                  response: 'Immediate activation',
                  icon: '⛈️'
                },
                {
                  weather: 'Hail Events',
                  automation: 'Launch hail damage campaigns, insurance claim messaging',
                  keywords: ['hail damage', 'insurance claims', 'roof inspection'],
                  response: 'Within 2 hours',
                  icon: '🧊'
                },
                {
                  weather: 'High Winds',
                  automation: 'Activate wind damage campaigns, emergency response ads',
                  keywords: ['wind damage', 'shingle repair', 'emergency service'],
                  response: 'Real-time activation',
                  icon: '💨'
                },
                {
                  weather: 'Heavy Snow',
                  automation: 'Focus on ice dam prevention and snow load concerns',
                  keywords: ['ice dam removal', 'snow load', 'winter roof damage'],
                  response: 'Seasonal activation',
                  icon: '❄️'
                }
              ].map((weather, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                  <div className="text-4xl mb-4">{weather.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{weather.weather}</h3>
                  <p className="text-gray-600 mb-4">{weather.automation}</p>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-700 mb-2">Focus Keywords:</h4>
                    <ul className="space-y-1">
                      {weather.keywords.map((keyword, keywordIndex) => (
                        <li key={keywordIndex} className="text-sm text-gray-600">
                          "{keyword}"
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-3">
                    <span className="text-sm font-semibold text-blue-800">Response Time: </span>
                    <span className="text-sm text-blue-700">{weather.response}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Landing Page Optimization */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              High-Converting Landing Pages for Roofers
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Emergency Landing Page Elements:</h3>
                <ul className="space-y-4">
                  {[
                    'Prominent emergency phone number with click-to-call',
                    'Clear "24/7 Emergency Service" messaging',
                    'Trust signals: licenses, insurance, BBB rating',
                    'Before/after photos of emergency repairs',
                    'Customer testimonials with emergency situations',
                    'Fast response time guarantees',
                    'Service area map and coverage information',
                    'Emergency contact form with immediate response'
                  ].map((element, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{element}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Conversion Optimization Results:</h3>
                <div className="space-y-6">
                  {[
                    { metric: '45%', description: 'Higher conversion rate with emergency-focused pages', icon: '📈' },
                    { metric: '80%', description: 'More phone calls from mobile users', icon: '📞' },
                    { metric: '65%', description: 'Increase in emergency service requests', icon: '🚨' },
                    { metric: '35%', description: 'Lower cost per emergency lead', icon: '💰' }
                  ].map((result, index) => (
                    <div key={index} className="flex items-center bg-white p-4 rounded-lg shadow-lg">
                      <div className="text-3xl mr-4">{result.icon}</div>
                      <div>
                        <div className="text-2xl font-bold text-blue-600">{result.metric}</div>
                        <div className="text-gray-600">{result.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Local Targeting */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Precision Local Targeting for Emergency Roofing
            </h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {[
                {
                  targeting: 'Geographic Targeting',
                  description: 'Target specific service areas with radius and location-based campaigns.',
                  features: ['City-level targeting', 'Zip code precision', 'Service radius targeting', 'Weather zone alignment']
                },
                {
                  targeting: 'Demographic Targeting',
                  description: 'Reach homeowners most likely to need emergency roofing services.',
                  features: ['Homeowner targeting', 'Property age demographics', 'Income-based targeting', 'Insurance coverage data']
                },
                {
                  targeting: 'Behavioral Targeting',
                  description: 'Target users based on their online behavior and search patterns.',
                  features: ['Home improvement interests', 'Insurance claim searches', 'Weather app users', 'Emergency service searches']
                }
              ].map((target, index) => (
                <div key={index} className="bg-white p-8 rounded-lg shadow-lg">
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{target.targeting}</h3>
                  <p className="text-gray-600 mb-6">{target.description}</p>
                  <ul className="space-y-3">
                    {target.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Roofing PPC Success Story
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    Elite Roofing: 750% ROI with Emergency PPC Campaigns
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Elite Roofing was missing emergency calls during storm season. Our weather-triggered PPC campaigns 
                    and emergency-focused landing pages transformed their lead generation.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">750%</div>
                      <div className="text-sm text-gray-600">Return on Ad Spend</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">$3.80</div>
                      <div className="text-sm text-gray-600">Cost Per Emergency Lead</div>
                    </div>
                  </div>
                  <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Campaign Results:</h4>
                  <ul className="space-y-3">
                    {[
                      '200+ emergency calls per month',
                      '22% average conversion rate',
                      '$125,000 additional revenue in 6 months',
                      '70% reduction in cost per acquisition',
                      '4.9-star average customer rating',
                      '50% increase in insurance claim jobs'
                    ].map((result, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Generate Immediate Roofing Leads?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Get a free PPC audit and discover how we can drive qualified emergency leads to your roofing business starting today.
            </p>
            <button className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Get Your Free Roofing PPC Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
