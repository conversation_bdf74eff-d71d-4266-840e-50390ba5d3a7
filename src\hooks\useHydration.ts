'use client';

import { useState, useEffect } from 'react';

/**
 * Custom hook to handle hydration mismatch issues
 * Returns true only after the component has mounted on the client
 */
export const useHydration = () => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  return isMounted;
};

/**
 * Custom hook for client-side only rendering
 * Useful for components that should only render on the client
 */
export const useClientOnly = () => {
  return useHydration();
};
