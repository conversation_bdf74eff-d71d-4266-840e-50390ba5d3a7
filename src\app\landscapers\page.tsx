import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Digital Marketing for Landscaping Companies | GroundUPDigital',
  description: 'Specialized digital marketing services for landscaping businesses. Increase leads, dominate local search, and grow your landscaping company with our proven strategies.',
  keywords: 'landscaping marketing, landscaping SEO, landscaping leads, lawn care marketing, landscape design marketing',
  openGraph: {
    title: 'Digital Marketing for Landscaping Companies | GroundUPDigital',
    description: 'Specialized digital marketing services for landscaping businesses. Increase leads, dominate local search, and grow your landscaping company with our proven strategies.',
    type: 'website',
  },
};

export default function LandscapersPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 via-white to-blue-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Digital Marketing for <span className="text-green-600">Landscaping Companies</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Grow your landscaping business with our specialized digital marketing services. From lawn care to 
              landscape design, we help you attract more customers and increase revenue year-round.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-green-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                Get Free Marketing Audit
              </button>
              <button className="border-2 border-green-600 text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition-colors">
                View Success Stories
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Proven Results for Landscaping Businesses
            </h2>
            <div className="grid md:grid-cols-4 gap-8 mb-16">
              {[
                { metric: '350%', label: 'Average Lead Increase', icon: '📈' },
                { metric: '$85K', label: 'Additional Annual Revenue', icon: '💰' },
                { metric: '#1', label: 'Local Search Rankings', icon: '🏆' },
                { metric: '4.8★', label: 'Average Review Rating', icon: '⭐' }
              ].map((stat, index) => (
                <div key={index} className="text-center bg-white p-8 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{stat.icon}</div>
                  <div className="text-4xl font-bold text-green-600 mb-2">{stat.metric}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Specialized Services for Landscapers
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: 'Landscaping SEO',
                  description: 'Dominate local search results for landscaping services in your area.',
                  features: ['Local keyword optimization', 'Google My Business optimization', 'Seasonal content strategy', 'Competitor analysis'],
                  link: '/landscapers/seo'
                },
                {
                  title: 'Landscaping Web Design',
                  description: 'Beautiful, conversion-focused websites that showcase your landscaping work.',
                  features: ['Portfolio galleries', 'Service area mapping', 'Quote request forms', 'Mobile optimization'],
                  link: '/landscapers/web-design'
                },
                {
                  title: 'Landscaping PPC',
                  description: 'Targeted advertising campaigns that generate immediate landscaping leads.',
                  features: ['Google Ads management', 'Seasonal campaigns', 'Local targeting', 'Lead tracking'],
                  link: '/landscapers/ppc'
                },
                {
                  title: 'Landscaping CRM',
                  description: 'Custom CRM solutions designed specifically for landscaping businesses.',
                  features: ['Customer management', 'Project tracking', 'Seasonal scheduling', 'Inventory management'],
                  link: '/landscapers/crm-solution'
                },
                {
                  title: 'Custom Landscaping Software',
                  description: 'Tailored software solutions to streamline your landscaping operations.',
                  features: ['Design software integration', 'Estimate generation', 'Project management', 'Client portals'],
                  link: '/landscapers/custom-software'
                },
                {
                  title: 'IoT for Landscapers',
                  description: 'Smart technology solutions for modern landscaping businesses.',
                  features: ['Irrigation monitoring', 'Weather integration', 'Equipment tracking', 'Automated reporting'],
                  link: '/landscapers/iot-solutions'
                }
              ].map((service, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-2 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <span className="w-1.5 h-1.5 bg-green-600 rounded-full mr-3"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <a 
                    href={service.link}
                    className="inline-flex items-center text-green-600 font-semibold hover:text-green-700"
                  >
                    Learn More →
                  </a>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Landscaping Challenges */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Common Landscaping Business Challenges We Solve
            </h2>
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Challenges:</h3>
                <ul className="space-y-4">
                  {[
                    'Seasonal demand fluctuations',
                    'High competition in local markets',
                    'Difficulty showcasing visual work online',
                    'Weather-dependent scheduling issues',
                    'Pricing transparency concerns',
                    'Customer education about services'
                  ].map((challenge, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-red-500 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{challenge}</span>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Our Solutions:</h3>
                <ul className="space-y-4">
                  {[
                    'Year-round marketing strategies with seasonal optimization',
                    'Local SEO dominance and competitive differentiation',
                    'Visual portfolio websites with before/after galleries',
                    'Automated scheduling and weather-responsive campaigns',
                    'Transparent pricing tools and quote calculators',
                    'Educational content marketing and service explanations'
                  ].map((solution, index) => (
                    <li key={index} className="flex items-start">
                      <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-4 flex-shrink-0"></span>
                      <span className="text-gray-600">{solution}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Types */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Landscaping Services We Market
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { service: 'Lawn Care & Maintenance', icon: '🌱' },
                { service: 'Landscape Design', icon: '🎨' },
                { service: 'Hardscaping', icon: '🪨' },
                { service: 'Tree Services', icon: '🌳' },
                { service: 'Irrigation Systems', icon: '💧' },
                { service: 'Seasonal Cleanup', icon: '🍂' },
                { service: 'Garden Installation', icon: '🌺' },
                { service: 'Outdoor Lighting', icon: '💡' }
              ].map((type, index) => (
                <div key={index} className="text-center bg-white p-6 rounded-lg shadow-lg">
                  <div className="text-4xl mb-4">{type.icon}</div>
                  <h3 className="font-semibold text-gray-900">{type.service}</h3>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Case Study Preview */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Success Story: GreenScape Landscaping
            </h2>
            <div className="bg-white rounded-lg shadow-xl p-8 lg:p-12">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    From 3 Leads to 50+ Leads Per Month
                  </h3>
                  <p className="text-gray-600 mb-6">
                    GreenScape Landscaping was struggling with inconsistent lead flow and seasonal revenue dips. 
                    Our comprehensive digital marketing strategy transformed their business.
                  </p>
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">350%</div>
                      <div className="text-sm text-gray-600">Lead Increase</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">$125K</div>
                      <div className="text-sm text-gray-600">Additional Revenue</div>
                    </div>
                  </div>
                  <button className="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                    Read Full Case Study
                  </button>
                </div>
                <div className="bg-gray-100 rounded-lg p-8 text-center">
                  <div className="text-6xl mb-4">📈</div>
                  <p className="text-gray-600 italic">
                    "GroundUPDigital completely transformed our business. We went from struggling to find customers 
                    to having a waiting list. Their landscaping expertise really shows."
                  </p>
                  <div className="mt-4 font-semibold text-gray-900">
                    - Mike Rodriguez, Owner
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-green-600">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Grow Your Landscaping Business?
            </h2>
            <p className="text-xl text-green-100 mb-8">
              Get a free marketing audit and discover how we can help you attract more customers and increase revenue.
            </p>
            <button className="bg-white text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              Get Your Free Landscaping Marketing Audit
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
